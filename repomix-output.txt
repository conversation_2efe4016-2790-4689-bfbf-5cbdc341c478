This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

================================================================
File Summary
================================================================

Purpose:
--------
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: src/**/*.ts, *.ts, *.tsx, src/**/*.tsx, **/*.md
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded

Additional Info:
----------------

================================================================
Directory Structure
================================================================
src/
  app/
    agents/
      [id]/
        edit/
          page.tsx
        EditAgentClientWrapper.tsx
        page.tsx
      new/
        NewAgentClientWrapper.tsx
        page.tsx
      AgentsClientWrapper.tsx
      page.tsx
    api/
      auth/
        [...nextauth]/
          route.ts
        check/
          route.ts
        register/
          route.ts
      emails/
        [id]/
          route.ts
        route.ts
      leads/
        [id]/
          details/
            route.ts
          history/
            route.ts
          route.ts
        route.ts
      prompts/
        [id]/
          route.ts
        route.ts
      salesforce/
        test/
          route.ts
      test/
        email/
          generate/
            route.ts
          send/
            route.ts
        leads/
          history/
            route.ts
          route.ts
      trigger/
        route.ts
      v1/
        agents/
          [id]/
            route.ts
          route.ts
        generate/
          route.ts
        prompts/
          [id]/
            route.ts
          test/
            route.ts
          route.ts
        salesforce/
          auth/
            callback/
              route.ts
            route.ts
        settings/
          email/
            [id]/
              route.ts
            route.ts
          profile/
            route.ts
    emails/
      page.tsx
    leads/
      [id]/
        page.tsx
      page.tsx
    login/
      form.tsx
      page.tsx
    prompts/
      test/
        page.tsx
      page.tsx
    register/
      form.tsx
      page.tsx
    settings/
      page.tsx
    test-functions/
      page.tsx
    layout.tsx
    page.tsx
  components/
    agents/
      AgentForm.tsx
      AgentSelect.tsx
      AgentsList.tsx
      AgentsManager.tsx
    auth/
      sessionProvider.tsx
    emails/
      EmailDetailsModal.tsx
    leads/
      LeadCommunicationsHistory.tsx
      LeadForm.tsx
      LeadsTable.tsx
      SendEmailModal.tsx
    prompts/
      LeadSelector.tsx
      PromptEditor.tsx
      PromptsManager.tsx
      PromptTester.tsx
      PromptVariableAnalyzer.tsx
      PromptVariableHighlighter.tsx
    ui/
      alert.tsx
      badge.tsx
      button.tsx
      card.tsx
      checkbox.tsx
      command.tsx
      dialog.tsx
      dropdown-menu.tsx
      form.tsx
      input.tsx
      label.tsx
      loader.tsx
      navigation-menu.tsx
      pagination.tsx
      popover.tsx
      scroll-area.tsx
      select.tsx
      separator.tsx
      skeleton.tsx
      status-badge.tsx
      switch.tsx
      table.tsx
      tabs.tsx
      textarea.tsx
      toast.tsx
      toaster.tsx
      use-toast.ts
    navigation.tsx
  db/
    schema/
      agents.ts
      email-configs.ts
      email-logs.ts
      emails.ts
      leads.ts
      prompts.ts
      users.ts
    create-email-configs.ts
    index.ts
    migrate.ts
    schema.ts
  hooks/
    use-lead-details.ts
    use-leads.ts
    use-prompts.ts
    use-toast.ts
    useToast.ts
  lib/
    auth.ts
    utils.ts
  providers/
    auth-provider.tsx
    providers.tsx
    query-provider.tsx
  services/
    anthropic.ts
    email.ts
    leads.ts
    openai.ts
    prompts.ts
    promptsManager.ts
    salesforce.ts
  trigger/
    example.ts
  types/
    agent.ts
    index.ts
    jsforce.d.ts
    next-auth.d.ts
  utils/
    pkce.ts
  middleware.ts
drizzle.config.ts
README.md
trigger.config.ts

================================================================
Files
================================================================

================
File: src/app/agents/[id]/edit/page.tsx
================
'use client';

import { useParams, useRouter } from 'next/navigation';
import { CreateAgentInput } from '@/types/agent';
import { AgentForm } from '@/components/agents/AgentForm';
import { useToast } from '@/hooks/useToast';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

async function fetchAgent(id: string) {
  const response = await fetch(`/api/v1/agents/${id}`);
  if (!response.ok) {
    throw new Error('Failed to fetch agent');
  }
  return response.json();
}

async function updateAgent(id: string, data: CreateAgentInput) {
  const response = await fetch(`/api/v1/agents/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: 'Failed to update agent' }));
    throw new Error(error.error || 'Failed to update agent');
  }

  return response.json();
}

export default function EditAgentPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: agent, isLoading: isLoadingAgent } = useQuery({
    queryKey: ['agent', params.id],
    queryFn: () => fetchAgent(params.id as string),
    enabled: !!params.id,
    staleTime: 30000, // Consider data fresh for 30 seconds
  });

  const updateMutation = useMutation({
    mutationFn: (data: CreateAgentInput) => 
      updateAgent(params.id as string, data),
    onSuccess: (data) => {
      toast({
        title: 'Success',
        description: `Agent "${data.name}" has been updated successfully`,
        variant: 'default',
      });
      queryClient.invalidateQueries({ queryKey: ['agents'] });
      router.push('/agents');
    },
    onError: (error: Error) => {
      console.error('Error updating agent:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to update agent. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = async (data: CreateAgentInput) => {
    updateMutation.mutate(data);
  };

  const handleBack = () => {
    router.push('/agents');
  };

  if (isLoadingAgent) {
    return (
      <div className="py-6">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
          <div className="text-center py-8">
            <p className="text-gray-500">Loading agent...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!agent) {
    return (
      <div className="py-6">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
          <div className="text-center py-8">
            <p className="text-gray-500">Agent not found</p>
            <Button
              onClick={handleBack}
              variant="outline"
              className="mt-4"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Agents
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="py-6">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button
              onClick={handleBack}
              variant="ghost"
              className="mr-4"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <h1 className="text-2xl font-semibold text-gray-900">
              Edit Agent: {agent.name}
            </h1>
          </div>
        </div>
        <AgentForm 
          initialData={agent} 
          onSubmit={handleSubmit}
          isLoading={updateMutation.isPending}
        />
      </div>
    </div>
  );
}

================
File: src/app/agents/[id]/EditAgentClientWrapper.tsx
================
'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState, useCallback } from 'react';
import { AgentForm } from '@/components/agents/AgentForm';
import { Agent, CreateAgentInput } from '@/types/agent';
import { useToast } from '@/hooks/useToast';

interface EditAgentClientWrapperProps {
  params: {
    id: string;
  };
}

export default function EditAgentClientWrapper({ params }: EditAgentClientWrapperProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [agent, setAgent] = useState<Agent | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchAgent = useCallback(async () => {
    try {
      const response = await fetch(`/api/v1/agents/${params.id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch agent');
      }
      const data = await response.json();
      setAgent(data);
    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch agent',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [params.id, toast]);

  useEffect(() => {
    fetchAgent();
  }, [fetchAgent]);

  const handleUpdate = async (data: CreateAgentInput) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/v1/agents/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to update agent');
      }

      await fetchAgent();
      toast({
        title: 'Success',
        description: 'Agent updated successfully',
      });
    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to update agent',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/v1/agents/${params.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete agent');
      }

      router.push('/agents');
      toast({
        title: 'Success',
        description: 'Agent deleted successfully',
      });
    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to delete agent',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!agent) {
    return <div>Agent not found</div>;
  }

  return <AgentForm onSubmit={handleUpdate} initialData={agent} onDelete={handleDelete} />;
}

================
File: src/app/agents/[id]/page.tsx
================
import EditAgentClientWrapper from './EditAgentClientWrapper';

// interface EditAgentPageProps {
//   params: {
//     id: string;
//   };
// }

export default function EditAgentPage({ params }: { params: { id: string } }) {
  return (
    <div className="py-6">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
        <h1 className="text-2xl font-semibold text-gray-900">Edit Agent</h1>
        <div className="mt-8">
          <EditAgentClientWrapper params={params} />
        </div>
      </div>
    </div>
  );
}

================
File: src/app/agents/new/NewAgentClientWrapper.tsx
================
'use client';

import { useRouter } from 'next/navigation';
import { AgentForm } from '@/components/agents/AgentForm';
import { CreateAgentInput } from '@/types/agent';
import { useToast } from '@/hooks/useToast';

export default function NewAgentClientWrapper() {
  const router = useRouter();
  const { toast } = useToast();

  const handleSubmit = async (data: CreateAgentInput) => {
    try {
      const response = await fetch('/api/v1/agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) throw new Error('Failed to create agent');

      toast({
        title: 'Success',
        description: 'Agent created successfully',
      });
      router.push('/agents');
    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to create agent',
        variant: 'destructive',
      });
    }
  };

  return <AgentForm onSubmit={handleSubmit} />;
}

================
File: src/app/agents/new/page.tsx
================
import NewAgentClientWrapper from './NewAgentClientWrapper';

export default function NewAgentPage() {
  return (
    <div className="py-6">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
        <h1 className="text-2xl font-semibold text-gray-900">Create New Agent</h1>
        <div className="mt-8">
          <NewAgentClientWrapper />
        </div>
      </div>
    </div>
  );
}

================
File: src/app/agents/AgentsClientWrapper.tsx
================
'use client';

import { useRouter } from 'next/navigation';
import { Agent } from '@/types/agent';
import { AgentsList } from '@/components/agents/AgentsList';
import { useToast } from '@/hooks/useToast';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

async function fetchAgents() {
  const response = await fetch('/api/v1/agents');
  if (!response.ok) {
    throw new Error('Failed to fetch agents');
  }
  return response.json();
}

export default function AgentsClientWrapper() {
  const router = useRouter();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data, isLoading } = useQuery({
    queryKey: ['agents'],
    queryFn: fetchAgents,
    staleTime: 30000, // Consider data fresh for 30 seconds
  });

  const deleteMutation = useMutation({
    mutationFn: async (agent: Agent) => {
      const response = await fetch(`/api/v1/agents/${agent.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete agent');
      }

      return response.json();
    },
    onSuccess: (_, agent) => {
      toast({
        title: 'Success',
        description: `Agent "${agent.name}" has been deleted successfully`,
        variant: 'default',
      });
      queryClient.invalidateQueries({ queryKey: ['agents'] });
    },
    onError: (error) => {
      console.error('Error deleting agent:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete agent. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const handleEdit = (agent: Agent) => {
    router.push(`/agents/${agent.id}/edit`);
  };

  const handleDelete = (agent: Agent) => {
    if (window.confirm(`Are you sure you want to delete agent "${agent.name}"?`)) {
      deleteMutation.mutate(agent);
    }
  };

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Loading agents...</p>
      </div>
    );
  }

  const agents = data?.agents || [];

  if (agents.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">No agents found</h3>
        <p className="mt-1 text-sm text-gray-500">Get started by creating a new agent.</p>
      </div>
    );
  }

  return (
    <AgentsList
      agents={agents}
      onEdit={handleEdit}
      onDelete={handleDelete}
      isLoading={deleteMutation.isPending}
    />
  );
}

================
File: src/app/agents/page.tsx
================
import Link from 'next/link';
import AgentsClientWrapper from './AgentsClientWrapper';

export default async function AgentsPage() {
  return (
    <div className="py-6">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold text-gray-900">My Agents</h1>
          <Link
            href="/agents/new"
            className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
          >
            Create New Agent
          </Link>
        </div>
        <div className="mt-8">
          <AgentsClientWrapper />
        </div>
      </div>
    </div>
  );
}

================
File: src/app/api/auth/[...nextauth]/route.ts
================
import { authOptions } from "@/lib/auth";
import NextAuth from "next-auth";

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };

================
File: src/app/api/auth/check/route.ts
================
import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { jwtVerify } from "jose";

export async function GET() {
    const cookieStore = cookies();
    const jwtToken = cookieStore.get("token");
    const sfSession = cookieStore.get("sf_session");

    try {
        if (jwtToken) {
            // Verify JWT token
            const secret = new TextEncoder().encode(
                process.env.JWT_SECRET || "your-default-secret-key-change-this"
            );
            await jwtVerify(jwtToken.value, secret);
            return NextResponse.json({ authenticated: true });
        }

        // Check Salesforce session
        if (sfSession) {
            return NextResponse.json({ authenticated: true });
        }

        // No valid authentication found
        return NextResponse.json({ authenticated: false });
    } catch (_error) {
        // JWT verification failed
        return NextResponse.json({ authenticated: false });
    }
}

================
File: src/app/api/auth/register/route.ts
================
import { NextRequest, NextResponse } from "next/server";
import { hash } from "bcryptjs";
import { db } from "@/db";
import { users } from "@/db/schema";
import { z } from "zod";

const registerSchema = z.object({
    email: z.string().email(),
    password: z.string().min(6),
    name: z.string().min(2),
});

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        const { email, password, name } = registerSchema.parse(body);

        // Проверяем, не существует ли уже пользователь
        const existingUser = await db.query.users.findFirst({
            where: (users, { eq }) => eq(users.email, email),
        });

        if (existingUser) {
            return NextResponse.json(
                { message: "User already exists" },
                { status: 400 }
            );
        }

        const hashedPassword = await hash(password, 10);
        await db.insert(users).values({
            email,
            password: hashedPassword,
            name,
        });

        return NextResponse.json(
            { message: "User created successfully" },
            { status: 201 }
        );
    } catch (error) {
        console.error("Registration error:", error);
        if (error instanceof z.ZodError) {
            return NextResponse.json(
                { message: "Invalid input", errors: error.errors },
                { status: 400 }
            );
        }
        return NextResponse.json(
            { message: "Internal server error" },
            { status: 500 }
        );
    }
}

================
File: src/app/api/emails/[id]/route.ts
================
import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { emails } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const emailId = parseInt(params.id, 10);
    if (!emailId) {
      return NextResponse.json(
        { error: 'Email ID is required' },
        { status: 400 }
      );
    }

    const email = await db.query.emails.findFirst({
      where: eq(emails.id, emailId),
    });


    if (!email) {
      return NextResponse.json(
        { error: 'Email not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(email);
  } catch (error) {
    console.error('Error fetching email:', error);
    return NextResponse.json(
      { error: 'Failed to fetch email' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { subject, content } = await request.json();
    const emailId = parseInt(params.id, 10);

    if (!emailId) {
      return NextResponse.json(
        { error: 'Email ID is required' },
        { status: 400 }
      );
    }

    // Update email in database
    const [updatedEmail] = await db
      .update(emails)
      .set({
        subject,
        content,
      })
      .where(eq(emails.id, emailId))
      .returning();

    if (!updatedEmail) {
      return NextResponse.json(
        { error: 'Email not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedEmail);
  } catch (error) {
    console.error('Error updating email:', error);
    return NextResponse.json(
      { error: 'Failed to update email' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const emailId = parseInt(params.id);
    if (isNaN(emailId)) {
      return NextResponse.json(
        { error: "Invalid email ID" },
        { status: 400 }
      );
    }

    // Удаляем письмо из базы данных
    await db.delete(emails)
      .where(eq(emails.id, emailId));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting email:", error);
    return NextResponse.json(
      { error: "Failed to delete email" },
      { status: 500 }
    );
  }
}

================
File: src/app/api/emails/route.ts
================
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { db } from '@/db';
import { leads, emails } from '@/db/schema';
import { emailGenerator } from '@/services/openai';
import { eq, desc, and } from 'drizzle-orm';
import { auth } from '@/lib/auth';

export async function POST(request: NextRequest) {
    try {
        const session = await auth();
        
        if (!session?.user?.email) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const { leadId } = await request.json();

        // Get lead details using id
        const lead = await db
            .select()
            .from(leads)
            .where(eq(leads.id, leadId))
            .limit(1);

        if (!lead[0]) {
            return NextResponse.json({ error: 'Lead not found' }, { status: 404 });
        }

        // Generate email using OpenAI
        const { subject, content, prompt } = await emailGenerator.generateEmail({
            firstName: lead[0].firstName,
            lastName: lead[0].lastName,
            company: lead[0].company ?? 'Unknown Company',
            title: lead[0].title,
            status: lead[0].status,
            salesforceId: lead[0].salesforceId ?? '',
            email: lead[0].email
        });

        // Save email to database
        const [savedEmail] = await db
            .insert(emails)
            .values({
                leadId: lead[0].id,
                subject,
                content,
                prompt,
                sent: false,
                createdBy: `${session.user.id}`,
            })
            .returning();

        return NextResponse.json(savedEmail);
    } catch (error) {
        console.error('Error generating email:', error);
        return NextResponse.json(
            { error: 'Failed to generate email' },
            { status: 500 }
        );
    }
}

export async function GET(request: NextRequest) {
    try {
        const session = await auth();
        console.log('session email', session)

        if (!session?.user) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const searchParams = request.nextUrl.searchParams;
        const leadId = searchParams.get('leadId');

        const baseQuery = db
            .select({
                id: emails.id,
                subject: emails.subject,
                content: emails.content,
                prompt: emails.prompt,
                leadId: emails.leadId,
                sent: emails.sent,
                sentAt: emails.sentAt,
                createdAt: emails.createdAt,
                createdBy: emails.createdBy,
                leadFirstName: leads.firstName,
                leadLastName: leads.lastName,
                leadCompany: leads.company,
            })
            .from(emails)
            .leftJoin(leads, eq(emails.leadId, leads.id))
            .where(
                leadId 
                    ? and(
                        eq(emails.createdBy, `${session.user.id}`),
                        eq(emails.leadId, parseInt(leadId, 10))
                      )
                    : eq(emails.createdBy, `${session.user.id}`)
            )
            .orderBy(desc(emails.createdAt));

        const allEmails = await baseQuery;
        return NextResponse.json(allEmails);
    } catch (error) {
        console.error('Error fetching emails:', error);
        return NextResponse.json(
            { error: 'Failed to fetch emails' },
            { status: 500 }
        );
    }
}

================
File: src/app/api/leads/[id]/details/route.ts
================
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { db } from "@/db";
import { leads } from "@/db/schema/leads";
import { eq } from "drizzle-orm";
import { authOptions } from "@/lib/auth";

export async function GET(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.accessToken) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Получаем данные лида из нашей БД
        const [lead] = await db
            .select()
            .from(leads)
            .where(eq(leads.id, parseInt(params.id)))
            .limit(1);

        if (!lead) {
            return NextResponse.json({ error: "Lead not found" }, { status: 404 });
        }

        if (!lead.salesforceId) {
            return NextResponse.json({
                lead,
                history: [],
                activities: [],
                opportunities: []
            });
        }

        // Получаем данные из Salesforce
        const sfResponse = await fetch(
            `${session.instanceUrl}/services/data/v59.0/sobjects/Lead/${lead.salesforceId}`,
            {
                headers: {
                    Authorization: `Bearer ${session.accessToken}`,
                },
            }
        );

        if (!sfResponse.ok) {
            console.error("Salesforce API error:", await sfResponse.text());
            // Если ошибка в Salesforce, возвращаем хотя бы локальные данные
            return NextResponse.json({
                lead,
                history: [],
                activities: [],
                opportunities: []
            });
        }

        const sfLead = await sfResponse.json();

        // Получаем историю из Salesforce
        const historyResponse = await fetch(
            `${session.instanceUrl}/services/data/v59.0/query/?q=SELECT+Field,OldValue,NewValue,CreatedDate+FROM+LeadHistory+WHERE+LeadId='${lead.salesforceId}'`,
            {
                headers: {
                    Authorization: `Bearer ${session.accessToken}`,
                },
            }
        );

        let history = [];
        if (historyResponse.ok) {
            const historyData = await historyResponse.json();
            history = historyData.records?.map((record: any) => ({
                type: record.Field,
                date: record.CreatedDate,
                description: `Changed from ${record.OldValue || 'empty'} to ${record.NewValue || 'empty'}`,
            })) || [];
        } else {
            const error = await historyResponse.text();
            console.error("Failed to fetch history:", error);
        }

        // Получаем активности из Salesforce
        const activitiesResponse = await fetch(
            `${session.instanceUrl}/services/data/v59.0/query/?q=SELECT+Id,Subject,ActivityDate,Description,Type+FROM+Task+WHERE+WhoId='${lead.salesforceId}'+ORDER+BY+ActivityDate+DESC+LIMIT+1000`,
            {
                headers: {
                    Authorization: `Bearer ${session.accessToken}`,
                },
            }
        );

        let activities = [];
        if (activitiesResponse.ok) {
            const activitiesData = await activitiesResponse.json();
            activities = activitiesData.records?.map((record: any) => ({
                id: record.Id,
                type: record.Type || 'Task',
                subject: record.Subject,
                date: record.ActivityDate,
                description: record.Description,
            })) || [];
        } else {
            const error = await activitiesResponse.text();
            console.error("Failed to fetch activities:", error);
        }

        // Получаем связанные возможности
        const opportunitiesResponse = await fetch(
            `${session.instanceUrl}/services/data/v59.0/query/?q=SELECT+Id,Name,StageName,Amount,CloseDate+FROM+Opportunity+WHERE+LeadId='${lead.salesforceId}'+ORDER+BY+CloseDate+DESC+LIMIT+1000`,
            {
                headers: {
                    Authorization: `Bearer ${session.accessToken}`,
                },
            }
        );

        let opportunities = [];
        if (opportunitiesResponse.ok) {
            const opportunitiesData = await opportunitiesResponse.json();
            opportunities = opportunitiesData.records?.map((record: any) => ({
                id: record.Id,
                name: record.Name,
                stage: record.StageName,
                amount: record.Amount,
                closeDate: record.CloseDate,
            })) || [];
        } else {
            const error = await opportunitiesResponse.text();
            console.error("Failed to fetch opportunities:", error);
        }

        // Объединяем данные из локальной БД и Salesforce
        const enrichedLead = {
            ...lead,
            ...sfLead,
        };

        return NextResponse.json({
            lead: enrichedLead,
            history,
            activities,
            opportunities,
        });
    } catch (error) {
        console.error("Error fetching lead details:", error);
        return NextResponse.json(
            { error: "Failed to fetch lead details" },
            { status: 500 }
        );
    }
}

================
File: src/app/api/leads/[id]/history/route.ts
================
import { salesforce } from "@/services/salesforce";
import { NextResponse } from "next/server";

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const history = await salesforce.getLeadComprehensiveHistory(params.id);
    return NextResponse.json(history);
  } catch (error) {
    console.error("Error fetching lead history:", error);
    return NextResponse.json(
      { error: "Failed to fetch lead history" },
      { status: 500 }
    );
  }
}

================
File: src/app/api/leads/[id]/route.ts
================
import { NextResponse } from "next/server";
import { db } from "@/db";
import { eq } from "drizzle-orm";
import { getServerSession } from "next-auth";
import { salesforce } from "@/services/salesforce";
import { leads } from "@/db/schema";
import { authOptions } from "@/lib/auth";

async function getUserId(session: any) {
    // If session has Salesforce tokens, use Salesforce authentication
    if (session.accessToken && session.instanceUrl) {
        return await salesforce.getCurrentUserId();
    }
    // Otherwise use email authentication user id
    return session.user.id;
}

export async function GET(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json(
                { error: "Not authenticated" },
                { status: 401 }
            );
        }

        const currentUserId = await getUserId(session);
        const lead = await db
            .select()
            .from(leads)
            .where(eq(leads.id, parseInt(params.id)))
            .limit(1);

        if (!lead || lead.length === 0) {
            return NextResponse.json({ error: "Lead not found" }, { status: 404 });
        }

        if (lead[0].createdBy !== currentUserId) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
        }

        return NextResponse.json(lead[0]);
    } catch (error) {
        console.error("Error fetching lead:", error);
        return NextResponse.json(
            { error: "Failed to fetch lead" },
            { status: 500 }
        );
    }
}

export async function PUT(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json(
                { error: "Not authenticated" },
                { status: 401 }
            );
        }

        const currentUserId = await getUserId(session);
        const body = await request.json();

        // Generate name from firstName and lastName
        const name = body.name || [body.firstName, body.lastName]
            .filter(Boolean)
            .join(' ') || 'Unknown';

        const existingLead = await db
            .select()
            .from(leads)
            .where(eq(leads.id, parseInt(params.id)))
            .limit(1);

        if (!existingLead || existingLead.length === 0) {
            return NextResponse.json({ error: "Lead not found" }, { status: 404 });
        }

        if (existingLead[0].createdBy !== currentUserId) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
        }

        // Prepare update data
        const updateData = {
            firstName: body.firstName || undefined,
            lastName: body.lastName || undefined,
            name: name,
            email: body.email || undefined,
            company: body.company || 'Unknown Company',
            title: body.title || undefined,
            phone: body.phone || undefined,
            status: body.status || undefined,
            industry: body.industry || undefined,
            rating: body.rating || undefined,
            leadSource: body.leadSource || undefined,
            description: body.description || undefined,
            website: body.website || undefined,
            numberOfEmployees: body.numberOfEmployees || undefined,
            updatedAt: new Date(),
        };

        const [updatedLead] = await db
            .update(leads)
            .set(updateData)
            .where(eq(leads.id, parseInt(params.id)))
            .returning();

        return NextResponse.json(updatedLead);
    } catch (error) {
        console.error("Error updating lead:", error);
        return NextResponse.json(
            { error: "Failed to update lead" },
            { status: 500 }
        );
    }
}

export async function DELETE(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json(
                { error: "Not authenticated" },
                { status: 401 }
            );
        }

        const currentUserId = await getUserId(session);
        const existingLead = await db
            .select()
            .from(leads)
            .where(eq(leads.id, parseInt(params.id)))
            .limit(1);

        if (!existingLead || existingLead.length === 0) {
            return NextResponse.json({ error: "Lead not found" }, { status: 404 });
        }

        if (existingLead[0].createdBy !== currentUserId) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
        }

        await db
            .delete(leads)
            .where(eq(leads.id, parseInt(params.id)));

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error("Error deleting lead:", error);
        return NextResponse.json(
            { error: "Failed to delete lead" },
            { status: 500 }
        );
    }
}

================
File: src/app/api/leads/route.ts
================
import { NextResponse, NextRequest } from "next/server";
import { salesforce } from "@/services/salesforce";
import { db } from "@/db";
import { eq, desc, asc, and, or, ilike } from "drizzle-orm";
import { getServerSession } from "next-auth";
import { sql } from "drizzle-orm";
import { leads, users } from "@/db/schema";
import { authOptions } from "@/lib/auth";

// Определяем допустимые поля для сортировки
const validSortFields = ['firstName', 'lastName', 'email', 'company', 'status', 'createdAt'] as const;
type SortField = typeof validSortFields[number];

const isSortField = (field: string): field is SortField => {
    return validSortFields.includes(field as SortField);
};

export async function GET(request: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json(
                {
                    error: "Not authenticated",
                    redirectTo: "/login",
                },
                { status: 401 }
            );
        }

        let currentUserId: string
        if (session.user?.email) {
            const userWithEmailId = await db.select({ id: users.id }).from(users)
                .where(eq(users.email, session.user?.email))
                .limit(1)
            currentUserId = userWithEmailId[0].id as any
        } else {
            currentUserId = await salesforce.getCurrentUserId();
        }

        // Get query parameters
        const url = new URL(request.url);
        const page = url.searchParams.get("page");
        const limit = url.searchParams.get("limit");
        const sortBy = url.searchParams.get("sortBy") || "createdAt";
        const sortOrder = (url.searchParams.get("sortOrder") || "desc") as "asc" | "desc";
        const search = url.searchParams.get("search");
        const ids = url.searchParams.get("ids");

        // Validate sort field
        if (!isSortField(sortBy)) {
            return NextResponse.json(
                { error: "Invalid sort field" },
                { status: 400 }
            );
        }

        // Base conditions
        let conditions = [eq(leads.createdBy, currentUserId)];

        // Add search condition if search parameter is present
        if (search) {
            conditions.push(
                or(
                    ilike(leads.firstName, `%${search}%`),
                    ilike(leads.lastName, `%${search}%`),
                    ilike(leads.email, `%${search}%`),
                    ilike(leads.company, `%${search}%`),
                )!
            );
        }

        // Add IDs filter if ids parameter is present
        if (ids) {
            const idArray = ids.split(",").map(id => parseInt(id)).filter(id => !isNaN(id));
            if (idArray.length > 0) {
                conditions.push(sql`${leads.id} IN (${sql.join(idArray, sql`, `)})`);
            }
        }

        // If we're fetching by IDs, skip pagination
        if (ids) {
            const filteredLeads = await db
                .select()
                .from(leads)
                .where(and(...conditions))
                .orderBy(sortOrder === "desc" ? desc(leads[sortBy]) : asc(leads[sortBy]));

            return NextResponse.json({
                leads: filteredLeads,
                pagination: {
                    total: filteredLeads.length,
                    page: 1,
                    limit: filteredLeads.length,
                    totalPages: 1
                }
            });
        }

        // Get total count for pagination (only for non-ID requests)
        const [{ count }] = await db
            .select({
                count: sql<number>`cast(count(*) as integer)`,
            })
            .from(leads)
            .where(and(...conditions));

        // Calculate pagination parameters
        const pageNum = parseInt(page || "1");
        const limitNum = parseInt(limit || "10");
        const offset = (pageNum - 1) * limitNum;

        // Get paginated and filtered leads
        const filteredLeads = await db
            .select()
            .from(leads)
            .where(and(...conditions))
            .orderBy(sortOrder === "desc" ? desc(leads[sortBy]) : asc(leads[sortBy]))
            .limit(limitNum)
            .offset(offset);

        // Return leads with pagination metadata
        return NextResponse.json({
            leads: filteredLeads,
            pagination: {
                total: count,
                page: pageNum,
                limit: limitNum,
                totalPages: Math.ceil(count / limitNum)
            }
        });
    } catch (error: any) {
        console.error("Error fetching leads:", error);
        return NextResponse.json(
            { error: "Failed to fetch leads" },
            { status: 500 }
        );
    }
}

export async function POST(request: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json(
                { error: "Not authenticated" },
                { status: 401 }
            );
        }

        const body = await request.json();

        let currentUserId: string;

        if (session.user?.email) {
            const emailUserId = await db
                .select({ id: users.id })
                .from(users)
                .where(eq(users.email, session.user.email))
                .limit(1)
            currentUserId = emailUserId[0].id as any
        } else {
            currentUserId = await salesforce.getCurrentUserId();
        }

        // Check if this is a sync request or a create request
        if (body.action === 'sync') {
            // Get leads from Salesforce
            const sfLeads = await salesforce.getLeads();

            // Process each lead
            for (const sfLead of sfLeads) {
                // Check if lead already exists
                const existingLead = await db
                    .select()
                    .from(leads)
                    .where(eq(leads.salesforceId, sfLead.Id))
                    .limit(1);

                if (existingLead.length > 0) {
                    // Update existing lead
                    await db
                        .update(leads)
                        .set({
                            firstName: sfLead.FirstName || undefined,
                            lastName: sfLead.LastName || undefined,
                            email: sfLead.Email || undefined,
                            company: sfLead.Company || 'Unknown Company',
                            title: sfLead.Title || undefined,
                            phone: sfLead.Phone || undefined,
                            status: sfLead.Status || undefined,
                            rating: sfLead.Rating || undefined,
                            industry: sfLead.Industry || undefined,
                            leadSource: sfLead.LeadSource || undefined,
                            description: sfLead.Description || undefined,
                            website: sfLead.Website || undefined,
                            numberOfEmployees: sfLead.NumberOfEmployees || undefined,
                            lastModifiedDate: sfLead.LastModifiedDate
                                ? new Date(sfLead.LastModifiedDate)
                                : undefined,
                            createdBy: currentUserId,
                            updatedAt: new Date(),
                        })
                        .where(eq(leads.id, existingLead[0].id));
                } else {
                    // Create new lead
                    await db.insert(leads).values({
                        salesforceId: sfLead.Id,
                        firstName: sfLead.FirstName || undefined,
                        lastName: sfLead.LastName || undefined,
                        email: sfLead.Email || undefined,
                        company: sfLead.Company || 'Unknown Company',
                        title: sfLead.Title || undefined,
                        phone: sfLead.Phone || undefined,
                        status: sfLead.Status || undefined,
                        rating: sfLead.Rating || undefined,
                        industry: sfLead.Industry || undefined,
                        leadSource: sfLead.LeadSource || undefined,
                        description: sfLead.Description || undefined,
                        website: sfLead.Website || undefined,
                        numberOfEmployees: sfLead.NumberOfEmployees || undefined,
                        lastModifiedDate: sfLead.LastModifiedDate
                            ? new Date(sfLead.LastModifiedDate)
                            : undefined,
                        createdBy: currentUserId,
                        updatedAt: new Date(),
                    });
                }
            }

            return NextResponse.json({ success: true });
        } else {
            // Validate required fields
            if (!body.firstName && !body.lastName) {
                return NextResponse.json(
                    { error: "firstName or lastName is required" },
                    { status: 400 }
                );
            }

            if (!body.company) {
                return NextResponse.json(
                    { error: "Company is required" },
                    { status: 400 }
                );
            }

            // Prepare lead data
            const leadData = {
                firstName: body.firstName || null,
                lastName: body.lastName || null,
                email: body.email || null,
                company: body.company || 'Unknown Company',
                title: body.title || null,
                phone: body.phone || null,
                status: body.status || null,
                industry: body.industry || null,
                rating: body.rating || null,
                leadSource: body.leadSource || null,
                description: body.description || null,
                website: body.website || null,
                numberOfEmployees: body.numberOfEmployees || null,
                createdBy: currentUserId,
                updatedAt: new Date(),
            };

            // Create a new lead
            const [newLead] = await db
                .insert(leads)
                .values(leadData)
                .returning();

            // Return the lead with its ID
            return NextResponse.json(newLead);
        }
    } catch (error: any) {
        console.error("Error in POST /api/leads:", error);

        if (error.redirectTo) {
            return NextResponse.json(
                { error: "Authentication required", redirectTo: error.redirectTo },
                { status: 401 }
            );
        }

        return NextResponse.json(
            { error: error.message || "Operation failed" },
            { status: 500 }
        );
    }
}

export async function PUT(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const data = await request.json();
        const { id, ...updateData } = data;

        const [updatedLead] = await db
            .update(leads)
            .set(updateData)
            .where(and(
                eq(leads.id, id),
                eq(leads.createdBy, session.user.id)
            ))
            .returning();

        return NextResponse.json(updatedLead);
    } catch (error) {
        console.error('Error updating lead:', error);
        return NextResponse.json(
            { error: 'Failed to update lead' },
            { status: 500 }
        );
    }
}

================
File: src/app/api/prompts/[id]/route.ts
================
import { NextResponse } from "next/server";
import { db } from "@/db";
import { prompts } from "@/db/schema/prompts";
import { eq } from "drizzle-orm";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

export async function GET(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const prompt = await db
            .select()
            .from(prompts)
            .where(eq(prompts.id, parseInt(params.id)))
            .then((res) => res[0]);

        if (!prompt) {
            return NextResponse.json({ error: "Prompt not found" }, { status: 404 });
        }

        return NextResponse.json(prompt);
    } catch (error) {
        console.error("Error fetching prompt:", error);
        return NextResponse.json(
            { error: "Failed to fetch prompt" },
            { status: 500 }
        );
    }
}

export async function PUT(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const data = await request.json();
        const prompt = await db
            .select()
            .from(prompts)
            .where(eq(prompts.id, parseInt(params.id)))
            .then((res) => res[0]);

        if (!prompt) {
            return NextResponse.json({ error: "Prompt not found" }, { status: 404 });
        }

        if (prompt.createdBy !== session.user.id) {
            return NextResponse.json(
                { error: "Unauthorized to modify this prompt" },
                { status: 403 }
            );
        }

        const updateData = {
            ...data,
            version: prompt.version + 1,
            isActive: data.isActive ?? prompt.isActive,
            settings: data.settings || prompt.settings,
            metadata: {
                ...prompt.metadata as Object,
                ...data.metadata,
                lastUpdate: {
                    timestamp: new Date().toISOString(),
                    user: session.user.id
                }
            }
        };

        const updatedPrompt = await db
            .update(prompts)
            .set(updateData)
            .where(eq(prompts.id, parseInt(params.id)))
            .returning();

        return NextResponse.json(updatedPrompt[0]);
    } catch (error) {
        console.error("Error updating prompt:", error);
        return NextResponse.json(
            { error: "Failed to update prompt" },
            { status: 500 }
        );
    }
}

export async function DELETE(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const prompt = await db
            .select()
            .from(prompts)
            .where(eq(prompts.id, parseInt(params.id)))
            .then((res) => res[0]);

        if (!prompt) {
            return NextResponse.json({ error: "Prompt not found" }, { status: 404 });
        }

        if (prompt.createdBy !== session.user.email && prompt.createdBy !== session.user.id) {
            return NextResponse.json(
                { error: "Unauthorized to delete this prompt" },
                { status: 403 }
            );
        }

        await db.delete(prompts).where(eq(prompts.id, parseInt(params.id)));
        return NextResponse.json({ success: true });
    } catch (error) {
        console.error("Error deleting prompt:", error);
        return NextResponse.json(
            { error: "Failed to delete prompt" },
            { status: 500 }
        );
    }
}

================
File: src/app/api/prompts/route.ts
================
import { NextResponse } from "next/server";
import { db } from "@/db";
import { prompts } from "@/db/schema/prompts";
import { eq, desc } from "drizzle-orm";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

export async function GET() {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }
        const allPrompts = await db.select().from(prompts).where(eq(prompts.createdBy, session.user.id)).orderBy(desc(prompts.createdAt));
        return NextResponse.json(allPrompts);
    } catch (error) {
        console.error("Error fetching prompts:", error);
        return NextResponse.json(
            { error: "Failed to fetch prompts" },
            { status: 500 }
        );
    }
}

export async function POST(request: Request) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const data = await request.json();
        const newPrompt = await db.insert(prompts).values({
            ...data,
            createdBy: session.user.id,
            version: 1,
            isActive: true,
            settings: data.settings || {},
            metadata: data.metadata || {}
        }).returning();

        return NextResponse.json(newPrompt[0]);
    } catch (error) {
        console.error("Error creating prompt:", error);
        return NextResponse.json(
            { error: "Failed to create prompt" },
            { status: 500 }
        );
    }
}

================
File: src/app/api/salesforce/test/route.ts
================
import { NextResponse } from 'next/server';
import { salesforce } from '@/services/salesforce';

export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    // Get some test data
    const testData = {
      connection: 'Success',
      timestamp: new Date().toISOString(),
      leads: await salesforce.getLeads()
    };
    
    return NextResponse.json(testData);
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Failed to connect to Salesforce' },
      { status: 500 }
    );
  }
}

================
File: src/app/api/test/email/generate/route.ts
================
import { EmailService } from "@/services/email";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  try {
    // Get the salesforce ID from query parameters
    const { searchParams } = new URL(request.url);
    const salesforceId = searchParams.get("salesforceId");

    if (!salesforceId) {
      return NextResponse.json(
        { error: "Salesforce ID is required" },
        { status: 400 }
      );
    }

    const email = await EmailService.generatePersonalizedEmail(salesforceId);
    return NextResponse.json(email);
  } catch (error) {
    console.error("Error generating email:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An error occurred" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    // Get salesforceId and prompt from request body
    const { salesforceId, prompt } = await request.json();

    // Validate required parameters
    if (!salesforceId) {
      return NextResponse.json(
        { error: "Salesforce ID is required" },
        { status: 400 }
      );
    }

    if (!prompt) {
      return NextResponse.json(
        { error: "Prompt is required" },
        { status: 400 }
      );
    }

    // Generate email using EmailService with custom prompt
    const email = await EmailService.generatePersonalizedEmail(
      salesforceId,
      prompt
    );

    return NextResponse.json(email);
  } catch (error) {
    console.error("Error generating email:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An error occurred" },
      { status: 500 }
    );
  }
}

================
File: src/app/api/test/email/send/route.ts
================
import { EmailService } from "@/services/email";
import { getServerSession } from "next-auth";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  try {
    // Get the salesforce ID from query parameters
    const { searchParams } = new URL(request.url);
    const salesforceId = searchParams.get("salesforceId");

    if (!salesforceId) {
      return NextResponse.json(
        { error: "Salesforce ID is required" },
        { status: 400 }
      );
    }

    const [result, tokenUsage] = await EmailService.sendEmail(
      undefined,
      salesforceId
    );
    return NextResponse.json({ result, tokenUsage });
  } catch (error) {
    console.error("Error sending email:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An error occurred" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession()
    const body = await request.json();
    const { emailId, emailConfigId } = body;

    if (session?.user?.email) {
        if (!emailId) {
        return NextResponse.json(
            { error: "emailId is required" },
            { status: 400 }
        );
        }

        if (!emailConfigId) {
        return NextResponse.json(
            { error: "emailConfigId is required" },
            { status: 400 }
        );
        }

        const [result, tokenUsage] = await EmailService.sendEmail(emailId, undefined, emailConfigId);
        return NextResponse.json({ result, tokenUsage });
    } else {
        if (!emailId) {
        return NextResponse.json(
            { error: "emailId is required" },
            { status: 400 }
        );
        }

        const [result, tokenUsage] = await EmailService.sendEmail(emailId);
        return NextResponse.json({ result, tokenUsage });
    }
  } catch (error) {
    console.error("Error sending email:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An error occurred" },
      { status: 500 }
    );
  }
}

================
File: src/app/api/test/leads/history/route.ts
================
import { salesforce } from "@/services/salesforce";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const salesforceId = request.nextUrl.searchParams.get("salesforceId");

    if (!salesforceId) {
      return NextResponse.json(
        { error: "Salesforce ID is required" },
        { status: 400 }
      );
    }

    const history = await salesforce.getLeadComprehensiveHistory(salesforceId);
    return NextResponse.json(history);
  } catch (error) {
    console.error("Error in lead history route:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An error occurred" },
      { status: 500 }
    );
  }
}

================
File: src/app/api/test/leads/route.ts
================
import { salesforce } from "@/services/salesforce";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const salesforceId = request.nextUrl.searchParams.get("salesforceId");

    if (salesforceId) {
      const lead = await salesforce.getLeadById(salesforceId);
      return NextResponse.json(lead);
    } else {
      const leads = await salesforce.getLeads();
      return NextResponse.json(leads);
    }
  } catch (error) {
    console.error("Error in leads route:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An error occurred" },
      { status: 500 }
    );
  }
}

================
File: src/app/api/trigger/route.ts
================
import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const body = await req.json();
    const { leadId, emailConfigId } = body;

    if (!leadId || !emailConfigId) {
      return new NextResponse('Missing required parameters', { status: 400 });
    }

    return NextResponse.json({});

  } catch (error: any) {
    console.error('Error in email trigger:', error);
    return new NextResponse(error.message || 'Internal Server Error', {
      status: error.status || 500
    });
  }
}

================
File: src/app/api/v1/agents/[id]/route.ts
================
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { db } from '@/db';
import { eq } from 'drizzle-orm';
import { UpdateAgentInput } from '@/types/agent';
import { agents } from '@/db/schema';
import { authOptions } from '@/lib/auth';

export async function GET(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const agent = await db
            .select()
            .from(agents)
            .where(eq(agents.id, parseInt(params.id)))
            .then((res) => res[0]);

        if (!agent) {
            return NextResponse.json(
                { error: 'Agent not found' },
                { status: 404 }
            );
        }

        return NextResponse.json(agent);
    } catch (error) {
        console.error('Error fetching agent:', error);
        return NextResponse.json(
            { error: 'Failed to fetch agent' },
            { status: 500 }
        );
    }
}

export async function PUT(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const data: UpdateAgentInput = await request.json();
        const agent = await db
            .select()
            .from(agents)
            .where(eq(agents.id, parseInt(params.id)))
            .then((res) => res[0]);

        if (!agent) {
            return NextResponse.json(
                { error: 'Agent not found' },
                { status: 404 }
            );
        }

        if (agent.createdBy !== session.user.id) {
            return NextResponse.json(
                { error: 'Unauthorized to modify this agent' },
                { status: 403 }
            );
        }

        const updateData = {
            ...data,
            version: agent.version + 1,
            metadata: {
                ...agent.metadata as Object,
                lastUpdate: {
                    timestamp: new Date().toISOString(),
                    user: session.user.id
                }
            }
        };

        const [updatedAgent] = await db
            .update(agents)
            .set(updateData)
            .where(eq(agents.id, parseInt(params.id)))
            .returning();

        return NextResponse.json(updatedAgent);
    } catch (error) {
        console.error('Error updating agent:', error);
        return NextResponse.json(
            { error: 'Failed to update agent' },
            { status: 500 }
        );
    }
}

export async function DELETE(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const agent = await db
            .select()
            .from(agents)
            .where(eq(agents.id, parseInt(params.id)))
            .then((res) => res[0]);

        if (!agent) {
            return NextResponse.json(
                { error: 'Agent not found' },
                { status: 404 }
            );
        }

        if (agent.createdBy !== session.user.id) {
            return NextResponse.json(
                { error: 'Unauthorized to delete this agent' },
                { status: 403 }
            );
        }

        await db
            .delete(agents)
            .where(eq(agents.id, parseInt(params.id)));

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error('Error deleting agent:', error);
        return NextResponse.json(
            { error: 'Failed to delete agent' },
            { status: 500 }
        );
    }
}

================
File: src/app/api/v1/agents/route.ts
================
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { CreateAgentInput } from '@/types/agent';
import { db } from '@/db';
import { eq, sql, desc } from 'drizzle-orm';
import { agents } from '@/db/schema';
import { authOptions } from '@/lib/auth';

export async function GET(req: Request) {
    try {

        const session = await getServerSession(authOptions);

        if (!session?.user?.id) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const { searchParams } = new URL(req.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '10');
        const offset = (page - 1) * limit;

        const [agentsList, totalCount] = await Promise.all([
            db.select()
                .from(agents)
                .where(eq(agents.createdBy, session.user.id))
                .limit(limit)
                .offset(offset)
                .orderBy(desc(agents.updatedAt)),
            db.select({ count: sql<number>`count(*)` })
                .from(agents)
                .where(eq(agents.createdBy, session.user.id))
                .then(res => Number(res[0].count))
        ]);

        return NextResponse.json({
            agents: agentsList,
            pagination: {
                total: totalCount,
                page,
                limit,
                pages: Math.ceil(totalCount / limit)
            }
        });
    } catch (error) {
        console.error('Error in GET /api/v1/agents:', error);
        return NextResponse.json(
            { error: 'Failed to fetch agents' },
            { status: 500 }
        );
    }
}

export async function POST(req: Request) {
    try {

        const session = await getServerSession(authOptions);

        if (!session?.user?.id) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const data: CreateAgentInput = await req.json();

        const [agent] = await db.insert(agents).values({
            name: data.name,
            description: data.description,
            prompt: data.prompt,
            promptId: data.promptId || null,
            settings: data.settings || {},
            metadata: {},
            version: 1,
            isActive: data.isActive ?? true,
            createdBy: session.user.id
        }).returning();


        return NextResponse.json(agent);
    } catch (error) {
        console.error('Error in POST /api/v1/agents:', error);
        return NextResponse.json(
            { error: 'Failed to create agent' },
            { status: 500 }
        );
    }
}

================
File: src/app/api/v1/generate/route.ts
================
import { NextResponse } from "next/server"
import { db } from "@/db"
import { eq } from "drizzle-orm"
import { getServerSession } from "next-auth"
import { anthropic } from "@/services/anthropic"
import { formatPrompt } from "@/services/prompts"
import { agents, emails, leads } from "@/db/schema"
import { authOptions } from "@/lib/auth"
import { salesforce } from "@/services/salesforce"

// interface AgentSettings {
//     model?: string;
//     temperature?: number;
// }

async function prepareVariables(lead: any, _agent: any) {
    // Подготавливаем детали о лиде
    const lead_details = [
        `Role: ${lead.title || 'Not specified'}`,
        `Status: ${lead.status || 'Not specified'}`,
        `Source: ${lead.leadSource || 'Not specified'}`
    ].join('\n');

    // Подготавливаем информацию о компании
    const company_info = [
        `Industry: ${lead.industry || 'Not specified'}`,
        `Size: ${lead.numberOfEmployees ? `${lead.numberOfEmployees} employees` : 'Not specified'}`,
        `Website: ${lead.website || 'Not specified'}`
    ].join('\n');

    const table_lead_history = lead.communications ?
        lead.communications :
        ['No history available'];

    const sf_lead_history = lead.salesforceId ?
        await salesforce.getLeadComprehensiveHistory(lead.salesforceId) :
       table_lead_history;

    // История лида (можно расширить в будущем)
    const lead_history = Array.isArray(sf_lead_history) 
        ? JSON.stringify(sf_lead_history)
        : String(sf_lead_history);

    // Добавляем базовые переменные
    const variables: Record<string, string> = {
        leadFirstName: lead.firstName || '',
        leadLastName: lead.lastName || '',
        leadCompany: lead.company || '',
        leadEmail: lead.email || '',
        lead_details,
        company_info,
        lead_history
    };

    // Добавляем переменные из JSONB поля details
    if (lead.details) {
        Object.entries(lead.details).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                variables[`details_${key}`] = String(value);
            }
        });
    }

    // Добавляем переменные из JSONB поля communications
    if (lead.communications) {
        Object.entries(lead.communications).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                variables[`communications_${key}`] = String(value);
            }
        });
    }

    // Добавляем переменные из JSONB поля company_info
    if (lead.company_info) {
        Object.entries(lead.company_info).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                variables[`company_${key}`] = String(value);
            }
        });
    }

    return variables;
}

export async function POST(req: Request, _agent: any) {
    try {
        const session = await getServerSession(authOptions)
        if (!session?.user) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
        }

        const { leadId, agentId, extraPrompt } = await req.json()

        if (!leadId || !agentId) {
            return NextResponse.json(
                { error: "Missing required fields" },
                { status: 400 }
            )
        }

        // Получаем информацию о лиде
        const lead = await db.query.leads.findFirst({
            where: eq(leads.id, leadId),
        })

        if (!lead) {
            return NextResponse.json(
                { error: "Lead not found" },
                { status: 404 }
            )
        }

        // Получаем информацию об агенте
        const agent = await db.query.agents.findFirst({
            where: eq(agents.id, agentId),
        })

        if (!agent) {
            return NextResponse.json(
                { error: "Agent not found" },
                { status: 404 }
            )
        }

        console.log('lead', lead)

        let variables;
        try {
            // Подготавливаем переменные для промпта
            variables = await prepareVariables(lead, agent);
        } catch (error) {
            console.error('Error preparing variables:', error);
            return NextResponse.json({
                error: 'Error preparing variables',
                details: error
            }, { status: 500 });
        }

        console.log('variables', variables)

        // Форматируем промпт с переменными
        const formattedPrompt = formatPrompt(agent.prompt || '', {
            ...variables,
            senderName: session.user.name!
        });

        const fullPrompt = extraPrompt ? `${formattedPrompt}\n\n${extraPrompt}` : formattedPrompt
        console.log('fullPrompt', fullPrompt)
        try {
            console.log('try to generate email')
            // Генерируем email используя Anthropic
            const response = await anthropic.generateEmail(fullPrompt);
            console.log('email generated')
            // Форматируем ответ с переменными
            const formattedResponse = {
                ...response,
                subject: formatPrompt(response.subject, {
                    ...variables,
                    senderName: session.user.name!
                }),
                body: formatPrompt(response.body, {
                    ...variables,
                    senderName: session.user.name!
                })
            };

            console.log('formattedResponse', formattedResponse)
            let savedEmail;
            console.log('try to save email')
            try {
                // Сохраняем email в базу данных
                [savedEmail] = await db.insert(emails).values({
                    leadId: lead.id,
                    subject: formattedResponse.subject,
                    content: formattedResponse.body,
                    prompt: fullPrompt,
                    createdBy: session.user.id,
                    sent: false,
                    createdAt: new Date()
                }).returning();
            } catch (err) {
                console.log('saving', err)
                return NextResponse.json({
                    error: 'Error saving email',
                    details: err
                }, { status: 500 });
            }

            console.log('savedEmail', savedEmail)

            return NextResponse.json(savedEmail);
        } catch (error: any) {
            console.log('error', error)
            // Проверяем, является ли ошибка нашей APIError
            if (error.name === 'APIError') {
                return NextResponse.json({
                    type: 'error',
                    error: {
                        type: error.type,
                        message: error.message,
                        request_id: error.requestId,
                        details: error.error
                    }
                }, { 
                    status: error.status,
                    headers: error.requestId ? {
                        'x-request-id': error.requestId
                    } : undefined
                });
            }

            // Для остальных ошибок возвращаем общее сообщение
            return NextResponse.json({
                type: 'error',
                error: {
                    type: 'api_error',
                    message: 'Внутренняя ошибка сервера',
                    details: error.message || error
                }
            }, { status: 500 });
        }
    } catch (error: any) {
        console.error('API Error:', error);
        return NextResponse.json({
            type: 'error',
            error: {
                type: 'api_error',
                message: 'Внутренняя ошибка сервера',
                details: error.message || error
            }
        }, { status: 500 });
    }
}

================
File: src/app/api/v1/prompts/[id]/route.ts
================
import { NextResponse } from 'next/server';
import { db } from '@/db';
import { prompts } from '@/db/schema/prompts';
import { eq, and } from 'drizzle-orm';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json({ error: 'Не авторизован' }, { status: 401 });
        }

        const prompt = await db
            .select()
            .from(prompts)
            .where(and(
                eq(prompts.id, parseInt(params.id)),
                eq(prompts.createdBy, session.user.id)
            ))
            .limit(1)
            .then(res => res[0]);

        if (!prompt) {
            return NextResponse.json(
                { error: 'Промпт не найден или нет доступа' },
                { status: 404 }
            );
        }

        return NextResponse.json({ prompt });
    } catch (error) {
        console.error('Ошибка при получении промпта:', error);
        return NextResponse.json(
            { error: 'Не удалось получить промпт' },
            { status: 500 }
        );
    }
}

export async function DELETE(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json({ error: 'Не авторизован' }, { status: 401 });
        }

        // Проверяем, существует ли промпт и принадлежит ли он пользователю
        const prompt = await db
            .select()
            .from(prompts)
            .where(and(
                eq(prompts.id, parseInt(params.id)),
                eq(prompts.createdBy, session.user.id)
            ))
            .limit(1);

        if (!prompt.length) {
            return NextResponse.json(
                { error: 'Промпт не найден или нет доступа' },
                { status: 404 }
            );
        }

        await db
            .delete(prompts)
            .where(and(
                eq(prompts.id, parseInt(params.id)),
                eq(prompts.createdBy, session.user.id)
            ));

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error('Ошибка при удалении промпта:', error);
        return NextResponse.json(
            { error: 'Не удалось удалить промпт' },
            { status: 500 }
        );
    }
}

export async function PATCH(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json({ error: 'Не авторизован' }, { status: 401 });
        }

        const body = await request.json();
        const { isActive } = body;

        if (typeof isActive !== 'boolean') {
            return NextResponse.json(
                { error: 'Неверный формат данных' },
                { status: 400 }
            );
        }

        // Проверяем, существует ли промпт и принадлежит ли он пользователю
        const prompt = await db
            .select()
            .from(prompts)
            .where(and(
                eq(prompts.id, parseInt(params.id)),
                eq(prompts.createdBy, session.user.id)
            ))
            .limit(1);

        if (!prompt.length) {
            return NextResponse.json(
                { error: 'Промпт не найден или нет доступа' },
                { status: 404 }
            );
        }

        const result = await db
            .update(prompts)
            .set({
                isActive,
                updatedAt: new Date()
            })
            .where(and(
                eq(prompts.id, parseInt(params.id)),
                eq(prompts.createdBy, session.user.id)
            ))
            .returning();

        return NextResponse.json({ prompt: result[0] });
    } catch (error) {
        console.error('Ошибка при обновлении статуса промпта:', error);
        return NextResponse.json(
            { error: 'Не удалось обновить статус промпта' },
            { status: 500 }
        );
    }
}

================
File: src/app/api/v1/prompts/test/route.ts
================
import { NextResponse } from 'next/server';
import { anthropic } from '@/services/anthropic';
import { formatPrompt } from '@/services/prompts';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// interface PromptVariables {
//     leadFirstName?: string;
//     leadLastName?: string;
//     leadCompany?: string;
//     leadEmail?: string;
//     lead_details?: string;
//     lead_history?: string;
//     company_info?: string;
//     senderName?: string;
// }

export async function POST(request: Request) {
    try {
        const session = await getServerSession(authOptions);
        const body = await request.json();
        const { content, variables = {}, settings } = body;


        if (!content) {
            return NextResponse.json(
                { error: 'Email content template is required' },
                { status: 400 }
            );
        }

        // Добавляем имя отправителя из сессии
        const enrichedVariables = {
            ...variables,
            senderName: session?.user?.name || 'Nick'
        };

        // Заменяем переменные в контексте и инструкциях
        const formattedPrompt = formatPrompt(content, enrichedVariables);

        // Генерируем email используя Anthropic с отформатированным промптом
        const response = await anthropic.generateEmail(formattedPrompt,
            {
                model: settings.model,
                max_tokens: settings.maxTokens,
                temperature: settings.temperature,
            }
        );

        // Форматируем тему и тело с переменными
        const formattedResponse = {
            ...response,
            subject: formatPrompt(response.subject, enrichedVariables),
            body: formatPrompt(response.body, enrichedVariables),
            formattedPrompt
        };

        return NextResponse.json(formattedResponse);
    } catch (error) {
        console.error('Error in prompt test:', error);
        return NextResponse.json(
            { error: 'Failed to generate email' },
            { status: 500 }
        );
    }
}

================
File: src/app/api/v1/prompts/route.ts
================
import { NextResponse } from 'next/server';
import { db } from '@/db';
import { prompts } from '@/db/schema/prompts';
import { eq, and, desc, sql, asc } from 'drizzle-orm';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Определяем допустимые поля для сортировки
type SortableFields = keyof typeof prompts._.columns;

export async function GET(request: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json({ error: 'Не авторизован' }, { status: 401 });
        }

        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '10');
        const search = searchParams.get('search') || '';
        const type = searchParams.get('type') || '';
        const sortBy = (searchParams.get('sortBy') || 'updatedAt') as SortableFields;
        const sortOrder = searchParams.get('sortOrder') || 'desc';

        const offset = (page - 1) * limit;

        // Формируем условия поиска
        const searchConditions = [];
        searchConditions.push(eq(prompts.createdBy, session.user.id));

        if (search) {
            searchConditions.push(
                sql`(${prompts.title} ILIKE ${`%${search}%`} OR ${prompts.content} ILIKE ${`%${search}%`} OR ${prompts.description} ILIKE ${`%${search}%`})`
            );
        }

        if (type) {
            searchConditions.push(eq(prompts.type, type));
        }

        // Получаем промпты
        const userPrompts = await db
            .select()
            .from(prompts)
            .where(and(...searchConditions))
            .orderBy(sortOrder === 'desc' ? desc(prompts[sortBy]) : asc(prompts[sortBy]))
            .limit(limit)
            .offset(offset);

        // Получаем общее количество
        const totalCount = await db
            .select({ count: sql`count(*)` })
            .from(prompts)
            .where(and(...searchConditions));

        return NextResponse.json({
            prompts: userPrompts,
            pagination: {
                total: Number(totalCount[0].count),
                page,
                limit,
                pages: Math.ceil(Number(totalCount[0].count) / limit)
            }
        });
    } catch (error) {
        console.error('Ошибка при получении промптов:', error);
        return NextResponse.json(
            { error: 'Не удалось получить промпты' },
            { status: 500 }
        );
    }
}

export async function POST(request: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json({ error: 'Не авторизован' }, { status: 401 });
        }

        const body = await request.json();
        const { title, content, type = 'system', description = '', settings = {}, metadata = {} } = body;

        if (!title || !content) {
            return NextResponse.json(
                { error: 'Заголовок и содержание обязательны' },
                { status: 400 }
            );
        }

        const result = await db.insert(prompts).values({
            title,
            content,
            type,
            description,
            settings,
            metadata,
            createdBy: session.user.id,
            version: 1,
            isActive: true
        }).returning();

        return NextResponse.json(result[0]);
    } catch (error) {
        console.error('Ошибка при создании промпта:', error);
        return NextResponse.json(
            { error: 'Не удалось создать промпт' },
            { status: 500 }
        );
    }
}

export async function PUT(request: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json({ error: 'Не авторизован' }, { status: 401 });
        }

        const body = await request.json();
        const { id, ...updateData } = body;

        if (!id) {
            return NextResponse.json(
                { error: 'ID обязателен' },
                { status: 400 }
            );
        }

        // Проверяем, принадлежит ли промпт пользователю
        const prompt = await db.select()
            .from(prompts)
            .where(and(
                eq(prompts.id, id),
                eq(prompts.createdBy, session.user.id)
            ))
            .limit(1);

        if (!prompt.length) {
            return NextResponse.json(
                { error: 'Промпт не найден или нет доступа' },
                { status: 404 }
            );
        }

        const result = await db.update(prompts)
            .set({
                ...updateData,
                version: prompt[0].version + 1,
                updatedAt: new Date()
            })
            .where(eq(prompts.id, id))
            .returning();

        return NextResponse.json({ prompt: result[0] });
    } catch (error) {
        console.error('Ошибка при обновлении промпта:', error);
        return NextResponse.json(
            { error: 'Не удалось обновить промпт' },
            { status: 500 }
        );
    }
}

================
File: src/app/api/v1/salesforce/auth/callback/route.ts
================
import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { salesforce } from '@/services/salesforce';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const code = request.nextUrl.searchParams.get('code');
    
    if (!code) {
      return NextResponse.redirect(new URL('/login?error=missing_code', request.nextUrl.origin));
    }

    // Get code verifier from cookie
    const cookieStore = cookies();
    const codeVerifier = cookieStore.get('sf_code_verifier')?.value;

    if (!codeVerifier) {
      console.error('Code verifier not found in cookies');
      return NextResponse.redirect(new URL('/login?error=missing_verifier', request.nextUrl.origin));
    }

    // Create response for redirect
    const response = NextResponse.redirect(new URL('/', request.nextUrl.origin));

    // Delete code verifier cookie
    response.cookies.set('sf_code_verifier', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 0,
    });

    // Exchange code for tokens using code verifier
    const tokens = await salesforce.getAccessToken(code, codeVerifier);
    
    if (!tokens || !tokens.access_token || !tokens.instance_url) {
      throw new Error('Failed to get access token');
    }

    // Set tokens in cookies
    response.cookies.set('sf_session', tokens.access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 60 * 60 * 24, // 24 hours
    });

    if (tokens.refresh_token) {
      response.cookies.set('sf_refresh_token', tokens.refresh_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
        maxAge: 60 * 60 * 24 * 30, // 30 days
      });
    }

    response.cookies.set('sf_instance_url', tokens.instance_url, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 60 * 60 * 24 * 30, // 30 days
    });

    // Update Salesforce connection with new tokens
    salesforce.setTokens(tokens.access_token, tokens.refresh_token, tokens.instance_url);

    return response;
  } catch (error) {
    console.error('Error in callback:', error);
    return NextResponse.redirect(new URL('/login?error=auth_failed', request.nextUrl.origin));
  }
}

================
File: src/app/api/v1/salesforce/auth/route.ts
================
import { NextResponse } from 'next/server';
import { salesforce } from '@/services/salesforce';

export async function GET() {
    try {
        // Generate authorization URL and code verifier
        const { url, codeVerifier } = salesforce.getAuthorizationUrl();

        // Create response with URL
        const response = NextResponse.json({
            url,
            timestamp: new Date().toISOString()
        });

        // Set code verifier in a cookie on the response
        response.cookies.set('sf_code_verifier', codeVerifier, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            path: '/',
            maxAge: 300, // 5 minutes
        });

        return response;
    } catch (error) {
        console.error('Error generating auth URL:', error);
        return NextResponse.json(
            { error: 'Failed to generate authorization URL' },
            { status: 500 }
        );
    }
}

================
File: src/app/api/v1/settings/email/[id]/route.ts
================
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { emailConfigs } from "@/db/schema/email-configs";
import { users } from "@/db/schema/users";
import { getServerSession } from "next-auth";
import { and, eq } from "drizzle-orm";

export async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession();
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const { id } = params;

        // Get the user's ID
        const [user] = await db
            .select()
            .from(users)
            .where(eq(users.email, session.user.email))
            .limit(1);

        if (!user) {
            return NextResponse.json({ error: "User not found" }, { status: 404 });
        }

        // Get the email config and verify it belongs to the user
        const [config] = await db
            .select()
            .from(emailConfigs)
            .where(
                and(
                    eq(emailConfigs.id, id),
                    eq(emailConfigs.createdBy, user.id)
                )
            )
            .limit(1);

        if (!config) {
            return NextResponse.json(
                { error: "Email configuration not found" },
                { status: 404 }
            );
        }

        // Don't send the password back in the response
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { password, ...configWithoutPassword } = config;

        return NextResponse.json(configWithoutPassword);
    } catch (error) {
        console.error("Error fetching email config:", error);
        return NextResponse.json(
            { error: "Failed to fetch email configuration" },
            { status: 500 }
        );
    }
}

export async function DELETE(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession();
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const { id } = params;

        // First get the user's ID
        const [user] = await db
            .select()
            .from(users)
            .where(eq(users.email, session.user.email))
            .limit(1);

        if (!user) {
            return NextResponse.json({ error: "User not found" }, { status: 404 });
        }

        // Verify the email config belongs to the user before deleting
        const result = await db
            .delete(emailConfigs)
            .where(
                and(
                    eq(emailConfigs.id, id),
                    eq(emailConfigs.createdBy, user.id)
                )
            )
            .returning();

        if (!result.length) {
            return NextResponse.json(
                { error: "Email configuration not found" },
                { status: 404 }
            );
        }

        return NextResponse.json(
            { message: "Email configuration deleted successfully" },
            { status: 200 }
        );
    } catch (error) {
        console.error("Error deleting email config:", error);
        return NextResponse.json(
            { error: "Failed to delete email configuration" },
            { status: 500 }
        );
    }
}

export async function PUT(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession();
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // First get the user's ID
        const [user] = await db
            .select()
            .from(users)
            .where(eq(users.email, session.user.email))
            .limit(1);

        if (!user) {
            return NextResponse.json({ error: "User not found" }, { status: 404 });
        }

        const { id } = params;
        const body = await request.json();
        const { smtp, port, email, password } = body;

        // Validate required fields
        if (!smtp || !port || !email) {
            return NextResponse.json(
                { error: "SMTP, port, and email are required" },
                { status: 400 }
            );
        }

        const updateData: any = {
            smtp,
            port: Number(port),
            email,
            updatedAt: new Date(),
        };

        // Only update password if provided
        if (password) {
            updateData.password = password;
        }

        // Verify the email config belongs to the user before updating
        const result = await db
            .update(emailConfigs)
            .set(updateData)
            .where(
                and(
                    eq(emailConfigs.id, id),
                    eq(emailConfigs.createdBy, user.id)
                )
            )
            .returning();

        if (!result.length) {
            return NextResponse.json(
                { error: "Email configuration not found" },
                { status: 404 }
            );
        }

        return NextResponse.json(
            { message: "Email configuration updated successfully" },
            { status: 200 }
        );
    } catch (error) {
        console.error("Error updating email config:", error);
        return NextResponse.json(
            { error: "Failed to update email configuration" },
            { status: 500 }
        );
    }
}

================
File: src/app/api/v1/settings/email/route.ts
================
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { emailConfigs, users } from "@/db/schema";
import { getServerSession } from "next-auth";
import { v4 as uuidv4 } from "uuid";
import { eq } from "drizzle-orm";

export async function GET() {
    try {
        const session = await getServerSession();
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // First get the user id
        const user = await db.query.users.findFirst({
            where: eq(users.email, session.user.email),
        });

        if (!user) {
            return NextResponse.json({ error: "User not found" }, { status: 404 });
        }

        const configs = await db.query.emailConfigs.findMany({
            where: eq(emailConfigs.createdBy, user.id),
            orderBy: (emailConfigs, { desc }) => [desc(emailConfigs.createdAt)],
        });

        // Don't send passwords back to the client
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const configsWithoutPasswords = configs.map(({ password, ...config }) => config);
        return NextResponse.json(configsWithoutPasswords);
    } catch (error) {
        console.error("Error fetching email configs:", error);
        return NextResponse.json(
            { error: "Failed to fetch email configurations" },
            { status: 500 }
        );
    }
}

export async function POST(request: NextRequest) {
    try {
        const session = await getServerSession();
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // First get the user id
        const user = await db.query.users.findFirst({
            where: eq(users.email, session.user.email),
        });

        if (!user) {
            return NextResponse.json({ error: "User not found" }, { status: 404 });
        }

        const body = await request.json();
        const { smtp, port, email, password } = body;

        // Validate required fields
        if (!smtp || !port || !email || !password) {
            return NextResponse.json(
                { error: "All fields are required" },
                { status: 400 }
            );
        }

        // Create new config
        await db.insert(emailConfigs).values({
            id: uuidv4(),
            smtp,
            port: Number(port),
            email,
            password,
            createdBy: user.id,
            createdAt: new Date(),
            updatedAt: new Date(),
        });

        return NextResponse.json(
            { message: "Email configuration saved" },
            { status: 201 }
        );
    } catch (error) {
        console.error("Error saving email config:", error);
        return NextResponse.json(
            { error: "Failed to save email configuration" },
            { status: 500 }
        );
    }
}

================
File: src/app/api/v1/settings/profile/route.ts
================
import { getServerSession } from "next-auth";
import { NextResponse } from "next/server";
import { authOptions } from "@/lib/auth";
import { db } from "@/db";
import { users } from "@/db/schema";
import { eq } from "drizzle-orm";
import bcrypt from "bcryptjs";

// Existing POST handler can stay for backward compatibility
export async function POST(req: Request) {
    return handleProfileUpdate(req);
  }
  
  // Add PUT handler that uses the same logic
  export async function PUT(req: Request) {
    return handleProfileUpdate(req);
  }

  // Add GET handler
  export async function GET(req: Request) {
    try {
      const session = await getServerSession(authOptions);
      
      if (!session?.user?.email) {
        return NextResponse.json(
          { error: "Unauthorized" },
          { status: 401 }
        );
      }

      // Get the current user from the database
      const [user] = await db
        .select({
          name: users.name,
          email: users.email,
          company: users.company,
        })
        .from(users)
        .where(eq(users.email, session.user.email))
        .limit(1);

      if (!user) {
        return NextResponse.json(
          { error: "User not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        fullName: user.name,
        email: user.email,
        company: user.company,
      });

    } catch (error) {
      console.error("Profile fetch error:", error);
      return NextResponse.json(
        { error: "Failed to fetch profile" },
        { status: 500 }
      );
    }
  }

  
async function handleProfileUpdate(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { fullName, company, email, password } = await req.json();

    // Get the current user from the database
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.email, session.user.email))
      .limit(1);

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {
      name: fullName,
      company,
      email,
    };

    // Only hash and update password if it's provided
    if (password) {
      const hashedPassword = await bcrypt.hash(password, 10);
      updateData.password = hashedPassword;
    }

    // Update user in database
    const [updatedUser] = await db
      .update(users)
      .set(updateData)
      .where(eq(users.id, user.id))
      .returning();

    return NextResponse.json({
      message: "Profile updated successfully",
      user: {
        name: updatedUser.name,
        email: updatedUser.email,
        company: updatedUser.company,
      },
    });

  } catch (error) {
    console.error("Profile update error:", error);
    return NextResponse.json(
      { error: "Failed to update profile" },
      { status: 500 }
    );
  }
}

================
File: src/app/emails/page.tsx
================
"use client";

import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { Eye, ExternalLink } from "lucide-react";
import Link from "next/link";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { EmailDetailsModal } from "@/components/emails/EmailDetailsModal";
import { StatusBadge } from "@/components/ui/status-badge";

export default function EmailsPage() {
  const [selectedEmailId, setSelectedEmailId] = useState<number | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  const { data: emails, isLoading } = useQuery({
    queryKey: ["emails"],
    queryFn: async () => {
      const response = await fetch("/api/emails");
      if (!response.ok) throw new Error("Failed to fetch emails");
      return response.json();
    },
  });

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Emails</h1>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Subject</TableHead>
              <TableHead className="w-[200px]">Lead</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
                  </div>
                </TableCell>
              </TableRow>
            ) : emails?.map((email: any) => (
              <TableRow key={email.id}>
                <TableCell>{email.subject}</TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <span>{email.leadFirstName} {email.leadLastName}</span>
                    <Link
                      href={`/leads/${email.leadId}`}
                      className="text-blue-500 hover:text-blue-700"
                      target="_blank"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </Link>
                  </div>
                </TableCell>
                <TableCell>
                  <StatusBadge
                    variant={email.sent ? "success" : "warning"}
                  >
                    {email.sent ? "Sent" : "Draft"}
                  </StatusBadge>
                </TableCell>
                <TableCell>{new Date(email.createdAt).toLocaleString()}</TableCell>
                <TableCell>
                  <div className="flex items-center justify-center">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => {
                        setSelectedEmailId(email.id);
                        setIsDetailsModalOpen(true);
                      }}
                      title="View Email Details"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <EmailDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedEmailId(null);
        }}
        emailId={selectedEmailId}
      />
    </div>
  );
}

================
File: src/app/leads/[id]/page.tsx
================
"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import {
  Mail,
  Send,
  Trash2,
  FileEdit,
  Loader2,
  ArrowLeft,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSession } from "next-auth/react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AgentSelect } from "@/components/agents/AgentSelect";
import { useToast } from "@/components/ui/use-toast";
import { LeadCommunicationsHistory } from "@/components/leads/LeadCommunicationsHistory";

interface Lead {
  id: number;
  salesforceId: string;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  company: string | null;
  title: string | null;
  phone: string | null;
  status: string | null;
  industry: string | null;
  rating: string | null;
  leadSource: string | null;
  description: string | null;
  website: string | null;
  numberOfEmployees: number | null;
  lastModifiedDate: string | null;
  communications: {
    facebook?: string;
    linkedin?: string;
    telegram?: string;
    vkontakte?: string;
    last_activity?: string;
    other_contact?: string;
    other_website?: string;
    contact_history?: string;
    [key: string]: any;
  } | null;
  history: any | null;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  details: {
    bitrix_id?: string;
    middle_name?: string;
    birth_date?: string;
    role?: string;
    contact_type?: string;
    tags?: string;
    city?: string;
    products?: string;
    projects?: string;
    mailing?: string;
    language?: string;
    tech_field?: string;
    export?: string;
    commented?: string;
    [key: string]: any;
  } | null;
  company_info: {
    name?: string;
    website?: string;
    industry?: string | null;
    [key: string]: any;
  } | null;
}

interface Email {
  id: number;
  subject: string;
  content: string;
  prompt: string;
  sent: boolean;
  createdAt: string;
}

interface EmailConfig {
  id: string;
  smtp: string;
  port: number;
  email: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export default function LeadPage() {
  const router = useRouter();
  const params = useParams();
  const [lead, setLead] = useState<Lead | null>(null);
  const [emails, setEmails] = useState<Email[]>([]);
  const [loading, setLoading] = useState(false);
  const [emailConfigs, setEmailConfigs] = useState<EmailConfig[]>([]);
  const [selectedEmailConfig, setSelectedEmailConfig] = useState<string>("");
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const [emailToSend, setEmailToSend] = useState<number | null>(null);
  const [editingEmail, setEditingEmail] = useState<number | null>(null);
  const [editedSubject, setEditedSubject] = useState("");
  const [editedContent, setEditedContent] = useState("");
  const [selectedAgent, setSelectedAgent] = useState<any | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [extraPrompt, setExtraPrompt] = useState<string>("");
  const session = useSession();
  const { toast } = useToast();
  const [showPromptMap, setShowPromptMap] = useState<Record<number, boolean>>({});

  useEffect(() => {
    fetchLead();
    fetchEmailConfigs();
  }, [params.id]);

  useEffect(() => {
    if (lead) {
      fetchEmails(lead.id.toString());
    }
  }, [lead]);

  const fetchLead = async () => {
    try {
      const response = await fetch(`/api/leads/${params.id}`);
      const data = await response.json();
      setLead(data);
    } catch (error) {
      console.error("Error fetching lead:", error);
      toast({
        title: "Error",
        description: "Failed to fetch lead details",
        variant: "destructive",
      });
    }
  };

  const fetchEmailConfigs = async () => {
    try {
      if (session.data?.user?.email) {
        const response = await fetch("/api/v1/settings/email");
        const data = await response.json();
        setEmailConfigs(Array.isArray(data) ? data : []);
      } else {
        setEmailConfigs([]);
      }
    } catch (error) {
      console.error("Error fetching email configs:", error);
      setEmailConfigs([]);
    }
  };

  const fetchEmails = async (leadId: string) => {
    try {
      const response = await fetch(`/api/emails?leadId=${leadId}`);
      const data = await response.json();
      const sortedEmails = Array.isArray(data) ? 
        data.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()) : 
        [];
      setEmails(sortedEmails);
    } catch (error) {
      console.error("Error fetching emails:", error);
      setEmails([]);
    }
  };

  const handleSendClick = (emailId: number) => {
    setEmailToSend(emailId);
    setShowEmailDialog(true);
  };

  const sendEmail = async (emailId?: number) => {
    setLoading(true);
    try {
      if (session.data?.user?.email) {
        const response = await fetch("/api/test/email/send", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            emailId: emailToSend,
            emailConfigId: selectedEmailConfig,
          }),
        });

        if (!response.ok) throw new Error("Failed to send email");

        setEmails((prev) =>
          prev.map((email) =>
            email.id === emailToSend ? { ...email, sent: true } : email
          )
        );

        setShowEmailDialog(false);
        setSelectedEmailConfig("");
        setEmailToSend(null);
      } else {
        const response = await fetch("/api/test/email/send", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            emailId,
          }),
        });

        if (!response.ok) throw new Error("Failed to send email");

        setEmails((prev) =>
          prev.map((email) =>
            email.id === emailId ? { ...email, sent: true } : email
          )
        );
      }
      
      toast({
        title: "Success",
        description: "Email sent successfully",
      });
    } catch (error) {
      console.error("Error sending email:", error);
      toast({
        title: "Error",
        description: "Failed to send email",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditClick = (email: Email) => {
    setEditingEmail(email.id);
    setEditedSubject(email.subject);
    setEditedContent(email.content);
  };

  const handleSaveEdit = async (emailId: number) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/emails/${emailId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subject: editedSubject,
          content: editedContent,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update email');
      }

      const updatedEmail = await response.json();
      setEmails(emails.map(email => 
        email.id === emailId ? updatedEmail : email
      ).sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));
      setEditingEmail(null);
      
      toast({
        title: "Success",
        description: "Email updated successfully",
      });
    } catch (error) {
      console.error('Error updating email:', error);
      toast({
        title: "Error",
        description: "Failed to update email",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingEmail(null);
    setEditedSubject("");
    setEditedContent("");
  };

  const handleDeleteEmail = async (emailId: number) => {
    if (!confirm("Are you sure you want to delete this email?")) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/emails/${emailId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete email');
      }

      setEmails(emails.filter(email => email.id !== emailId));
      toast({
        title: "Success",
        description: "Email deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting email:', error);
      toast({
        title: "Error",
        description: "Failed to delete email",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateEmail = async () => {
    if (!lead || !selectedAgent) {
      toast({
        title: "Error",
        description: "Please select an agent to generate email",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsGenerating(true);
      const response = await fetch("/api/v1/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          leadId: lead.id,
          agentId: selectedAgent.id,
          extraPrompt,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Error generating email");
      }

      const generatedEmail = await response.json();
      setEmails((prevEmails) => [generatedEmail, ...prevEmails]);
      
      toast({
        title: "Success",
        description: "Email generated successfully",
      });
    } catch (error) {
      console.error("Error generating email:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error generating email",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Функция для переключения видимости промпта
  const togglePromptVisibility = (emailId: number) => {
    setShowPromptMap((prev) => ({
      ...prev,
      [emailId]: !prev[emailId],
    }));
  };

  // Функция для вставки переменной в текстовое поле промпта
  const insertVariable = (variable: string) => {
    setExtraPrompt((prev) => prev + ` ${variable}`);
  };

  if (!lead) {
    return <div className="container mx-auto p-4">Loading...</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <div className="mb-6">
        <Button
          variant="outline"
          onClick={() => router.push('/leads')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Leads
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6">
        {/* Lead Details & Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Lead Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>First Name</Label>
                  <Input value={lead.firstName || ''} readOnly />
                </div>
                <div>
                  <Label>Last Name</Label>
                  <Input value={lead.lastName || ''} readOnly />
                </div>
              </div>
              <div>
                <Label>Email</Label>
                <Input value={lead.email || ''} readOnly />
              </div>
              <div>
                <Label>Company</Label>
                <Input value={lead.company || ''} readOnly />
              </div>
              <div>
                <Label>Title</Label>
                <Input value={lead.title || ''} readOnly />
              </div>
              <div>
                <Label>Status</Label>
                <Input value={lead.status || ''} readOnly />
              </div>

              {/* Дополнительная информация из JSON-полей */}
              {lead.details && (
                <div className="border p-4 rounded-md mt-4">
                  <h3 className="font-medium mb-3">Дополнительная информация</h3>
                  <div className="grid grid-cols-2 gap-4">
                    {lead.details.bitrix_id && (
                      <div>
                        <Label>ID в Битрикс</Label>
                        <Input value={lead.details.bitrix_id} readOnly />
                      </div>
                    )}
                    {lead.details.middle_name && (
                      <div>
                        <Label>Отчество</Label>
                        <Input value={lead.details.middle_name} readOnly />
                      </div>
                    )}
                    {lead.details.role && (
                      <div>
                        <Label>Роль</Label>
                        <Input value={lead.details.role} readOnly />
                      </div>
                    )}
                    {lead.details.city && (
                      <div>
                        <Label>Город</Label>
                        <Input value={lead.details.city} readOnly />
                      </div>
                    )}
                    {lead.details.language && (
                      <div>
                        <Label>Язык</Label>
                        <Input value={lead.details.language} readOnly />
                      </div>
                    )}
                    {lead.details.tags && (
                      <div>
                        <Label>Теги</Label>
                        <Input value={lead.details.tags} readOnly />
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Информация о коммуникациях */}
              {lead.communications && typeof lead.communications === 'object' && (
                <div className="border p-4 rounded-md mt-4">
                  <h3 className="font-medium mb-3">Контактная информация</h3>
                  <div className="grid grid-cols-2 gap-4">
                    {lead.communications.facebook && (
                      <div>
                        <Label>Facebook</Label>
                        <Input value={lead.communications.facebook} readOnly />
                      </div>
                    )}
                    {lead.communications.telegram && (
                      <div>
                        <Label>Telegram</Label>
                        <Input value={lead.communications.telegram} readOnly />
                      </div>
                    )}
                    {lead.communications.vkontakte && (
                      <div>
                        <Label>ВКонтакте</Label>
                        <Input value={lead.communications.vkontakte} readOnly />
                      </div>
                    )}
                    {lead.communications.linkedin && (
                      <div>
                        <Label>LinkedIn</Label>
                        <Input value={lead.communications.linkedin} readOnly />
                      </div>
                    )}
                    {lead.communications.last_activity && (
                      <div>
                        <Label>Последняя активность</Label>
                        <Input value={lead.communications.last_activity} readOnly />
                      </div>
                    )}
                    {lead.communications.contact_history && (
                      <div className="col-span-2">
                        <Label>История контактов</Label>
                        <Textarea value={lead.communications.contact_history} readOnly className="mt-1" />
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Информация о компании */}
              {lead.company_info && (
                <div className="border p-4 rounded-md mt-4">
                  <h3 className="font-medium mb-3">Информация о компании</h3>
                  <div className="grid grid-cols-2 gap-4">
                    {lead.company_info.name && (
                      <div>
                        <Label>Название</Label>
                        <Input value={lead.company_info.name} readOnly />
                      </div>
                    )}
                    {lead.company_info.website && (
                      <div>
                        <Label>Веб-сайт</Label>
                        <Input value={lead.company_info.website} readOnly />
                      </div>
                    )}
                    {lead.company_info.industry && (
                      <div>
                        <Label>Отрасль</Label>
                        <Input value={lead.company_info.industry} readOnly />
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div>
              <Label>Agent</Label>
                <AgentSelect
                  selectedId={selectedAgent?.id}
                  onSelect={(agent) => setSelectedAgent(agent)}
                />
              </div>
              <div>
                    <Label htmlFor="description">Extra Prompt</Label>
                    <Textarea
                      id="description"
                      value={extraPrompt || ''}
                      onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => 
                        setExtraPrompt(e.target.value)}
                      className="min-h-[200px]"
                    />
                    <div className="mt-2 p-3 border rounded-md bg-muted/30">
                      <h4 className="text-sm font-medium mb-2">Доступные переменные:</h4>
                      <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                        <div>
                          <h5 className="font-medium">Основные:</h5>
                          <ul className="list-disc pl-4">
                            <li className="flex items-center justify-between">
                              <span>{'{{leadFirstName}}'} - имя лида</span>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="h-6 px-2 text-xs"
                                onClick={() => insertVariable('{{leadFirstName}}')}
                              >
                                +
                              </Button>
                            </li>
                            <li className="flex items-center justify-between">
                              <span>{'{{leadLastName}}'} - фамилия лида</span>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="h-6 px-2 text-xs"
                                onClick={() => insertVariable('{{leadLastName}}')}
                              >
                                +
                              </Button>
                            </li>
                            <li className="flex items-center justify-between">
                              <span>{'{{leadCompany}}'} - компания лида</span>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="h-6 px-2 text-xs"
                                onClick={() => insertVariable('{{leadCompany}}')}
                              >
                                +
                              </Button>
                            </li>
                            <li className="flex items-center justify-between">
                              <span>{'{{leadEmail}}'} - email лида</span>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="h-6 px-2 text-xs"
                                onClick={() => insertVariable('{{leadEmail}}')}
                              >
                                +
                              </Button>
                            </li>
                            <li className="flex items-center justify-between">
                              <span>{'{{lead_details}}'} - детали лида</span>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="h-6 px-2 text-xs"
                                onClick={() => insertVariable('{{lead_details}}')}
                              >
                                +
                              </Button>
                            </li>
                            <li className="flex items-center justify-between">
                              <span>{'{{lead_history}}'} - история лида</span>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="h-6 px-2 text-xs"
                                onClick={() => insertVariable('{{lead_history}}')}
                              >
                                +
                              </Button>
                            </li>
                            <li className="flex items-center justify-between">
                              <span>{'{{company_info}}'} - информация о компании</span>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="h-6 px-2 text-xs"
                                onClick={() => insertVariable('{{company_info}}')}
                              >
                                +
                              </Button>
                            </li>
                            <li className="flex items-center justify-between">
                              <span>{'{{senderName}}'} - имя отправителя</span>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="h-6 px-2 text-xs"
                                onClick={() => insertVariable('{{senderName}}')}
                              >
                                +
                              </Button>
                            </li>
                          </ul>
                        </div>
                        <div>
                          <h5 className="font-medium">Дополнительные поля:</h5>
                          <ul className="list-disc pl-4">
                            {lead.details && Object.keys(lead.details).map(key => (
                              <li key={`details_${key}`} className="flex items-center justify-between">
                                <span>{'{{details_' + key + '}}'} - {String(lead.details![key] || '')}</span>
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  className="h-6 px-2 text-xs"
                                  onClick={() => insertVariable(`{{details_${key}}}`)}
                                >
                                  +
                                </Button>
                              </li>
                            ))}
                            {lead.communications && Object.keys(lead.communications).map(key => (
                              <li key={`communications_${key}`} className="flex items-center justify-between">
                                <span>{'{{communications_' + key + '}}'} - {String(lead.communications![key] || '')}</span>
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  className="h-6 px-2 text-xs"
                                  onClick={() => insertVariable(`{{communications_${key}}}`)}
                                >
                                  +
                                </Button>
                              </li>
                            ))}
                            {lead.company_info && Object.keys(lead.company_info).map(key => (
                              <li key={`company_${key}`} className="flex items-center justify-between">
                                <span>{'{{company_' + key + '}}'} - {String(lead.company_info![key] || '')}</span>
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  className="h-6 px-2 text-xs"
                                  onClick={() => insertVariable(`{{company_${key}}}`)}
                                >
                                  +
                                </Button>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
              <div>
                <Button
                  onClick={handleGenerateEmail}
                  disabled={isGenerating || !selectedAgent}
                  className="w-full"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="h-5 w-5 animate-spin mr-2" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Mail className="h-5 w-5 mr-2" />
                      Generate Email
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs for Generated Emails and Communication History */}
        <Tabs defaultValue="emails" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="emails">Generated Emails</TabsTrigger>
            <TabsTrigger value="history">Communication History</TabsTrigger>
          </TabsList>
          
          <TabsContent value="emails">
            <Card>
              <CardHeader>
                <CardTitle>Generated Emails</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {emails.map((email) => (
                  <Card key={email.id}>
                    <CardContent className="pt-6">
                      {editingEmail === email.id ? (
                        // Edit mode
                        <div className="space-y-4">
                          <div>
                            <Label>Subject</Label>
                            <Input
                              value={editedSubject}
                              onChange={(e) => setEditedSubject(e.target.value)}
                            />
                          </div>
                          <div>
                            <Label>Content</Label>
                            <Textarea
                              value={editedContent}
                              onChange={(e) => setEditedContent(e.target.value)}
                              className="min-h-[200px]"
                            />
                          </div>
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleCancelEdit}
                              disabled={loading}
                            >
                              Cancel
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => handleSaveEdit(email.id)}
                              disabled={loading}
                            >
                              Save
                            </Button>
                          </div>
                        </div>
                      ) : (
                        // View mode
                        <>
                          <h3 className="font-medium">{email.subject}</h3>
                          <p className="text-sm text-muted-foreground mt-2 whitespace-pre-wrap">
                            {email.content}
                          </p>
                          <div className="mt-4">
                            <Button
                              variant="ghost" 
                              size="sm"
                              onClick={() => togglePromptVisibility(email.id)}
                              className="flex items-center text-xs text-muted-foreground"
                            >
                              {showPromptMap[email.id] ? (
                                <>
                                  <ChevronUp className="h-3 w-3 mr-1" />
                                  Скрыть промпт
                                </>
                              ) : (
                                <>
                                  <ChevronDown className="h-3 w-3 mr-1" />
                                  Показать промпт
                                </>
                              )}
                            </Button>
                            {showPromptMap[email.id] && (
                              <div className="mt-2 p-3 border rounded-md bg-muted/30">
                                <Label className="text-xs mb-1 block">Промпт для генерации</Label>
                                <p className="text-xs whitespace-pre-wrap">{email.prompt}</p>
                              </div>
                            )}
                          </div>
                          <div className="mt-2 flex justify-between items-center text-sm text-muted-foreground">
                            <span>{new Date(email.createdAt).toLocaleDateString()}</span>
                            <div className="flex items-center gap-2">
                              <Badge variant={email.sent ? "default" : "secondary"}>
                                {email.sent ? "Sent" : "Draft"}
                              </Badge>
                              {!email.sent && (
                                <>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleEditClick(email)}
                                    disabled={loading}
                                  >
                                    <FileEdit className="h-4 w-4 mr-1" />
                                    Edit
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() =>
                                      session.data?.user?.email
                                        ? handleSendClick(email.id)
                                        : sendEmail(email.id)
                                    }
                                    disabled={loading}
                                  >
                                    <Send className="h-4 w-4 mr-1" />
                                    Send
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleDeleteEmail(email.id)}
                                    disabled={loading}
                                  >
                                    <Trash2 className="h-4 w-4 mr-1" />
                                    Delete
                                  </Button>
                                </>
                              )}
                            </div>
                          </div>
                        </>
                      )}
                    </CardContent>
                  </Card>
                ))}
                {emails.length === 0 && (
                  <p className="text-muted-foreground">No emails generated yet</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="history">
            {lead.salesforceId && (
              <LeadCommunicationsHistory leadId={lead.salesforceId} />
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Email Selection Dialog */}
      <Dialog open={showEmailDialog} onOpenChange={setShowEmailDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Select Email to Send From</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Select value={selectedEmailConfig} onValueChange={setSelectedEmailConfig}>
              <SelectTrigger>
                <SelectValue placeholder="Select an email" />
              </SelectTrigger>
              <SelectContent>
                {session.data?.user?.email && (
                  <SelectItem value="logged_in">
                    {session.data.user.email} (Logged In)
                  </SelectItem>
                )}
                {emailConfigs.map((config) => (
                  <SelectItem key={config.id} value={config.id}>
                    {config.email}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="mt-4 flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowEmailDialog(false)}>
                Cancel
              </Button>
              <Button onClick={() => sendEmail()} disabled={!selectedEmailConfig || loading}>
                Send Email
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

================
File: src/app/leads/page.tsx
================
"use client";

import { useEffect, useState, useCallback } from "react";
import { LeadsTable } from "@/components/leads/LeadsTable";
import { LeadForm } from "@/components/leads/LeadForm";
import { Lead } from "@/db/schema/leads";
import { LeadsService } from "@/services/leads";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { Search } from "lucide-react";
import { SendEmailModal } from "@/components/leads/SendEmailModal";

// Define the form data type to match the LeadForm's schema
type LeadFormData = {
  firstName: string;
  lastName: string;
  email: string;
  company?: string;
  title?: string;
  phone?: string;
  status?: string;
  industry?: string;
  website?: string;
};

export default function LeadsPage() {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [search, setSearch] = useState("");
  const [selectedLeads, setSelectedLeads] = useState<number[]>([]);
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 1
  });
  const { toast } = useToast();

  const fetchLeads = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await LeadsService.getLeads({
        page,
        sortBy,
        sortOrder,
        limit: 10,
        search
      });
      setLeads(data.leads);
      setPagination(data.pagination);
    } catch (error: any) {
      console.error("Error fetching leads:", error);
      setError(error.message || "Failed to fetch leads");
    } finally {
      setIsLoading(false);
    }
  }, [page, sortBy, sortOrder, search]);

  useEffect(() => {
    fetchLeads();
  }, [page, sortBy, sortOrder, search, fetchLeads]);

  const handleCreate = async (formData: LeadFormData) => {
    if (!formData.company) {
      toast({
        title: "Error",
        description: "Company is required",
        variant: "destructive",
      });
      return;
    }

    try {
      // Add required fields and transform to match the expected type
      const leadData = {
        ...formData,
        company: formData.company,
        createdBy: "system", // You might want to get this from your auth context
        firstName: formData.firstName || null,
        lastName: formData.lastName || null,
        email: formData.email || null,
        title: formData.title || null,
        phone: formData.phone || null,
        industry: formData.industry || null,
        status: formData.status || null,
        website: formData.website || null,
      };

      await LeadsService.createLead(leadData);
      await fetchLeads();
      setIsCreating(false);
      toast({
        title: "Success",
        description: "Lead created successfully",
      });
    } catch (_error) {
      toast({
        title: "Error",
        description: "Failed to create lead",
        variant: "destructive",
      });
    }
  };

  const handleUpdate = async (id: number, data: Partial<Lead>) => {
    try {
      await LeadsService.updateLead(id, data);
      await fetchLeads();
      toast({
        title: "Success",
        description: "Lead updated successfully",
      });
    } catch (_error) {
      toast({
        title: "Error",
        description: "Failed to update lead",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (id: number) => {
    try {
      await LeadsService.deleteLead(id);
      await fetchLeads();
      toast({
        title: "Success",
        description: "Lead deleted successfully",
      });
    } catch (_error) {
      toast({
        title: "Error",
        description: "Failed to delete lead",
        variant: "destructive",
      });
    }
  };

  const handleSearch = (value: string) => {
    setSearch(value);
    setPage(1); // Reset to first page when searching
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button variant="default" onClick={() => setIsCreating(true)}>
            Create Lead
          </Button>
          {selectedLeads.length > 0 && (
            <Button 
              variant="outline" 
              onClick={() => setSelectedLeads([])}
            >
              Clear Selection ({selectedLeads.length})
            </Button>
          )}
        </div>
        <div className="flex items-center gap-4">
          <Button 
            variant="default"
            onClick={() => setIsEmailModalOpen(true)}
            disabled={selectedLeads.length === 0}
          >
            Send Email {selectedLeads.length > 0 && `(${selectedLeads.length})`}
          </Button>
        </div>
      </div>

      <div className="relative mb-4">
        <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search leads..."
          value={search}
          onChange={(e) => handleSearch(e.target.value)}
          className="pl-10"
        />
      </div>

      {error ? (
        <div className="text-red-500 mb-4">{error}</div>
      ) : (
        <LeadsTable
          leads={leads}
          isLoading={isLoading}
          pagination={pagination}
          selectedLeads={selectedLeads}
          onUpdate={handleUpdate}
          onDelete={handleDelete}
          onSort={(newSortBy, newSortOrder) => {
            setSortBy(newSortBy);
            setSortOrder(newSortOrder);
          }}
          onPageChange={setPage}
          onSelectionChange={setSelectedLeads}
        />
      )}

      <Dialog open={isCreating} onOpenChange={setIsCreating}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Lead</DialogTitle>
            <DialogDescription>
              Add a new lead to your pipeline.
            </DialogDescription>
          </DialogHeader>
          <LeadForm onSubmit={handleCreate} />
        </DialogContent>
      </Dialog>

      <SendEmailModal
        isOpen={isEmailModalOpen}
        onClose={() => setIsEmailModalOpen(false)}
        selectedLeads={selectedLeads}
      />
    </div>
  );
}

================
File: src/app/login/form.tsx
================
"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { signIn } from "next-auth/react";
import Link from "next/link";

export default function Form() {
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    await signIn("credentials", {
      email: formData.get("email"),
      password: formData.get("password"),
      redirect: true,
      callbackUrl: "/",
    });
  };

  const handleSalesforceSignIn = () => {
    signIn("salesforce", { callbackUrl: "/" });
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="email" className="text-sm font-medium">
            Email
          </label>
          <Input
            id="email"
            type="email"
            name="email"
            placeholder="Enter your email"
            required
          />
        </div>
        <div className="space-y-2">
          <label htmlFor="password" className="text-sm font-medium">
            Password
          </label>
          <Input
            id="password"
            type="password"
            name="password"
            placeholder="Enter your password"
            required
          />
        </div>
        <Button type="submit" className="w-full">
          Sign In
        </Button>
      </form>

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <Separator />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Or continue with
          </span>
        </div>
      </div>

      <Button
        type="button"
        variant="outline"
        className="w-full"
        onClick={handleSalesforceSignIn}
      >
        Sign in with Salesforce
      </Button>

      <div className="text-center text-sm">
        Don&apos;t have an account?{" "}
        <Link href="/register" className="text-primary hover:underline">
          Register
        </Link>
      </div>
    </div>
  );
}

================
File: src/app/login/page.tsx
================
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Form from "./form";

export default function LoginPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl text-center">Login</CardTitle>
        </CardHeader>
        <CardContent>
          <Form />
        </CardContent>
      </Card>
    </div>
  );
}

================
File: src/app/prompts/test/page.tsx
================
"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2Icon } from "lucide-react";

interface Lead {
  Id: string;
  FirstName: string;
  LastName: string;
  Email: string;
  Company: string;
  Title: string;
  Status: string;
  displayName: string;
}

interface Prompt {
  id: number;
  title: string;
  content: string;
  version: number;
}

export default function PromptsTestPage() {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [selectedLead, setSelectedLead] = useState<string>("");
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [promptTitle, setPromptTitle] = useState<string>("");
  const [promptTemplate, setPromptTemplate] = useState<string>(
    `You are writing a follow-up email to {{FirstName}} {{LastName}} who works at {{Company}} as {{Title}}.
Write a personalized email that references their company and role.

The email should be professional but friendly, and should aim to schedule a meeting.

Format the response as a proper email with subject line and body.`
  );
  const [generatedEmail, setGeneratedEmail] = useState<{
    subject: string;
    body: string;
    formattedPrompt: string;
  } | null>(null);
  const [generating, setGenerating] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch leads
        const leadsResponse = await fetch("/api/leads");
        const leadsData = await leadsResponse.json();

        if (leadsResponse.status === 401 && leadsData.redirectTo) {
          window.location.href = leadsData.redirectTo;
          return;
        }

        if (!leadsResponse.ok) {
          throw new Error(leadsData.error || "Failed to fetch leads");
        }

        // Fetch prompts
        const promptsResponse = await fetch("/api/v1/prompts");
        const promptsData = await promptsResponse.json();

        setLeads(leadsData.leads || []);
        setPrompts(promptsData.prompts || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const generateEmail = async () => {
    if (!selectedLead) {
      setError("Please select a lead first");
      return;
    }

    const lead = leads.find((l) => l.Id === selectedLead);
    if (!lead) return;

    try {
      setGenerating(true);
      setError(null);

      const response = await fetch("/api/v1/prompts/test", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: promptTemplate,
          variables: lead,
          settings: {
            model: "claude-3-5-haiku-20241022",
            temperature: 0.2,
            maxTokens: 4096
          }
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to generate email");
      }

      setGeneratedEmail({
        subject: data.subject,
        body: data.body,
        formattedPrompt: data.formattedPrompt
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to generate email");
    } finally {
      setGenerating(false);
    }
  };

  const savePrompt = async () => {
    if (!promptTitle || !promptTemplate) {
      setError("Title and prompt template are required");
      return;
    }

    try {
      setSaving(true);
      setError(null);

      const method = selectedPrompt ? "PUT" : "POST";
      const body = selectedPrompt
        ? { id: selectedPrompt.id, title: promptTitle, content: promptTemplate }
        : { title: promptTitle, content: promptTemplate };

      const response = await fetch("/api/v1/prompts", {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to save prompt");
      }

      // Refresh prompts list
      const promptsResponse = await fetch("/api/v1/prompts");
      const promptsData = await promptsResponse.json();
      setPrompts(promptsData.prompts || []);

      // Clear form if creating new prompt
      if (!selectedPrompt) {
        setPromptTitle("");
        setPromptTemplate("");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to save prompt");
    } finally {
      setSaving(false);
    }
  };

  const loadPrompt = (prompt: Prompt) => {
    setSelectedPrompt(prompt);
    setPromptTitle(prompt.title);
    setPromptTemplate(prompt.content);
  };

  if (loading) {
    return (
      <div className="p-4 max-w-4xl mx-auto space-y-4">
        <Skeleton className="h-8 w-48" />
        <div className="grid grid-cols-1 gap-4">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent className="space-y-2">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-10 w-32" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 max-w-4xl mx-auto">
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <p className="text-destructive">Error: {error}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-4 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Prompt Testing</h1>

      {/* Prompts List */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Saved Prompts</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 gap-2">
          {prompts.map((prompt) => (
            <Button
              key={`${prompt.id}-${prompt.version}`}
              onClick={() => loadPrompt(prompt)}
              variant="outline"
              className="h-auto py-2 justify-start"
            >
              <div className="text-left">
                <div className="font-medium">{prompt.title}</div>
                <div className="text-sm text-muted-foreground">
                  Version {prompt.version}
                </div>
              </div>
            </Button>
          ))}
        </CardContent>
      </Card>

      {/* Lead Selection */}
      <Card className="mb-4">
        <CardHeader>
          <CardTitle>Select Lead</CardTitle>
        </CardHeader>
        <CardContent>
          <Select value={selectedLead} onValueChange={setSelectedLead}>
            <SelectTrigger>
              <SelectValue placeholder="Select a lead..." />
            </SelectTrigger>
            <SelectContent>
              {leads.map((lead) => (
                <SelectItem key={lead.Id} value={lead.Id}>
                  {lead.displayName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Lead Details */}
          {selectedLead &&
            (() => {
              const lead = leads.find((l) => l.Id === selectedLead);
              return lead ? (
                <div className="mt-4 space-y-2">
                  <h3 className="font-medium">Lead Details</h3>
                  <dl className="grid grid-cols-2 gap-2 text-sm">
                    <dt className="text-muted-foreground">Name:</dt>
                    <dd>
                      {lead.FirstName} {lead.LastName}
                    </dd>
                    <dt className="text-muted-foreground">Company:</dt>
                    <dd>{lead.Company}</dd>
                    <dt className="text-muted-foreground">Title:</dt>
                    <dd>{lead.Title}</dd>
                    <dt className="text-muted-foreground">Email:</dt>
                    <dd>{lead.Email}</dd>
                    <dt className="text-muted-foreground">Status:</dt>
                    <dd>{lead.Status}</dd>
                  </dl>
                </div>
              ) : null;
            })()}
        </CardContent>
      </Card>

      {/* Prompt Form */}
      <Card className="mb-4">
        <CardHeader>
          <CardTitle>Prompt Template</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium leading-none">
              Prompt Title
            </label>
            <Input
              value={promptTitle}
              onChange={(e) => setPromptTitle(e.target.value)}
              placeholder="Enter a title for your prompt"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium leading-none">
              Prompt Template
            </label>
            <Textarea
              value={promptTemplate}
              onChange={(e) => setPromptTemplate(e.target.value)}
              className="min-h-[150px]"
            />
            <div className="p-3 border rounded-md bg-muted/30 mt-2">
              <h4 className="text-sm font-medium mb-2">Доступные переменные:</h4>
              <ul className="list-disc pl-4 grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                <li>{'{{FirstName}}'} - имя лида</li>
                <li>{'{{LastName}}'} - фамилия лида</li>
                <li>{'{{Email}}'} - email лида</li>
                <li>{'{{Company}}'} - компания лида</li>
                <li>{'{{Title}}'} - должность лида</li>
                <li>{'{{Status}}'} - статус лида</li>
                <li>{'{{Id}}'} - ID лида</li>
                <li>{'{{senderName}}'} - имя отправителя (подставляется автоматически)</li>
              </ul>
            </div>
          </div>

          <div className="flex space-x-4">
            <Button
              onClick={savePrompt}
              disabled={saving || !promptTitle || !promptTemplate}
              variant="secondary"
            >
              {saving && <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />}
              {saving
                ? "Saving..."
                : selectedPrompt
                ? "Save New Version"
                : "Save Prompt"}
            </Button>

            <Button
              onClick={generateEmail}
              disabled={generating || !selectedLead}
            >
              {generating && (
                <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
              )}
              {generating ? "Generating..." : "Generate Email"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Generated Email */}
      <Card className="mb-4">
        <CardHeader>
          <CardTitle>Generated Email</CardTitle>
        </CardHeader>
        <CardContent>
          {generating ? (
            <div className="flex justify-center p-8">
              <Loader2Icon className="h-8 w-8 animate-spin" />
            </div>
          ) : generatedEmail ? (
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Subject:</h3>
                <div className="bg-muted rounded-md p-4 whitespace-pre-wrap font-mono text-sm">
                  {generatedEmail.subject}
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium mb-2">Body:</h3>
                <div className="bg-muted rounded-md p-4 whitespace-pre-wrap font-mono text-sm">
                  {generatedEmail.body}
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium mb-2">Formatted Prompt:</h3>
                <p className="text-xs text-muted-foreground mb-2">Полный промпт с подставленными переменными, который был отправлен в API</p>
                <div className="bg-muted rounded-md p-4 whitespace-pre-wrap font-mono text-sm max-h-96 overflow-y-auto">
                  {generatedEmail.formattedPrompt}
                </div>
              </div>
            </div>
          ) : (
            <p className="text-center p-8 text-muted-foreground">
              No email generated yet
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

================
File: src/app/prompts/page.tsx
================
"use client";

import { useState, useMemo } from "react";
import { usePrompts, useCreatePrompt, useDeletePrompt, useTestPrompt, useUpdatePrompt } from "@/hooks/use-prompts";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { type Prompt, type PromptType } from "@/db/schema/prompts";
import { PromptTester } from "@/components/prompts/PromptTester";
import { useToast } from "@/components/ui/use-toast";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { HighlightVariables } from "@/components/prompts/PromptVariableHighlighter";
import { Switch } from "@/components/ui/switch";

const promptTypes: PromptType[] = ["user", "system", "assistant"];

export default function PromptsPage() {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [content, setContent] = useState("");
  const [type, setType] = useState<PromptType>("system");
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [search, setSearch] = useState("");
  const [promptToDelete, setPromptToDelete] = useState<number | null>(null);
  const [previewMode, setPreviewMode] = useState(false);
  const { toast } = useToast();

  const { data: prompts, isLoading } = usePrompts();
  const createPrompt = useCreatePrompt();
  const deletePrompt = useDeletePrompt();
  const testPrompt = useTestPrompt();
  const updatePrompt = useUpdatePrompt();

  const filteredPrompts = useMemo(() => {
    if (!prompts) return [];
    if (!search) return prompts;

    const searchLower = search.toLowerCase();
    return prompts.filter(
      (prompt) =>
        prompt.title.toLowerCase().includes(searchLower) ||
        (prompt.description?.toLowerCase() || "").includes(searchLower) ||
        prompt.content.toLowerCase().includes(searchLower)
    );
  }, [prompts, search]);

  const handleCreate = async () => {
    try {
      await createPrompt.mutateAsync({
        title,
        description,
        content,
        type,
      });
      setTitle("");
      setDescription("");
      setContent("");
      toast({
        title: "Success",
        description: "Prompt created successfully",
      });
    } catch (_error) {
      toast({
        title: "Error",
        description: "Failed to create prompt",
        variant: "destructive",
      });
    }
  };

  const handlePromptClick = (prompt: Partial<Prompt>) => {
    const promptWithCreatedBy = {
      ...prompt,
      createdBy: prompt.createdBy || ''
    } as Prompt;
    setIsEditing(true);
    setSelectedPrompt(promptWithCreatedBy);
    setTitle(prompt.title || '');
    setDescription(prompt.description || '');
    setContent(prompt.content || '');
    setType((prompt.type || 'system') as PromptType);
    setSearch('');
  };

  const handleUpdate = async () => {
    if (!selectedPrompt) return;

    try {
      const updatedPrompt = await updatePrompt.mutateAsync({
        id: selectedPrompt.id,
        title,
        description,
        content,
        type,
      });
      setSelectedPrompt(updatedPrompt);
      toast({
        title: "Success",
        description: "Prompt updated successfully",
      });
    } catch (_error) {
      toast({
        title: "Error",
        description: "Failed to update prompt",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (promptId: number) => {
    try {
      await deletePrompt.mutateAsync(promptId);
      if (selectedPrompt?.id === promptId) {
        setSelectedPrompt(null);
      }
      toast({
        title: "Success",
        description: "Prompt deleted successfully",
      });
    } catch (_error) {
      toast({
        title: "Error",
        description: "Failed to delete prompt",
        variant: "destructive",
      });
    }
  };

  const handleTest = async (content: string, variables: Record<string, string>, settings: { model: string; temperature: number; maxTokens: number }) => {
    try {
      const result = await testPrompt.mutateAsync({
        content,
        settings,
        variables
      });
      
      toast({
        title: "Success",
        description: "Prompt tested successfully",
      });

      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to test prompt",
        variant: "destructive",
      });
      throw error;
    }
  };

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setContent(e.target.value);
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto py-10">
      <div className="grid grid-cols-12 gap-6">
        {/* Left Column - Prompts List */}
        <div className="col-span-4">
          <Card className="h-full">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
              <CardTitle>Prompts</CardTitle>
              <Button
                onClick={() => {
                  setIsEditing(true);
                  setSelectedPrompt(null);
                  setTitle("");
                  setDescription("");
                  setContent("");
                  setType("system");
                }}
              >
                Create New
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="rounded-lg border shadow-md">
                  <Input
                    placeholder="Search prompts..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="border-0 focus-visible:ring-0"
                  />
                  <div className="max-h-[300px] overflow-y-auto">
                    {filteredPrompts.length === 0 ? (
                      <div className="p-4 text-sm text-muted-foreground">
                        No prompts found.
                      </div>
                    ) : (
                      filteredPrompts.map((prompt) => (
                        <div
                          key={prompt.id}
                          className="flex flex-1 flex-col p-2 hover:bg-accent cursor-pointer border-t"
                          onClick={() => handlePromptClick(prompt)}
                          role="button"
                          tabIndex={0}
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{prompt.title}</span>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                setPromptToDelete(prompt.id);
                              }}
                            >
                              Delete
                            </Button>
                          </div>
                          {prompt.description && (
                            <span className="text-sm text-muted-foreground">
                              {prompt.description}
                            </span>
                          )}
                          <span className="text-xs text-muted-foreground">
                            Type: {prompt.type}
                          </span>
                          {prompt.content && prompt.content.length > 0 && (
                            <div className="text-xs text-muted-foreground mt-1 p-1 border-t border-dashed">
                              <HighlightVariables>
                                {prompt.content.length > 100
                                  ? `${prompt.content.substring(0, 100)}...`
                                  : prompt.content}
                              </HighlightVariables>
                            </div>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Edit/Test Area */}
        <div className="col-span-8">
          {selectedPrompt ? (
            <Card>
              <CardHeader>
                <CardTitle>{selectedPrompt.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="edit" className="space-y-4">
                  <TabsList>
                    <TabsTrigger value="edit">Edit</TabsTrigger>
                    <TabsTrigger value="test">Test</TabsTrigger>
                  </TabsList>

                  <TabsContent value="edit" className="space-y-4">
                    <div className="space-y-4">
                      <div>
                        <Label>Title</Label>
                        <Input
                          value={title}
                          onChange={(e) => setTitle(e.target.value)}
                          placeholder="Enter prompt title"
                        />
                      </div>
                      <div>
                        <Label>Description</Label>
                        <Input
                          value={description}
                          onChange={(e) => setDescription(e.target.value)}
                          placeholder="Enter prompt description"
                        />
                      </div>
                      <div>
                        <Label>Type</Label>
                        <select
                          className="w-full p-2 border rounded"
                          value={type}
                          onChange={(e) => setType(e.target.value as PromptType)}
                        >
                          {promptTypes.map((type) => (
                            <option key={type} value={type}>
                              {type}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="placeholder-info border p-4 rounded-md bg-muted/30 my-4">
                        <h3 className="font-medium text-base mb-2">Использование переменных в шаблоне</h3>
                        <p className="text-sm text-muted-foreground mb-2">Вы можете использовать следующие переменные для динамической вставки информации о лиде:</p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                          <div>
                            <h4 className="font-medium text-sm mb-1">Основные переменные:</h4>
                            <ul className="list-disc pl-4 text-sm space-y-1">
                              <li><code className="bg-muted px-1 rounded">{'{{leadFirstName}}'}</code> - имя лида</li>
                              <li><code className="bg-muted px-1 rounded">{'{{leadLastName}}'}</code> - фамилия лида</li>
                              <li><code className="bg-muted px-1 rounded">{'{{leadEmail}}'}</code> - email лида</li>
                              <li><code className="bg-muted px-1 rounded">{'{{leadCompany}}'}</code> - компания лида</li>
                              <li><code className="bg-muted px-1 rounded">{'{{lead_details}}'}</code> - детали о лиде</li>
                              <li><code className="bg-muted px-1 rounded">{'{{lead_history}}'}</code> - история лида</li>
                              <li><code className="bg-muted px-1 rounded">{'{{company_info}}'}</code> - информация о компании</li>
                              <li><code className="bg-muted px-1 rounded">{'{{senderName}}'}</code> - имя отправителя</li>
                            </ul>
                          </div>
                          
                          <div>
                            <h4 className="font-medium text-sm mb-1">JSON-поля (динамические):</h4>
                            <ul className="list-disc pl-4 text-sm space-y-1">
                              <li><code className="bg-muted px-1 rounded">{'{{details_*}}'}</code> - поля из details 
                                <div className="text-xs text-muted-foreground ml-5 mt-1">
                                  Примеры: <code className="bg-muted px-1 rounded">details_city</code>, <code className="bg-muted px-1 rounded">details_role</code>
                                </div>
                              </li>
                              <li><code className="bg-muted px-1 rounded">{'{{communications_*}}'}</code> - поля из communications 
                                <div className="text-xs text-muted-foreground ml-5 mt-1">
                                  Примеры: <code className="bg-muted px-1 rounded">communications_linkedin</code>
                                </div>
                              </li>
                              <li><code className="bg-muted px-1 rounded">{'{{company_*}}'}</code> - поля из company_info 
                                <div className="text-xs text-muted-foreground ml-5 mt-1">
                                  Примеры: <code className="bg-muted px-1 rounded">company_name</code>, <code className="bg-muted px-1 rounded">company_website</code>
                                </div>
                              </li>
                            </ul>
                          </div>
                        </div>
                        
                        <h4 className="font-medium text-sm mt-4 mb-2">Примеры использования:</h4>
                        <div className="bg-muted p-3 rounded-md text-sm whitespace-pre-wrap">
                          <HighlightVariables>
                          {`Здравствуйте, {{leadFirstName}} {{leadLastName}}, 

Я заметил, что вы работаете в {{leadCompany}} и находитесь в городе {{details_city}}.

Мы нашли ваш профиль в {{communications_linkedin}} и хотели бы предложить вам наши услуги.

С уважением,
{{senderName}}`}
                          </HighlightVariables>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <Label>Content</Label>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="previewMode"
                              checked={previewMode}
                              onCheckedChange={setPreviewMode}
                            />
                            <Label htmlFor="previewMode" className="text-sm cursor-pointer">
                              {previewMode ? "Режим просмотра" : "Режим редактирования"}
                            </Label>
                          </div>
                        </div>
                        
                        {previewMode ? (
                          <div className="bg-white dark:bg-gray-900 border rounded-lg p-3 min-h-[200px] overflow-auto">
                            <HighlightVariables>{content}</HighlightVariables>
                          </div>
                        ) : (
                          <Textarea
                            value={content}
                            onChange={handleContentChange}
                            placeholder="Enter prompt content"
                            className="min-h-[200px]"
                          />
                        )}
                      </div>
                      <Button 
                        onClick={isEditing ? handleUpdate : handleCreate}
                        disabled={!title || !content || createPrompt.isPending || updatePrompt.isPending}
                      >
                        {isEditing ? (
                          updatePrompt.isPending ? "Updating..." : "Update Prompt"
                        ) : (
                          createPrompt.isPending ? "Creating..." : "Create Prompt"
                        )}
                      </Button>
                    </div>
                  </TabsContent>

                  <TabsContent value="test" className="space-y-4">
                    <PromptTester
                      content={selectedPrompt.content}
                      onTest={handleTest}
                    />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          ) : (
            isEditing && (
              <Card>
                <CardHeader>
                  <CardTitle>Create New Prompt</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Label>Title</Label>
                      <Input
                        value={title}
                        onChange={(e) => setTitle(e.target.value)}
                        placeholder="Enter prompt title"
                      />
                    </div>
                    <div>
                      <Label>Description</Label>
                      <Input
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        placeholder="Enter prompt description"
                      />
                    </div>
                    <div>
                      <Label>Type</Label>
                      <select
                        className="w-full p-2 border rounded"
                        value={type}
                        onChange={(e) => setType(e.target.value as PromptType)}
                      >
                        {promptTypes.map((type) => (
                          <option key={type} value={type}>
                            {type}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <Label>Content</Label>
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="previewModeNew"
                            checked={previewMode}
                            onCheckedChange={setPreviewMode}
                          />
                          <Label htmlFor="previewModeNew" className="text-sm cursor-pointer">
                            {previewMode ? "Режим просмотра" : "Режим редактирования"}
                          </Label>
                        </div>
                      </div>
                      
                      {previewMode ? (
                        <div className="bg-white dark:bg-gray-900 border rounded-lg p-3 min-h-[200px] overflow-auto">
                          <HighlightVariables>{content}</HighlightVariables>
                        </div>
                      ) : (
                        <Textarea
                          value={content}
                          onChange={handleContentChange}
                          placeholder="Enter prompt content"
                          className="min-h-[200px]"
                        />
                      )}
                    </div>
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setIsEditing(false);
                          setTitle("");
                          setDescription("");
                          setContent("");
                          setType("system");
                        }}
                      >
                        Cancel
                      </Button>
                      <Button 
                        onClick={handleCreate}
                        disabled={!title || !content || createPrompt.isPending}
                      >
                        {createPrompt.isPending ? "Creating..." : "Create Prompt"}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          )}
        </div>
      </div>
      <Dialog open={!!promptToDelete} onOpenChange={() => setPromptToDelete(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Prompt</DialogTitle>
            <DialogDescription>
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-3">
            <p>Are you sure you want to delete this prompt?</p>
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => setPromptToDelete(null)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                if (promptToDelete) {
                  handleDelete(promptToDelete);
                  setPromptToDelete(null);
                }
              }}
            >
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

================
File: src/app/register/form.tsx
================
"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { FormEvent } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function Form() {
  const router = useRouter();

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const response = await fetch(`/api/auth/register`, {
      method: "POST",
      body: JSON.stringify({
        email: formData.get("email"),
        password: formData.get("password"),
        name: formData.get("name"),
      }),
    });

    if (response.ok) {
      router.push("/login");
    } else {
      const data = await response.json();
      console.error('Registration error:', data);
    }
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="name" className="text-sm font-medium">
            Name
          </label>
          <Input
            id="name"
            type="text"
            name="name"
            placeholder="Enter your name"
            required
          />
        </div>
        <div className="space-y-2">
          <label htmlFor="email" className="text-sm font-medium">
            Email
          </label>
          <Input
            id="email"
            type="email"
            name="email"
            placeholder="Enter your email"
            required
          />
        </div>
        <div className="space-y-2">
          <label htmlFor="password" className="text-sm font-medium">
            Password
          </label>
          <Input
            id="password"
            type="password"
            name="password"
            placeholder="Enter your password"
            required
          />
        </div>
        <Button type="submit" className="w-full">
          Register
        </Button>
      </form>

      <div className="text-center text-sm">
        Already have an account?{" "}
        <Link href="/login" className="text-primary hover:underline">
          Login
        </Link>
      </div>
    </div>
  );
}

================
File: src/app/register/page.tsx
================
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Form from "./form";

export default function RegisterPage() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">
            Register
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Form />
        </CardContent>
      </Card>
    </div>
  );
}

================
File: src/app/settings/page.tsx
================
"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/useToast";
import { Loader2, Plus, Pencil, Trash2 } from "lucide-react";
import { useSession } from "next-auth/react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

type Tab = "profile" | "email";

interface EmailConfig {
  id: string;
  smtp: string;
  port: number;
  email: string;
  createdAt: string;
}

const fetchProfile = async () => {
  const response = await fetch("/api/v1/settings/profile");
  if (!response.ok) throw new Error("Failed to fetch profile");
  return response.json();
};

const fetchEmailConfigs = async () => {
  const response = await fetch("/api/v1/settings/email");
  if (!response.ok) throw new Error("Failed to fetch email configs");
  const data = await response.json();
  return Array.isArray(data) ? data : data ? [data] : [];
};

export default function SettingsPage() {
  const { data: session } = useSession();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  const [activeTab, setActiveTab] = useState<Tab>("profile");
  const [isEditing, setIsEditing] = useState(false);
  const [selectedConfig, setSelectedConfig] = useState<EmailConfig | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Form States
  const [profileFormData, setProfileFormData] = useState({
    fullName: "",
    company: "",
    email: "",
    password: "",
  });

  const [emailFormData, setEmailFormData] = useState({
    smtp: "",
    port: "",
    email: "",
    password: "",
  });

  // Queries
  const { data: profile, isLoading: profileLoading } = useQuery({
    queryKey: ["profile"],
    queryFn: fetchProfile,
    placeholderData: session?.user ? {
      fullName: session.user.name || "",
      company: session.user.company || "",
      email: session.user.email || "",
    } : undefined,
  });

  const { data: emailConfigs = [], isLoading: emailConfigsLoading } = useQuery({
    queryKey: ["emailConfigs"],
    queryFn: fetchEmailConfigs,
    enabled: activeTab === "email",
  });

  // Mutations
  const { mutate: updateProfile, isPending: isProfileUpdating } = useMutation({
    mutationFn: async (data: typeof profileFormData) => {
      const response = await fetch("/api/v1/settings/profile", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error("Failed to update profile");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["profile"] });
      setLastUpdated(new Date());
      toast({
        title: "Success",
        description: "Profile settings updated successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to save profile settings",
        variant: "destructive",
      });
    },
  });

  const { mutate: saveEmailConfig, isPending: isEmailSaving } = useMutation({
    mutationFn: async (data: typeof emailFormData) => {
      const response = await fetch("/api/v1/settings/email", {
        method: selectedConfig ? "PUT" : "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...data,
          port: parseInt(data.port),
          id: selectedConfig?.id,
        }),
      });
      if (!response.ok) throw new Error("Failed to save email configuration");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["emailConfigs"] });
      toast({
        title: "Success",
        description: "Email configuration saved successfully",
      });
      resetEmailForm();
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to save email configuration",
        variant: "destructive",
      });
    },
  });

  const { mutate: deleteEmailConfig } = useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/v1/settings/email/${id}`, {
        method: "DELETE",
      });
      if (!response.ok) throw new Error("Failed to delete email configuration");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["emailConfigs"] });
      toast({
        title: "Success",
        description: "Email configuration deleted successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to delete email configuration",
        variant: "destructive",
      });
    },
  });

  // Effects
  useEffect(() => {
    if (profile) {
      setProfileFormData({
        fullName: profile.fullName || "",
        company: profile.company || "",
        email: profile.email || "",
        password: "",
      });
    }
  }, [profile]);

  // Handlers
  const handleProfileSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateProfile(profileFormData);
  };

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    saveEmailConfig(emailFormData);
  };

  const handleEdit = (config: EmailConfig) => {
    setSelectedConfig(config);
    setEmailFormData({
      smtp: config.smtp,
      port: config.port.toString(),
      email: config.email,
      password: "",
    });
    setIsEditing(true);
  };

  const resetEmailForm = () => {
    setEmailFormData({
      smtp: "",
      port: "",
      email: "",
      password: "",
    });
    setIsEditing(false);
    setSelectedConfig(null);
  };

  const isOAuthUser = session?.user?.provider === "oauth";

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Settings</h1>

      <div className="flex space-x-2 mb-6">
        <Button
          variant={activeTab === "profile" ? "default" : "outline"}
          onClick={() => setActiveTab("profile")}
        >
          Profile
        </Button>
        <Button
          variant={activeTab === "email" ? "default" : "outline"}
          onClick={() => setActiveTab("email")}
        >
          Email Configuration
        </Button>
      </div>

      <div className="space-y-6">
        {activeTab === "profile" && (
          <Card>
            <CardHeader>
              <CardTitle>Profile Settings</CardTitle>
              {lastUpdated && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <div className="flex h-2 w-2 mr-2 relative">
                    <div className="animate-ping absolute inline-flex h-2 w-2 rounded-full bg-green-400 opacity-75"></div>
                    <div className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></div>
                  </div>
                  Last updated: {lastUpdated.toLocaleTimeString()}
                </div>
              )}
            </CardHeader>
            <CardContent>
              <form onSubmit={handleProfileSubmit} className="space-y-4">
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="fullName">Full Name</Label>
                    <Input
                      id="fullName"
                      placeholder="Enter Full Name"
                      value={profileFormData.fullName}
                      onChange={(e) =>
                        setProfileFormData((prev) => ({
                          ...prev,
                          fullName: e.target.value,
                        }))
                      }
                      disabled={profileLoading || isProfileUpdating}
                      className={profileLoading ? "bg-muted" : ""}
                      required
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="company">Company</Label>
                    <Input
                      id="company"
                      placeholder="Enter Company Name"
                      value={profileFormData.company}
                      onChange={(e) =>
                        setProfileFormData((prev) => ({
                          ...prev,
                          company: e.target.value,
                        }))
                      }
                      disabled={profileLoading || isProfileUpdating}
                      className={profileLoading ? "bg-muted" : ""}
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="profileEmail">Email</Label>
                    <Input
                      id="profileEmail"
                      type="email"
                      placeholder="Enter Email"
                      value={profileFormData.email}
                      onChange={(e) =>
                        setProfileFormData((prev) => ({
                          ...prev,
                          email: e.target.value,
                        }))
                      }
                      disabled={profileLoading || isProfileUpdating}
                      className={profileLoading ? "bg-muted" : ""}
                      required
                    />
                  </div>

                  {!isOAuthUser && (
                    <div className="grid gap-2">
                      <Label htmlFor="profilePassword">Change Password</Label>
                      <Input
                        id="profilePassword"
                        type="password"
                        placeholder="Enter New Password"
                        value={profileFormData.password}
                        onChange={(e) =>
                          setProfileFormData((prev) => ({
                            ...prev,
                            password: e.target.value,
                          }))
                        }
                        disabled={profileLoading || isProfileUpdating}
                        className={profileLoading ? "bg-muted" : ""}
                      />
                    </div>
                  )}
                </div>

                <Button 
                  type="submit" 
                  disabled={profileLoading || isProfileUpdating}
                  className="w-full sm:w-auto"
                >
                  {profileLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Loading...
                    </>
                  ) : isProfileUpdating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        )}

        {activeTab === "email" && (
          <>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Email Configurations</CardTitle>
                <Button onClick={() => setIsEditing(true)} className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add New
                </Button>
              </CardHeader>
              <CardContent>
                {emailConfigsLoading ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : emailConfigs.length === 0 ? (
                  <p className="text-muted-foreground text-center py-4">
                    No email configurations found. Add one to get started.
                  </p>
                ) : (
                  <div className="space-y-4">
                    {emailConfigs.map((config) => (
                      <div
                        key={config.id}
                        className="flex items-center justify-between p-4 border rounded-lg"
                      >
                        <div>
                          <p className="font-medium">{config.email}</p>
                          <p className="text-sm text-muted-foreground">
                            {config.smtp}:{config.port}
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(config)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => deleteEmailConfig(config.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {isEditing && (
              <Card>
                <CardHeader>
                  <CardTitle>
                    {selectedConfig ? "Edit Email Configuration" : "Add Email Configuration"}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleEmailSubmit} className="space-y-4">
                    <div className="grid gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="smtp">SMTP Server</Label>
                        <Input
                          id="smtp"
                          placeholder="smtp.example.com"
                          value={emailFormData.smtp}
                          onChange={(e) =>
                            setEmailFormData((prev) => ({
                              ...prev,
                              smtp: e.target.value,
                            }))
                          }
                          required
                        />
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="port">Port</Label>
                        <Input
                          id="port"
                          type="number"
                          placeholder="587"
                          value={emailFormData.port}
                          onChange={(e) =>
                            setEmailFormData((prev) => ({
                              ...prev,
                              port: e.target.value,
                            }))
                          }
                          required
                        />
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={emailFormData.email}
                          onChange={(e) =>
                            setEmailFormData((prev) => ({
                              ...prev,
                              email: e.target.value,
                            }))
                          }
                          required
                        />
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="password">Password</Label>
                        <Input
                          id="password"
                          type="password"
                          value={emailFormData.password}
                          onChange={(e) =>
                            setEmailFormData((prev) => ({
                              ...prev,
                              password: e.target.value,
                            }))
                          }
                          required={!selectedConfig}
                          placeholder={
                            selectedConfig ? "Enter new password to update" : "Enter password"
                          }
                        />
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button type="submit" className="flex-1" disabled={isEmailSaving}>
                        {isEmailSaving ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          "Save Configuration"
                        )}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={resetEmailForm}
                      >
                        Cancel
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}
          </>
        )}
      </div>
    </div>
  );
}

================
File: src/app/test-functions/page.tsx
================
"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";

export default function TestFunctions() {
  const [result, setResult] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [leadId, setLeadId] = useState<string>("");

  const handleTest = async (endpoint: string) => {
    setLoading(true);
    try {
      const response = await fetch(endpoint);
      const data = await response.json();
      setResult(JSON.stringify(data, null, 2));
    } catch (error) {
      setResult(error instanceof Error ? error.message : "An error occurred");
    }
    setLoading(false);
  };

  const handleLeadTest = async (endpoint: string) => {
    if (!leadId) {
      setResult("Please enter a lead ID");
      return;
    }
    setLoading(true);
    try {
      const response = await fetch(`${endpoint}?salesforceId=${leadId}`);
      const data = await response.json();
      setResult(JSON.stringify(data, null, 2));
    } catch (error) {
      setResult(error instanceof Error ? error.message : "An error occurred");
    }
    setLoading(false);
  };

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">Test Functions</h1>

      <div className="mb-6">
        <Input
          type="text"
          placeholder="Enter Lead ID"
          value={leadId}
          onChange={(e) => setLeadId(e.target.value)}
          className="max-w-md"
        />
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Salesforce Functions</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-wrap gap-4">
            <Button
              onClick={() => handleTest("/api/test/leads")}
              disabled={loading}
              variant="default"
            >
              Get Leads
            </Button>
            <Button
              onClick={() => handleLeadTest("/api/test/leads")}
              disabled={loading}
              variant="default"
            >
              Get Lead by ID
            </Button>
            <Button
              onClick={() => handleLeadTest("/api/test/leads/history")}
              disabled={loading}
              variant="default"
            >
              Get Lead History
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Email Functions</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-wrap gap-4">
            <Button
              onClick={() => handleLeadTest("/api/test/email/generate")}
              disabled={loading}
              variant="secondary"
            >
              Generate Email
            </Button>
            <Button
              onClick={() => handleLeadTest("/api/test/email/send")}
              disabled={loading}
              variant="secondary"
            >
              Send Email
            </Button>
          </CardContent>
        </Card>
      </div>

      {loading && (
        <div className="mt-6">
          <Skeleton className="h-[200px] w-full" />
        </div>
      )}

      {result && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Result</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertDescription>
                <pre className="whitespace-pre-wrap overflow-auto max-h-[400px]">
                  {result}
                </pre>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

================
File: src/app/layout.tsx
================
import type { Metadata } from "next";
import { GeistSans } from "geist/font/sans";
import { GeistMono } from "geist/font/mono";
import "./globals.css";
import Navigation from "@/components/navigation";
import { Toaster } from "@/components/ui/toaster";
import { getServerSession } from "next-auth";
import { Providers } from "@/providers/providers";
import { cn } from "@/lib/utils";

export const metadata: Metadata = {
  title: "Email Generator",
  description: "AI-powered email generation for Salesforce leads",
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerSession();

  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${GeistSans.variable} ${GeistMono.variable} min-h-screen bg-background font-sans antialiased`}
      >
        <Providers session={session}>
          <Navigation />
          <main className={cn(
            "container",
            "mx-auto pt-16"
          )}>{children}</main>
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}

================
File: src/app/page.tsx
================
"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Mail,
  Send,
  Trash2,
  FileEdit,
  RefreshCw,
  Loader2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSession } from "next-auth/react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { AgentSelect } from "@/components/agents/AgentSelect";
import { useToast } from "@/components/ui/use-toast";

interface Lead {
  id: number;
  salesforceId: string;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  company: string | null;
  title: string | null;
  phone: string | null;
  status: string | null;
  industry: string | null;
  rating: string | null;
  leadSource: string | null;
  description: string | null;
  website: string | null;
  numberOfEmployees: number | null;
  lastModifiedDate: string | null;
  communications: any | null;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

interface Email {
  id: number;
  subject: string;
  content: string;
  sent: boolean;
  createdAt: string;
}

interface Agent {
  id: number;
  name: string;
  description: string;
  type: string;
  settings: Record<string, any>;
}

interface EmailConfig {
  id: string;
  smtp: string;
  port: number;
  email: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export default function Home() {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);
  const [emails, setEmails] = useState<Email[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [emailConfigs, setEmailConfigs] = useState<EmailConfig[]>([]);
  const [selectedEmailConfig, setSelectedEmailConfig] = useState<string>("");
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const [emailToSend, setEmailToSend] = useState<number | null>(null);
  const [editingEmail, setEditingEmail] = useState<number | null>(null);
  const [editedSubject, setEditedSubject] = useState("");
  const [editedContent, setEditedContent] = useState("");
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [extraPrompt, setExtraPrompt] = useState<string>("");
  const session = useSession()
  const itemsPerPage = 10;
  const { toast } = useToast();
  const [isGenerating, setIsGenerating] = useState(false);

  const fetchEmailConfigs = useCallback(async () => {
    try {
      if (session.data?.user?.email) {
        const response = await fetch("/api/v1/settings/email");
        const data = await response.json();
        setEmailConfigs(Array.isArray(data) ? data : []);
      } else {
        setEmailConfigs([])
      }
    } catch (error) {
      console.error("Error fetching email configs:", error);
      setEmailConfigs([]);
    }
  }, [session.data?.user?.email]);

  useEffect(() => {
    fetchLeads();
    fetchEmailConfigs();
  }, [fetchEmailConfigs]);

  useEffect(() => {
    if (selectedLead) {
      fetchEmails(selectedLead.id.toString());
    }
  }, [selectedLead]);



  const fetchLeads = async () => {
    try {
      const response = await fetch("/api/leads");
      const data = await response.json();
      setLeads(Array.isArray(data.leads) ? data.leads : []);
    } catch (error) {
      console.error("Error fetching leads:", error);
      setLeads([]);
    }
  };

  const syncLeads = async () => {
    setLoading(true);
    try {
      await fetch("/api/leads", {
        method: "POST",
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'sync' }),
      });
      await fetchLeads();
    } catch (error) {
      console.error("Error syncing leads:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchEmails = async (leadId: string) => {
    try {
      const response = await fetch(`/api/emails?leadId=${leadId}`);
      const data = await response.json();
      // Сортируем письма по дате создания в обратном порядке (новые сверху)
      const sortedEmails = Array.isArray(data) ? 
        data.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()) : 
        [];
      setEmails(sortedEmails);
    } catch (error) {
      console.error("Error fetching emails:", error);
      setEmails([]);
    }
  };

  const handleSendClick = (emailId: number) => {
    setEmailToSend(emailId);
    setShowEmailDialog(true);
  };

  const sendEmail = async (emailId?: number) => {
    setLoading(true);
    try {
      if (session.data?.user?.email) {
        const response = await fetch("/api/test/email/send", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            emailId: emailToSend,
            emailConfigId: selectedEmailConfig,
          }),
        });

        if (!response.ok) throw new Error("Failed to send email");

        setEmails((prev) =>
          prev.map((email) =>
            email.id === emailToSend ? { ...email, sent: true } : email
          )
        );

        setShowEmailDialog(false);
        setSelectedEmailConfig("");
        setEmailToSend(null);
      } else {
        const response = await fetch("/api/test/email/send", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            emailId,
          }),
        });

        if (!response.ok) throw new Error("Failed to send email");

        setEmails((prev) =>
          prev.map((email) =>
            email.id === emailId ? { ...email, sent: true } : email
          )
        );
      }
    } catch (error) {
      console.error("Error sending email:", error);
    } finally {
      setLoading(false);
    }
  };

//   const updateLead = async (leadId: number, data: Partial<Lead>) => {
//     setLoading(true);
//     try {
//       const response = await fetch(`/api/leads/${leadId}`, {
//         method: 'PUT',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify(data),
//       });
      
//       if (!response.ok) {
//         throw new Error('Failed to update lead');
//       }
      
//       const updatedLead = await response.json();
//       setLeads(leads.map(lead => lead.id === leadId ? updatedLead : lead));
//       setSelectedLead(updatedLead);
//     } catch (error) {
//       console.error('Error updating lead:', error);
//     } finally {
//       setLoading(false);
//     }
//   };

  const handleEditClick = (email: Email) => {
    setEditingEmail(email.id);
    setEditedSubject(email.subject);
    setEditedContent(email.content);
  };

  const handleSaveEdit = async (emailId: number) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/emails/${emailId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subject: editedSubject,
          content: editedContent,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update email');
      }

      const updatedEmail = await response.json();
      setEmails(emails.map(email => 
        email.id === emailId ? updatedEmail : email
      ).sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));
      setEditingEmail(null);
    } catch (error) {
      console.error('Error updating email:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingEmail(null);
    setEditedSubject("");
    setEditedContent("");
  };

  const handleDeleteEmail = async (emailId: number) => {
    if (!confirm("Вы уверены, что хотите удалить это письмо?")) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/emails/${emailId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete email');
      }

      setEmails(emails.filter(email => email.id !== emailId));
      toast({
        title: "Успех",
        description: "Письмо успешно удалено",
      });
    } catch (error) {
      console.error('Error deleting email:', error);
      toast({
        title: "Ошибка",
        description: "Не удалось удалить письмо",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const totalPages = Math.ceil(leads.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentLeads = leads.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setSelectedLead(null);
  };

  const handleGenerateEmail = async () => {
    if (!selectedLead || !selectedAgent) {
      toast({
        title: "Ошибка",
        description: "Пожалуйста, выберите лид и агента для генерации",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsGenerating(true);
      const response = await fetch("/api/v1/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          leadId: selectedLead.id,
          agentId: selectedAgent.id,
          extraPrompt,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Ошибка при генерации email");
      }

      const generatedEmail = await response.json();
      setEmails((prevEmails) => [...prevEmails, generatedEmail]);
      
      toast({
        title: "Успех",
        description: "Email успешно сгенерирован",
      });
    } catch (error) {
      console.error("Error generating email:", error);
      toast({
        title: "Ошибка",
        description: error instanceof Error ? error.message : "Произошла ошибка при генерации email",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Salesforce Lead Email Generator</h1>
        <Button onClick={syncLeads} disabled={loading} className="gap-2">
          <RefreshCw
            className={`h-5 w-5 ${loading ? "animate-spin" : ""}`}
          />
          Sync Leads
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Leads List */}
        <Card>
          <CardHeader>
            <CardTitle>Leads</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {currentLeads.map((lead) => (
              <div
                key={lead.id}
                onClick={() => setSelectedLead(lead)}
                className={`p-3 rounded-lg cursor-pointer transition-colors ${
                  selectedLead?.id === lead.id
                    ? "bg-accent"
                    : "hover:bg-accent/50"
                }`}
              >
                <h3 className="font-medium">
                  {lead.firstName} {lead.lastName}
                </h3>
                <p className="text-sm text-muted-foreground">{lead.company}</p>
                <p className="text-sm text-muted-foreground">{lead.email}</p>
              </div>
            ))}
            {/* Pagination Controls */}
            {leads.length > itemsPerPage && (
              <div className="flex items-center justify-between mt-4 pt-4 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="text-sm text-muted-foreground">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            )}
            {leads.length === 0 && (
              <p className="text-muted-foreground">
                No leads found (please sync)
              </p>
            )}
          </CardContent>
        </Card>

        {/* Lead Details & Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Lead Details</CardTitle>
          </CardHeader>
          <CardContent>
            {selectedLead ? (
              <div>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">First Name</Label>
                      <Input
                        id="firstName"
                        value={selectedLead.firstName || ''}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                          setSelectedLead({...selectedLead, firstName: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input
                        id="lastName"
                        value={selectedLead.lastName || ''}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                          setSelectedLead({...selectedLead, lastName: e.target.value})}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      value={selectedLead.email || ''}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                        setSelectedLead({...selectedLead, email: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="company">Company</Label>
                    <Input
                      id="company"
                      value={selectedLead.company || ''}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                        setSelectedLead({...selectedLead, company: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      value={selectedLead.title || ''}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                        setSelectedLead({...selectedLead, title: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="status">Status</Label>
                    <Input
                      id="status"
                      value={selectedLead.status || ''}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                        setSelectedLead({...selectedLead, status: e.target.value})}
                    />
                  </div>
                  <div>
                    <AgentSelect
                      selectedId={selectedAgent?.id}
                      onSelect={(agent) => setSelectedAgent(agent)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="description">Extra Prompt</Label>
                    <Textarea
                      id="description"
                      value={extraPrompt || ''}
                      onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => 
                        setExtraPrompt(e.target.value)}
                      className="min-h-[200px]"
                    />
                  </div>
                </div>
                <div className="mt-6 flex justify-between">
                  <Button
                    onClick={handleGenerateEmail}
                    disabled={isGenerating || !selectedAgent}
                    className="gap-2"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="h-5 w-5 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Mail className="h-5 w-5" />
                        Generate Email
                      </>
                    )}
                  </Button>
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">
                Select a lead to view details
              </p>
            )}
          </CardContent>
        </Card>

        {/* Generated Emails */}
        <Card>
          <CardHeader>
            <CardTitle>Generated Emails</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {emails.map((email) => (
              <Card key={email.id}>
                <CardContent className="pt-6">
                  {editingEmail === email.id ? (
                    // Edit mode
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="subject">Subject</Label>
                        <Input
                          id="subject"
                          value={editedSubject}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                            setEditedSubject(e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="content">Content</Label>
                        <Textarea
                          id="content"
                          value={editedContent}
                          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => 
                            setEditedContent(e.target.value)}
                          className="min-h-[200px]"
                        />
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCancelEdit()}
                          disabled={loading}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Cancel
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleSaveEdit(email.id)}
                          disabled={loading}
                        >
                          <FileEdit className="h-4 w-4 mr-1" />
                          Save
                        </Button>
                      </div>
                    </div>
                  ) : (
                    // View mode
                    <>
                      <h3 className="font-medium">{email.subject}</h3>
                      <p className="text-sm text-muted-foreground mt-2 whitespace-pre-wrap">
                        {email.content}
                      </p>
                      <div className="mt-2 flex justify-between items-center text-sm text-muted-foreground">
                        <span>{new Date(email.createdAt).toLocaleDateString()}</span>
                        <div className="flex items-start gap-2">
                          <Badge variant={email.sent ? "default" : "secondary"}>
                            {email.sent ? "Sent" : "Draft"}
                          </Badge>
                          </div>
                          </div>
                        <div className="mt-2 flex justify-between items-center text-sm text-muted-foreground">
                        <div className="flex items-center gap-2">
                          {!email.sent && (
                            <>
                            <div className="flex flex-wrap gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEditClick(email)}
                                disabled={loading}
                                className="flex items-center gap-1 sm:w-auto w-full"
                              >
                                <FileEdit className="h-4 w-4" />
                                <span className="sm:inline hidden">Edit</span>
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  session.data?.user?.email
                                    ? handleSendClick(email.id)
                                    : sendEmail(email.id)
                                }
                                disabled={loading}
                                className="flex items-center gap-1 sm:w-auto w-full"
                              >
                                <Send className="h-4 w-4" />
                                <span className="sm:inline hidden">Send</span>
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteEmail(email.id)}
                                disabled={loading}
                                className="flex items-center gap-1 sm:w-auto w-full"
                              >
                                <Trash2 className="h-4 w-4" />
                                <span className="sm:inline hidden">Delete</span>
                              </Button>
                            </div>
                            </>
                          )}
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            ))}
            {emails.length === 0 && selectedLead && (
              <p className="text-muted-foreground">No emails generated yet</p>
            )}
            {!selectedLead && (
              <p className="text-muted-foreground">Select a lead to view emails</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Email Selection Dialog */}
      <Dialog open={showEmailDialog} onOpenChange={setShowEmailDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Select Email to Send From</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Select value={selectedEmailConfig} onValueChange={setSelectedEmailConfig}>
              <SelectTrigger>
                <SelectValue placeholder="Select an email" />
              </SelectTrigger>
              <SelectContent>
                {session.data?.user?.email && (
                  <SelectItem value="logged_in">
                    {session.data.user.email} (Logged In)
                  </SelectItem>
                )}
                {emailConfigs.map((config) => (
                  <SelectItem key={config.id} value={config.id}>
                    {config.email}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="mt-4 flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowEmailDialog(false)}>
                Cancel
              </Button>
              <Button onClick={() => sendEmail()} disabled={!selectedEmailConfig || loading}>
                Send Email
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

================
File: src/components/agents/AgentForm.tsx
================
'use client';

import { useState, useEffect } from 'react';
import { Agent, CreateAgentInput, LLMModel } from '@/types/agent';
import { Prompt } from '@/db/schema/prompts';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface AgentFormProps {
  initialData?: Partial<Agent>;
  onSubmit: (data: CreateAgentInput) => Promise<void>;
  onDelete?: () => Promise<void>;
  isLoading?: boolean;
}

export function AgentForm({ initialData, onSubmit, onDelete, isLoading = false }: AgentFormProps) {
  const { toast } = useToast();
  const [formData, setFormData] = useState<CreateAgentInput>({
    name: initialData?.name || '',
    description: initialData?.description || '',
    prompt: initialData?.prompt || '',
    promptId: initialData?.promptId,
    settings: initialData?.settings || {
      llmProvider: 'openai',
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 2000,
    },
    isActive: initialData?.isActive ?? true,
  });

  const [prompts, setPrompts] = useState<Prompt[]>([]);
//   const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchPrompts();
  }, []);

  const fetchPrompts = async () => {
    try {
      const response = await fetch('/api/v1/prompts');
      if (!response.ok) throw new Error('Failed to fetch prompts');
      const data = await response.json();
      setPrompts(data.prompts || []);
    } catch (error) {
      console.error('Error fetching prompts:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch prompts',
        variant: 'destructive',
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await onSubmit(formData);
      toast({
        title: 'Success',
        description: `Agent ${initialData ? 'updated' : 'created'} successfully`,
      });
    } catch (error) {
      console.error('Form submission error:', error);
      toast({
        title: 'Error',
        description: 'Failed to save agent. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleDelete = async () => {
    if (!onDelete) return;
    try {
      await onDelete();
      toast({
        title: 'Success',
        description: 'Agent deleted successfully',
      });
    } catch (error) {
      console.error('Delete error:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete agent. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const llmProviders = [
    { value: 'openai', label: 'OpenAI' },
    { value: 'anthropic', label: 'Anthropic' }
  ];

  const modelsByProvider = {
    openai: [
      { value: 'gpt-4', label: 'GPT-4', maxTokens: 8192 },
      { value: 'gpt-4-turbo', label: 'GPT-4 Turbo', maxTokens: 128000 },
      { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo', maxTokens: 16385 },
      { value: 'chatgpt-4o-latest', label: 'ChatGPT-4O Latest', maxTokens: 128000 },
      { value: 'gpt-4o-mini', label: 'GPT-4O Mini', maxTokens: 128000 },
      { value: 'o1-mini', label: 'O1 Mini', maxTokens: 128000 },
      { value: 'o1-preview', label: 'O1 Preview', maxTokens: 128000 }
    ],
    anthropic: [
      { value: 'claude-3-5-sonnet-20241022', label: 'Claude 3.5 Sonnet', maxTokens: 200000 },
      { value: 'claude-3-5-haiku-20241022', label: 'Claude 3.5 Haiku', maxTokens: 200000 }
    ]
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div>
          <Label htmlFor="name">Name</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="Agent name"
            disabled={isLoading}
          />
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            placeholder="Agent description"
            disabled={isLoading}
          />
        </div>

        <div>
          <Label htmlFor="promptId">Base Prompt</Label>
          <Select
            value={formData.promptId?.toString() || "none"}
            onValueChange={(value) => {
              if (value === "none") {
                setFormData({
                  ...formData,
                  promptId: undefined,
                  prompt: ""
                });
                return;
              }
              const selectedPrompt = prompts.find(p => p.id === parseInt(value));
              setFormData({
                ...formData,
                promptId: parseInt(value),
                prompt: selectedPrompt?.content || ""
              });
            }}
            disabled={isLoading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a base prompt" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">No base prompt</SelectItem>
              {prompts.map((prompt) => (
                <SelectItem key={prompt.id} value={prompt.id.toString()}>
                  {prompt.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="prompt">Prompt</Label>
          <Textarea
            id="prompt"
            value={formData.prompt}
            readOnly
            placeholder="Prompt will be shown here"
            className="h-48 bg-muted"
          />
        </div>

        <div>
          <Label htmlFor="llmProvider">LLM Provider</Label>
          <Select
            value={formData.settings.llmProvider}
            onValueChange={(value) => setFormData({
              ...formData,
              settings: { ...formData.settings, llmProvider: value as 'openai' | 'anthropic' }
            })}
            disabled={isLoading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select LLM provider" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="openai">OpenAI</SelectItem>
              <SelectItem value="anthropic">Anthropic</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="model">Model</Label>
          <Select
            value={formData.settings.model}
            onValueChange={(value) => {
              const selectedModel = modelsByProvider[formData.settings.llmProvider]
                .find(model => model.value === value);
              setFormData({
                ...formData,
                settings: {
                  ...formData.settings,
                  model: value as LLMModel,
                  maxTokens: selectedModel?.maxTokens || formData.settings.maxTokens
                }
              });
            }}
            disabled={isLoading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select model" />
            </SelectTrigger>
            <SelectContent>
              {modelsByProvider[formData.settings.llmProvider].map((model) => (
                <SelectItem key={model.value} value={model.value}>
                  {model.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="temperature">Temperature</Label>
          <Input
            id="temperature"
            type="number"
            min="0"
            max="1"
            step="0.1"
            value={formData.settings.temperature}
            onChange={(e) => setFormData({
              ...formData,
              settings: { ...formData.settings, temperature: parseFloat(e.target.value) }
            })}
            disabled={isLoading}
          />
        </div>

        <div>
          <Label htmlFor="maxTokens">Max Tokens</Label>
          <Input
            id="maxTokens"
            type="number"
            min="1"
            max="200000"
            value={formData.settings.maxTokens}
            onChange={(e) => setFormData({
              ...formData,
              settings: { ...formData.settings, maxTokens: parseInt(e.target.value) }
            })}
            disabled={isLoading}
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="isActive"
            checked={formData.isActive}
            onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
            disabled={isLoading}
          />
          <Label htmlFor="isActive">Active</Label>
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        {onDelete && (
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={isLoading}
          >
            Delete
          </Button>
        )}
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : initialData ? 'Update' : 'Create'}
        </Button>
      </div>
    </form>
  );
}

================
File: src/components/agents/AgentSelect.tsx
================
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useQuery } from "@tanstack/react-query"
import { Skeleton } from "@/components/ui/skeleton"
import { useEffect } from "react"

interface Agent {
  id: number
  name: string
  description: string
  type: string
  settings: Record<string, any>
}

interface AgentSelectProps {
  onSelect: (agent: Agent) => void
  selectedId?: number
  disabled?: boolean
}

export function AgentSelect({ onSelect, selectedId, disabled }: AgentSelectProps) {
  const { data, isLoading, error } = useQuery<{ agents: Agent[] }>({
    queryKey: ["agents"],
    queryFn: async () => {
      const response = await fetch("/api/v1/agents")
      if (!response.ok) {
        throw new Error("Ошибка загрузки агентов")
      }
      const data = await response.json()
      return { agents: Array.isArray(data.agents) ? data.agents : [] }
    },
  })

  const agents = data?.agents || []

  useEffect(() => {
    if (agents.length > 0 && !selectedId) {
      onSelect(agents[0])
    }
  }, [agents, selectedId, onSelect])

  if (error) {
    return (
      <Button variant="outline" className="w-full text-destructive">
        Ошибка загрузки агентов
      </Button>
    )
  }

  if (isLoading) {
    return <Skeleton className="h-10 w-full" />
  }

  return (
    <Select
      value={selectedId?.toString() || (agents.length > 0 ? agents[0].id.toString() : undefined)}
      onValueChange={(value) => {
        const agent = agents.find((a) => a.id === parseInt(value))
        if (agent) {
          onSelect(agent)
        }
      }}
      disabled={disabled}
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Select agent.." />
      </SelectTrigger>
      <SelectContent>
        {agents.length === 0 ? (
          <SelectItem value="empty" disabled>
            No agents
          </SelectItem>
        ) : (
          agents.map((agent) => (
            <SelectItem key={agent.id} value={agent.id.toString()}>
              <div className="flex flex-col">
                <span>{agent.name}</span>
                <span className="text-sm text-muted-foreground">
                  {agent.description}
                </span>
              </div>
            </SelectItem>
          ))
        )}
      </SelectContent>
    </Select>
  )
}

================
File: src/components/agents/AgentsList.tsx
================
'use client';

import { Agent } from '@/types/agent';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Edit2, Trash2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface AgentsListProps {
  agents: Agent[];
  onEdit: (agent: Agent) => void;
  onDelete: (agent: Agent) => void;
  isLoading?: boolean;
}

export function AgentsList({ agents, onEdit, onDelete, isLoading = false }: AgentsListProps) {
  if (!agents?.length) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No agents found</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {agents.map((agent) => (
        <Card key={agent.id} className="flex flex-col">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl">{agent.name}</CardTitle>
              <Badge variant={agent.isActive ? 'default' : 'secondary'}>
                {agent.isActive ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            <CardDescription className="mt-2">
              {agent.description || 'No description'}
            </CardDescription>
          </CardHeader>
          <CardContent className="flex-grow">
            <div className="space-y-2">
              <div>
                <span className="text-sm font-medium">Provider:</span>
                <span className="ml-2 text-sm text-gray-500">
                  {agent.settings.llmProvider}
                </span>
              </div>
              <div>
                <span className="text-sm font-medium">Model:</span>
                <span className="ml-2 text-sm text-gray-500">
                  {agent.settings.model}
                </span>
              </div>
              <div>
                <span className="text-sm font-medium">Version:</span>
                <span className="ml-2 text-sm text-gray-500">
                  {agent.version}
                </span>
              </div>
              <div>
                <span className="text-sm font-medium">Updated:</span>
                <span className="ml-2 text-sm text-gray-500">
                  {formatDistanceToNow(new Date(agent.updatedAt), { addSuffix: true })}
                </span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(agent)}
              disabled={isLoading}
            >
              <Edit2 className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => onDelete(agent)}
              disabled={isLoading}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}

================
File: src/components/agents/AgentsManager.tsx
================
'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Agent, CreateAgentInput } from '@/types/agent';
import { AgentsList } from './AgentsList';
import { AgentForm } from './AgentForm';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from '@/components/ui/use-toast';

export function AgentsManager() {
  const { data: session, status } = useSession();
  const [agents, setAgents] = useState<Agent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();

  const fetchAgents = async () => {
    try {
      setIsLoading(true);

      const response = await fetch('/api/v1/agents');
      const data = await response.json();
      
      
      if (!response.ok) {
        if (response.status === 401) {
          toast({
            title: 'Unauthorized',
            description: 'Please sign in to view agents',
            variant: 'destructive',
          });
          return;
        }
        throw new Error(data.error || 'Failed to fetch agents');
      }

      if (Array.isArray(data.agents)) {
        setAgents(data.agents);
      } else {
        console.warn('Unexpected data format:', data);
        setAgents([]);
      }
    } catch (error) {
      console.error('Error fetching agents:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch agents',
        variant: 'destructive',
      });
      setAgents([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (session?.user?.id) {
      fetchAgents();
    }
  }, [session]);

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-gray-500">Loading...</p>
      </div>
    );
  }

  if (status === "unauthenticated") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-gray-500">Please sign in to view agents</p>
      </div>
    );
  }

  const handleCreateAgent = async (data: CreateAgentInput) => {
    try {
      const response = await fetch('/api/v1/agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to create agent');
      }

      await fetchAgents();
      setIsDialogOpen(false);
      toast({
        title: 'Success',
        description: 'Agent created successfully',
      });
    } catch (error) {
      console.error('Error creating agent:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create agent',
        variant: 'destructive',
      });
    }
  };

  const handleEditAgent = async (agent: Agent) => {
    setSelectedAgent(agent);
    setIsDialogOpen(true);
  };

  const handleDeleteAgent = async (agent: Agent) => {
    try {
      const response = await fetch(`/api/v1/agents/${agent.id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete agent');
      }

      await fetchAgents();
      toast({
        title: 'Success',
        description: 'Agent deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting agent:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete agent',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">
          Agents ({agents.length})
        </h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>Create Agent</Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{selectedAgent ? 'Edit Agent' : 'Create Agent'}</DialogTitle>
              <DialogDescription>
                {selectedAgent 
                  ? 'Edit your agent details below.' 
                  : 'Create a new agent by filling out the form below.'}
              </DialogDescription>
            </DialogHeader>
            <AgentForm
              initialData={selectedAgent || undefined}
              onSubmit={handleCreateAgent}
            />
          </DialogContent>
        </Dialog>
      </div>

      {isLoading ? (
        <div className="text-center py-8">
          <p className="text-gray-500">Loading agents...</p>
        </div>
      ) : agents.length > 0 ? (
        <AgentsList
          agents={agents}
          onEdit={handleEditAgent}
          onDelete={handleDeleteAgent}
        />
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500">No agents found. Create your first agent!</p>
        </div>
      )}
    </div>
  );
}

================
File: src/components/auth/sessionProvider.tsx
================
"use client";
import { SessionProvider } from "next-auth/react";
export default SessionProvider;

================
File: src/components/emails/EmailDetailsModal.tsx
================
"use client";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useQuery } from "@tanstack/react-query";

interface EmailDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  emailId: number | null;
}

export function EmailDetailsModal({ isOpen, onClose, emailId }: EmailDetailsModalProps) {
  const { data: email, isLoading } = useQuery({
    queryKey: ["email", emailId],
    queryFn: async () => {
      if (!emailId) return null;
      const response = await fetch(`/api/emails/${emailId}`);
      if (!response.ok) throw new Error("Failed to fetch email");
      return response.json();
    },
    enabled: !!emailId,
  });

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Email Details</DialogTitle>
        </DialogHeader>

        <div className="max-h-[80vh] overflow-hidden flex flex-col">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
            </div>
          ) : email ? (
            <div className="space-y-6 overflow-y-auto pr-2">
              <div>
                <h3 className="text-lg font-medium sticky top-0 bg-white py-2">Generated Email</h3>
                <div className="mt-2 space-y-2">
                  <div className="font-medium">Subject:</div>
                  <div className="p-3 bg-gray-50 rounded-md">{email.subject}</div>
                  <div className="font-medium">Content:</div>
                  <div className="p-3 bg-gray-50 rounded-md whitespace-pre-wrap">
                    {email.content}
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium sticky top-0 bg-white py-2">Prompt Used</h3>
                <div className="mt-2 p-3 bg-gray-50 rounded-md whitespace-pre-wrap">
                  {email.prompt}
                </div>
              </div>
            </div>
          ) : null}
        </div>
      </DialogContent>
    </Dialog>
  );
}

================
File: src/components/leads/LeadCommunicationsHistory.tsx
================
"use client";

import { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { format } from "date-fns";

interface HistoryItem {
  Id?: string;
  Subject?: string;
  Description?: string;
  ActivityDate?: string;
  Status?: string;
  CreatedDate: string;
  StartDateTime?: string;
  EndDateTime?: string;
  Field?: string;
  OldValue?: string;
  NewValue?: string;
}

interface LeadCommunicationsHistoryProps {
  leadId: string;
}

export function LeadCommunicationsHistory({
  leadId,
}: LeadCommunicationsHistoryProps) {
  const [history, setHistory] = useState<HistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchHistory() {
      if (!leadId) return;
      
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch(`/api/leads/${leadId}/history`);
        if (!response.ok) {
          throw new Error("Failed to fetch history");
        }
        const historyData = await response.json();
        setHistory(historyData);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch history");
      } finally {
        setIsLoading(false);
      }
    }

    fetchHistory();
  }, [leadId]);

  const renderHistoryItem = (item: HistoryItem) => {
    const date = format(new Date(item.CreatedDate), "MMM d, yyyy h:mm a");

    // Task
    if (item.Subject && !item.StartDateTime) {
      return (
        <TableRow key={item.Id}>
          <TableCell className="w-44">{date}</TableCell>
          <TableCell>Task</TableCell>
          <TableCell>
            <div className="font-medium">{item.Subject}</div>
            {item.Description && (
              <div className="text-sm text-gray-500">{item.Description}</div>
            )}
            {item.Status && (
              <div className="text-sm text-gray-500">Status: {item.Status}</div>
            )}
          </TableCell>
        </TableRow>
      );
    }

    // Event
    if (item.StartDateTime) {
      return (
        <TableRow key={item.Id}>
          <TableCell className="w-44">{date}</TableCell>
          <TableCell>Event</TableCell>
          <TableCell>
            <div className="font-medium">{item.Subject}</div>
            {item.Description && (
              <div className="text-sm text-gray-500">{item.Description}</div>
            )}
            <div className="text-sm text-gray-500">
              {format(new Date(item.StartDateTime), "MMM d, yyyy h:mm a")} -{" "}
              {format(new Date(item.EndDateTime!), "h:mm a")}
            </div>
          </TableCell>
        </TableRow>
      );
    }

    // Field History
    if (item.Field) {
      return (
        <TableRow key={`${item.Field}-${item.CreatedDate}`}>
          <TableCell className="w-44">{date}</TableCell>
          <TableCell>Field Update</TableCell>
          <TableCell>
            <div className="font-medium">{item.Field}</div>
            <div className="text-sm text-gray-500">
              Changed from: {item.OldValue || "(empty)"} to:{" "}
              {item.NewValue || "(empty)"}
            </div>
          </TableCell>
        </TableRow>
      );
    }

    return null;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Lead Communication History</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="py-4 text-center">Loading history...</div>
        ) : error ? (
          <div className="py-4 text-center text-red-500">{error}</div>
        ) : history.length === 0 ? (
          <div className="py-4 text-center">No history found</div>
        ) : (
          <div className="w-full rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Details</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {history.map(renderHistoryItem)}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

================
File: src/components/leads/LeadForm.tsx
================
"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Lead } from "@/db/schema/leads";
import { useState } from "react";

const leadFormSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  company: z.string().optional(),
  title: z.string().optional(),
  phone: z.string().optional(),
  status: z.string().optional(),
  industry: z.string().optional(),
  website: z.string().optional(),
});

type LeadFormData = z.infer<typeof leadFormSchema>;

interface LeadFormProps {
  initialData?: Partial<Lead>;
  onSubmit: (data: LeadFormData) => Promise<void>;
}

const normalizeInitialData = (data: Partial<Lead> | undefined): Partial<LeadFormData> | undefined => {
  if (!data) return undefined;
  
  return Object.entries(data).reduce((acc, [key, value]) => {
    if (value !== null) {
      acc[key as keyof LeadFormData] = value as any;
    }
    return acc;
  }, {} as Partial<LeadFormData>);
};
export function LeadForm({ initialData, onSubmit }: LeadFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  
  const form = useForm<LeadFormData>({
    resolver: zodResolver(leadFormSchema),
    defaultValues: normalizeInitialData(initialData) || {
      firstName: "",
      lastName: "",
      email: "",
      company: "",
      title: "",
      phone: "",
      status: "",
      industry: "",
    },
  });

  const handleSubmit = async (data: LeadFormData) => {
    try {
      setIsLoading(true);
      await onSubmit(data);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="firstName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>First Name</FormLabel>
              <FormControl>
                <Input {...field} disabled={isLoading} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="lastName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Last Name</FormLabel>
              <FormControl>
                <Input {...field} disabled={isLoading} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input {...field} type="email" disabled={isLoading} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="company"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Company</FormLabel>
              <FormControl>
                <Input {...field} disabled={isLoading} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Status</FormLabel>
              <FormControl>
                <Input {...field} disabled={isLoading} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <svg
                className="mr-2 h-4 w-4 animate-spin"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              {initialData ? "Updating..." : "Creating..."}
            </>
          ) : (
            <>{initialData ? "Update Lead" : "Create Lead"}</>
          )}
        </Button>
      </form>
    </Form>
  );
}

================
File: src/components/leads/LeadsTable.tsx
================
"use client";

import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Lead } from "@/db/schema/leads";
import { StatusBadge } from "@/components/ui/status-badge";
import { LeadForm } from "./LeadForm";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { 
  ChevronUpIcon, 
  ChevronDownIcon,
  ChevronsUpDown,
  Loader2,
  Pencil,
  Trash,
  ExternalLink
} from "lucide-react";
import Link from "next/link";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface LeadsTableProps {
  leads: Lead[];
  isLoading?: boolean;
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  onUpdate: (id: number, data: Partial<Lead>) => Promise<void>;
  onDelete: (id: number) => Promise<void>;
  onSort: (sortBy: string, sortOrder: "asc" | "desc") => void;
  onPageChange: (page: number) => void;
  onSelectionChange?: (selectedIds: number[]) => void;
  selectedLeads?: number[];
}

export function LeadsTable({ 
  leads = [], 
  isLoading,
  pagination,
  selectedLeads = [],
  onUpdate,
  onDelete,
  onSort,
  onPageChange,
  onSelectionChange
}: LeadsTableProps) {
  const [editingLead, setEditingLead] = useState<Lead | null>(null);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [sortBy, setSortBy] = useState<string>("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // Создаем массив страниц для селекта
  const pageOptions = Array.from(
    { length: pagination.totalPages },
    (_, i) => i + 1
  );

  const getStatusVariant = (status: string | null) => {
    if (!status) return 'default';
    switch (status.toLowerCase()) {
      case 'active':
        return 'success';
      case 'pending':
        return 'warning';
      case 'inactive':
        return 'error';
      case 'new':
        return 'info';
      default:
        return 'default';
    }
  };

  const handlePageChange = (value: string) => {
    const page = parseInt(value);
    if (!isNaN(page) && page >= 1 && page <= pagination.totalPages) {
      onPageChange(page);
    }
  };

  // Обновляем инпут при изменении страницы
  useEffect(() => {
    // setPageInput(pagination.page.toString());
  }, [pagination.page]);

  const handleSort = (key: string) => {
    const direction = 
      sortBy === key && sortOrder === "asc" 
        ? "desc" 
        : "asc";
    setSortBy(key);
    setSortOrder(direction);
    onSort(key, direction);
  };

  const handleSelectAll = (checked: boolean) => {
    const newSelectedIds = checked ? leads.map(lead => lead.id) : [];
    onSelectionChange?.(newSelectedIds);
  };

  const handleSelect = (id: number, checked: boolean) => {
    const newSelectedIds = checked
      ? [...selectedLeads, id]
      : selectedLeads.filter(selectedId => selectedId !== id);
    onSelectionChange?.(newSelectedIds);
  };

  const getSortIcon = (key: string) => {
    if (sortBy !== key) return <ChevronsUpDown className="w-4 h-4" />;
    return sortOrder === "asc" 
      ? <ChevronUpIcon className="w-4 h-4" />
      : <ChevronDownIcon className="w-4 h-4" />;
  };

  if (isLoading) {
    return (
      <div className="w-full h-32 flex items-center justify-center">
        <Loader2 className="w-6 h-6 animate-spin" />
      </div>
    );
  }

  return (
    <div className="w-full space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableCell className="w-[40px]">
              <Checkbox
                checked={leads.length > 0 && leads.every(lead => selectedLeads.includes(lead.id))}
                onCheckedChange={handleSelectAll}
                aria-label="Select all"
              />
            </TableCell>
            <TableCell>
              <button
                onClick={() => handleSort("firstName")}
                className="flex items-center gap-1"
              >
                Name {getSortIcon("firstName")}
              </button>
            </TableCell>
            <TableCell>
              <button
                onClick={() => handleSort("email")}
                className="flex items-center gap-1"
              >
                Email {getSortIcon("email")}
              </button>
            </TableCell>
            <TableCell>
              <button
                onClick={() => handleSort("company")}
                className="flex items-center gap-1"
              >
                Company {getSortIcon("company")}
              </button>
            </TableCell>
            <TableCell>
              <button
                onClick={() => handleSort("title")}
                className="flex items-center gap-1"
              >
                Title {getSortIcon("title")}
              </button>
            </TableCell>
            <TableCell>
              <button
                onClick={() => handleSort("status")}
                className="flex items-center gap-1"
              >
                Status {getSortIcon("status")}
              </button>
            </TableCell>
            <TableCell className="text-right">Actions</TableCell>
          </TableRow>
        </TableHeader>
        <TableBody>
          {leads.map((lead) => (
            <TableRow key={lead.id}>
              <TableCell>
                <Checkbox
                  checked={selectedLeads.includes(lead.id)}
                  onCheckedChange={(checked) => handleSelect(lead.id, checked === true)}
                  aria-label={`Select ${lead.firstName} ${lead.lastName}`}
                />
              </TableCell>
              <TableCell>{lead.firstName} {lead.lastName}</TableCell>
              <TableCell>{lead.email}</TableCell>
              <TableCell>{lead.company}</TableCell>
              <TableCell>{lead.title}</TableCell>
              <TableCell>
                <StatusBadge
                  variant={getStatusVariant(lead.status)}
                >
                  {lead.status}
                </StatusBadge>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex items-center justify-end space-x-2">
                  <Link
                    href={`/leads/${lead.id}`}
                    className="text-blue-500 hover:text-blue-700"
                    target="_blank"
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Link>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setEditingLead(lead)}
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setDeletingId(lead.id)}
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Showing page {pagination.page} of {pagination.totalPages}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pagination.page - 1)}
            disabled={pagination.page <= 1}
          >
            Previous
          </Button>
          <Select
            value={pagination.page.toString()}
            onValueChange={handlePageChange}
          >
            <SelectTrigger className="w-[70px]">
              <SelectValue placeholder={pagination.page} />
            </SelectTrigger>
            <SelectContent>
              {pageOptions.map((page) => (
                <SelectItem key={page} value={page.toString()}>
                  {page}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <span className="text-sm text-muted-foreground">
            of {pagination.totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pagination.page + 1)}
            disabled={pagination.page >= pagination.totalPages}
          >
            Next
          </Button>
        </div>
      </div>

      <Dialog open={!!editingLead} onOpenChange={(open) => !open && setEditingLead(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Lead</DialogTitle>
          </DialogHeader>
          {editingLead && (
            <LeadForm
              initialData={editingLead}
              onSubmit={async (data) => {
                try {
                  await onUpdate(editingLead.id, data);
                  setEditingLead(null);
                } catch (error) {
                  console.error('Failed to update lead:', error);
                }
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      <Dialog open={!!deletingId} onOpenChange={(open) => !open && setDeletingId(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Lead</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this lead? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-4">
            <Button variant="outline" onClick={() => setDeletingId(null)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={async () => {
                if (!deletingId) return;
                try {
                  await onDelete(deletingId);
                  setDeletingId(null);
                } catch (error) {
                  console.error('Failed to delete lead:', error);
                }
              }}
            >
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

================
File: src/components/leads/SendEmailModal.tsx
================
"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import Link from "next/link";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Lead } from "@/db/schema/leads";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { CheckCircle, XCircle, Loader2, ExternalLink, Eye } from "lucide-react";
import { EmailDetailsModal } from "@/components/emails/EmailDetailsModal";

interface SendEmailModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedLeads: number[];
}

type EmailStatus = {
  leadId: number;
  status: 'idle' | 'generating' | 'sending' | 'success' | 'error';
  error?: string;
  emailId?: number;
};

export function SendEmailModal({ isOpen, onClose, selectedLeads }: SendEmailModalProps) {
  const [selectedAgent, setSelectedAgent] = useState<string>("");
  const [selectedEmailId, setSelectedEmailId] = useState<number | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [emailStatuses, setEmailStatuses] = useState<EmailStatus[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentlySendingLeadId, setCurrentlySendingLeadId] = useState<number | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Reset statuses when modal opens or selected leads change
  useEffect(() => {
    if (isOpen) {
      // Сортируем ID лидов в порядке возрастания
      const sortedLeadIds = [...selectedLeads].sort((a, b) => a - b);
      setEmailStatuses(sortedLeadIds.map(id => ({
        leadId: id,
        status: 'idle'
      })));
      setSelectedAgent("");
      setIsGenerating(false);
      setCurrentlySendingLeadId(null);
    }
  }, [isOpen, selectedLeads]);

  const handleCancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setIsGenerating(false);
    setCurrentlySendingLeadId(null);
    setEmailStatuses(prev => prev.map(status => ({
      ...status,
      status: status.status === 'generating' || status.status === 'sending' ? 'error' : status.status,
      error: status.status === 'generating' || status.status === 'sending' ? 'Cancelled' : status.error
    })));
  }, []);

  // Cleanup abort controller when modal closes
  useEffect(() => {
    if (!isOpen) {
      handleCancel();
    }
    // Cleanup on unmount
    return () => {
      handleCancel();
    };
  }, [isOpen, handleCancel]);

  // Fetch leads data for the selected IDs
  const { data: leads, isLoading: isLoadingLeads } = useQuery({
    queryKey: ["selected-leads", selectedLeads],
    queryFn: async () => {
      const response = await fetch(`/api/leads?ids=${selectedLeads.join(",")}`);
      if (!response.ok) throw new Error("Failed to fetch leads");
      const data = await response.json();
      // Сортируем лидов по ID в порядке возрастания
      return data.leads.sort((a: Lead, b: Lead) => a.id - b.id) as Lead[];
    },
    enabled: selectedLeads.length > 0,
  });

  // Fetch agents
  const { data: agents, isLoading: isLoadingAgents } = useQuery({
    queryKey: ["agents"],
    queryFn: async () => {
      const response = await fetch("/api/v1/agents");
      if (!response.ok) throw new Error("Failed to fetch agents");
      const data = await response.json();
      return data; // Возвращаем весь объект data
    },
  });

  // Set first agent as default when modal opens and agents are loaded
  useEffect(() => {
    if (isOpen && agents?.length > 0 && !selectedAgent) {
      setSelectedAgent(String(agents[0].id));
    }
  }, [isOpen, agents, selectedAgent]);

  const handleSend = async () => {
    if (!selectedAgent) return;
    
    setIsGenerating(true);
    abortControllerRef.current = new AbortController();
    
    try {
      // Сортируем ID лидов в порядке возрастания
      const sortedLeadIds = [...selectedLeads].sort((a, b) => a - b);
      
      for (const leadId of sortedLeadIds) {
        try {
          // Проверяем, не была ли отменена операция
          if (abortControllerRef.current?.signal.aborted) {
            throw new Error('Operation cancelled');
          }

          setCurrentlySendingLeadId(leadId);
          setEmailStatuses(prev => prev.map(status => ({
            ...status,
            status: status.leadId === leadId ? 'generating' : status.status
          })));

          // Шаг 1: Генерация email
          const generateResponse = await fetch("/api/v1/generate", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              leadId,
              agentId: selectedAgent
            }),
            signal: abortControllerRef.current.signal
          });

          if (!generateResponse.ok) {
            throw new Error("Failed to generate email");
          }
          
          const emailData = await generateResponse.json();
          
          if (emailData.type === 'error') {
            throw new Error(emailData.error.message || "Failed to generate email");
          }

          // Проверяем, не была ли отменена операция
          if (abortControllerRef.current?.signal.aborted) {
            throw new Error('Operation cancelled');
          }

          // Обновляем статус на "sending"
          setEmailStatuses(prev => prev.map(status => ({
            ...status,
            status: status.leadId === leadId ? 'sending' : status.status
          })));

          // Шаг 2: Отправка email
          const sendResponse = await fetch("/api/test/email/send", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ emailId: emailData.id }),
            signal: abortControllerRef.current.signal
          });

          if (!sendResponse.ok) {
            throw new Error("Failed to send email");
          }

          setEmailStatuses(prev => prev.map(status => 
            status.leadId === leadId ? { ...status, status: 'success', emailId: emailData.id } : status
          ));
        } catch (error) {
          if (error instanceof Error && error.name === 'AbortError') {
            throw error; // Re-throw AbortError to break the main loop
          }
          setEmailStatuses(prev => prev.map(status => 
            status.leadId === leadId ? { 
              ...status, 
              status: 'error',
              error: error instanceof Error ? error.message : 'Unknown error'
            } : status
          ));
        }
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('Operation was cancelled');
      }
    } finally {
      setIsGenerating(false);
      setCurrentlySendingLeadId(null);
      abortControllerRef.current = null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Generate Emails ({selectedLeads.length} leads)</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Select Agent</label>
            <Select
              value={selectedAgent}
              onValueChange={setSelectedAgent}
              disabled={isGenerating}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select an agent" />
              </SelectTrigger>
              <SelectContent>
                {agents?.agents?.map((agent: any) => (
                  <SelectItem key={agent.id} value={String(agent.id)}>
                    {agent.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="border rounded-lg max-h-[400px] overflow-y-auto">
            <div className="min-w-full">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[200px]">Name</TableHead>
                    <TableHead className="w-[250px]">Email</TableHead>
                    <TableHead className="w-[200px]">Company</TableHead>
                    <TableHead className="w-[100px]">Status</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoadingLeads ? (
                    <TableRow>
                      <TableCell colSpan={5} className="h-24">
                        <div className="flex items-center justify-center">
                          <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
                          <span className="ml-2 text-sm text-muted-foreground">Loading leads...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : leads?.map((lead) => {
                    const status = emailStatuses.find(s => s.leadId === lead.id);
                    return (
                      <TableRow key={lead.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <span>{lead.firstName} {lead.lastName}</span>
                            <Link 
                              href={`/leads/${lead.id}`}
                              target="_blank"
                              className="text-blue-500 hover:text-blue-700"
                            >
                              <ExternalLink className="w-4 h-4" />
                            </Link>
                          </div>
                        </TableCell>
                        <TableCell>{lead.email}</TableCell>
                        <TableCell>{lead.company}</TableCell>
                        <TableCell>
                          <div className="flex items-center justify-center">
                            {status?.status === 'generating' && currentlySendingLeadId === lead.id && (
                              <div className="flex items-center space-x-2">
                                <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
                                <span className="text-sm text-muted-foreground">Generating...</span>
                              </div>
                            )}
                            {status?.status === 'sending' && currentlySendingLeadId === lead.id && (
                              <div className="flex items-center space-x-2">
                                <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
                                <span className="text-sm text-muted-foreground">Sending...</span>
                              </div>
                            )}
                            {status?.status === 'success' && (
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="w-5 h-5 text-green-500" />
                                <span className="text-sm text-muted-foreground">Sent</span>
                              </div>
                            )}
                            {status?.status === 'error' && (
                              <div className="flex items-center space-x-2" title={status.error}>
                                <XCircle className="w-5 h-5 text-red-500" />
                                <span className="text-sm text-muted-foreground">Failed</span>
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-center">
                          <div className="flex items-center justify-center space-x-2">
                            {status?.status === 'success' && (
                              <button
                                onClick={() => {
                                  const leadEmail = emailStatuses.find(l => l.leadId === lead.id);
                                  setSelectedEmailId(leadEmail?.emailId || null);
                                  setIsDetailsModalOpen(true);
                                }}
                                className="p-2 hover:bg-gray-100 rounded-full"
                                title="View Email Details"
                              >
                                <Eye className="w-4 h-4" />
                              </button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose} disabled={isGenerating}>
              Cancel
            </Button>
            {isGenerating ? (
              <Button variant="destructive" onClick={handleCancel}>
                Stop
              </Button>
            ) : (
              <Button onClick={handleSend} disabled={!selectedAgent}>
                Generate Emails
              </Button>
            )}
          </div>
        </div>
      </DialogContent>

      <EmailDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedEmailId(null);
        }}
        emailId={selectedEmailId}
      />
    </Dialog>
  );
}

================
File: src/components/prompts/LeadSelector.tsx
================
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Lead } from "@/db/schema/leads";

interface LeadSelectorProps {
  onLeadSelect: (lead: Lead) => void;
}

interface LeadsResponse {
  leads: Lead[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export function LeadSelector({ onLeadSelect }: LeadSelectorProps) {
  const [selectedLeadId, setSelectedLeadId] = useState<string>("");

  const { data, isLoading } = useQuery<LeadsResponse>({
    queryKey: ["leads"],
    queryFn: async () => {
      const response = await fetch("/api/leads");
      if (!response.ok) {
        throw new Error("Failed to fetch leads");
      }
      return response.json();
    },
  });

  const handleLeadChange = (leadId: string) => {
    setSelectedLeadId(leadId);
    const lead = data?.leads.find((l) => l.id === parseInt(leadId, 10));
    if (lead) {
      onLeadSelect(lead);
    }
  };

  if (isLoading) {
    return <div>Loading leads...</div>;
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-2">
          <Label>Select Lead</Label>
          <Select value={selectedLeadId} onValueChange={handleLeadChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select a lead" />
            </SelectTrigger>
            <SelectContent>
              {data?.leads.map((lead) => (
                <SelectItem key={lead.id} value={lead.id.toString()}>
                  {lead.firstName} {lead.lastName} - {lead.email}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  );
}

================
File: src/components/prompts/PromptEditor.tsx
================
"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { Switch } from "@/components/ui/switch";
import { promptTypes, type Prompt } from "@/db/schema/prompts";
import { PromptTester } from "./PromptTester";
import { useTestPrompt } from "@/hooks/use-prompts";
import { PromptVariableHighlighter } from "./PromptVariableHighlighter";

// Стили для предпросмотра
const previewStyles = `
  .hljs-preview {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-break: break-word;
    padding: 0.5rem;
    border-radius: 0.375rem;
    min-height: 200px;
    margin-top: 0.5rem;
    border: 1px solid #e5e7eb;
    overflow: auto;
    background-color: #ffffff;
  }
  
  .dark .hljs-preview {
    background-color: #1e293b;
    border-color: #334155;
  }
`;

interface PromptEditorProps {
  prompt?: Prompt;
  mode: "create" | "edit";
  onSave?: (prompt: Partial<Prompt>) => Promise<void>;
  onCancel?: () => void;
}

export function PromptEditor({ prompt, mode, onSave, onCancel }: PromptEditorProps) {
  const [editData, setEditData] = useState<Partial<Prompt>>({
    title: prompt?.title || "",
    type: prompt?.type || promptTypes.SYSTEM,
    content: prompt?.content || "",
    description: prompt?.description || "",
    isActive: prompt?.isActive ?? true,
    settings: prompt?.settings || {},
    metadata: prompt?.metadata || {}
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const { toast } = useToast();
  const testPrompt = useTestPrompt();

  const handleSave = async () => {
    try {
      setIsLoading(true);
      if (onSave) {
        await onSave(editData);
      }
      toast({
        title: "Success",
        description: "Prompt saved successfully",
      });
    } catch (error) {
      console.error("Error saving prompt:", error);
      toast({
        title: "Error",
        description: "Failed to save prompt",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTest = async (content: string, variables: Record<string, string>, settings: any) => {
    try {
      const result = await testPrompt.mutateAsync({
        content,
        settings: settings || {
          model: "claude-3-opus-20240229",
          temperature: 0.7,
          maxTokens: 2048
        },
        variables
      });
      
      toast({
        title: "Success",
        description: "Prompt tested successfully",
      });

      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to test prompt",
        variant: "destructive",
      });
      throw error;
    }
  };

  // Функция для обновления превью с подсветкой
  const updatePreview = (text: string) => {
    // Используем теперь не нужно вызывать highlight напрямую,
    // так как это делает компонент PromptVariableHighlighter
  };

  // Обрабатываем изменение текста
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setEditData({ ...editData, content: newValue });
  };

  // Инициализация подсветки при загрузке
  useEffect(() => {
    updatePreview(editData.content || "");
  }, []);

  // Функция для вставки переменной в текст
  const insertVariable = (variable: string) => {
    const textarea = document.getElementById("content") as HTMLTextAreaElement;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const currentContent = editData.content || "";
      const newContent = 
        currentContent.substring(0, start) + 
        `{{${variable}}}` + 
        currentContent.substring(end);
      
      setEditData({ ...editData, content: newContent });
      updatePreview(newContent);
      
      // Устанавливаем курсор после вставленной переменной
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + variable.length + 4, start + variable.length + 4);
      }, 0);
    }
  };

  return (
    <div className="space-y-4">
      <style dangerouslySetInnerHTML={{ __html: previewStyles }} />
      <Tabs defaultValue="edit" className="w-full">
        <TabsList className="w-full">
          <TabsTrigger value="edit" className="flex-1">
            {mode === "edit" ? "Edit Prompt" : "Create Prompt"}
          </TabsTrigger>
          <TabsTrigger value="test" className="flex-1">Test Prompt</TabsTrigger>
        </TabsList>

        <TabsContent value="edit" className="space-y-4 mt-4">
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={editData.title}
                onChange={(e) => setEditData({ ...editData, title: e.target.value })}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={editData.description!}
                onChange={(e) => setEditData({ ...editData, description: e.target.value })}
                className="h-20"
              />
            </div>

            <div className="grid gap-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="content">Prompt Content</Label>
                <div className="flex items-center">
                  <Switch 
                    id="preview" 
                    checked={showPreview} 
                    onCheckedChange={setShowPreview}
                    className="mr-2"
                  />
                  <Label htmlFor="preview" className="text-sm cursor-pointer">
                    {showPreview ? "Подсветка переменных" : "Редактирование"}
                  </Label>
                </div>
              </div>
              
              {!showPreview ? (
                <Textarea
                  id="content"
                  value={editData.content}
                  onChange={handleChange}
                  className="h-[300px] font-mono"
                />
              ) : (
                <div className="hljs-preview">
                  <PromptVariableHighlighter text={editData.content || ''} />
                </div>
              )}
              
              {/* Легенда с цветами переменных */}
              <div className="flex flex-wrap mt-2 text-xs text-muted-foreground">
                <div className="legend-item">
                  <span className="legend-swatch" style={{ backgroundColor: 'rgba(37, 99, 235, 0.1)', border: '1px solid rgba(37, 99, 235, 0.2)' }}></span>
                  <span>Основные</span>
                </div>
                <div className="legend-item">
                  <span className="legend-swatch" style={{ backgroundColor: 'rgba(8, 145, 178, 0.1)', border: '1px solid rgba(8, 145, 178, 0.2)' }}></span>
                  <span>details_*</span>
                </div>
                <div className="legend-item">
                  <span className="legend-swatch" style={{ backgroundColor: 'rgba(124, 58, 237, 0.1)', border: '1px solid rgba(124, 58, 237, 0.2)' }}></span>
                  <span>communications_*</span>
                </div>
                <div className="legend-item">
                  <span className="legend-swatch" style={{ backgroundColor: 'rgba(5, 150, 105, 0.1)', border: '1px solid rgba(5, 150, 105, 0.2)' }}></span>
                  <span>company_*</span>
                </div>
              </div>
              
              {/* Панель для вставки переменных */}
              <div className="variable-insertion py-3 border rounded-md mt-2">
                <h3 className="text-sm font-medium px-3 mb-2">Вставить переменную:</h3>
                <div className="px-3 flex flex-wrap gap-1">
                  <Button variant="outline" size="sm" onClick={() => insertVariable("leadFirstName")}>leadFirstName</Button>
                  <Button variant="outline" size="sm" onClick={() => insertVariable("leadLastName")}>leadLastName</Button>
                  <Button variant="outline" size="sm" onClick={() => insertVariable("leadEmail")}>leadEmail</Button>
                  <Button variant="outline" size="sm" onClick={() => insertVariable("leadCompany")}>leadCompany</Button>
                  <Button variant="outline" size="sm" onClick={() => insertVariable("senderName")}>senderName</Button>
                </div>
                <div className="border-t mt-2 pt-2 px-3">
                  <h4 className="text-sm font-medium mb-1">Поля details:</h4>
                  <div className="flex flex-wrap gap-1">
                    <Button variant="outline" size="sm" onClick={() => insertVariable("details_city")}>details_city</Button>
                    <Button variant="outline" size="sm" onClick={() => insertVariable("details_role")}>details_role</Button>
                    <Button variant="outline" size="sm" onClick={() => insertVariable("details_language")}>details_language</Button>
                  </div>
                </div>
                <div className="border-t mt-2 pt-2 px-3">
                  <h4 className="text-sm font-medium mb-1">Поля communications:</h4>
                  <div className="flex flex-wrap gap-1">
                    <Button variant="outline" size="sm" onClick={() => insertVariable("communications_linkedin")}>communications_linkedin</Button>
                    <Button variant="outline" size="sm" onClick={() => insertVariable("communications_telegram")}>communications_telegram</Button>
                  </div>
                </div>
                <div className="border-t mt-2 pt-2 px-3">
                  <h4 className="text-sm font-medium mb-1">Поля company:</h4>
                  <div className="flex flex-wrap gap-1">
                    <Button variant="outline" size="sm" onClick={() => insertVariable("company_name")}>company_name</Button>
                    <Button variant="outline" size="sm" onClick={() => insertVariable("company_website")}>company_website</Button>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={editData.isActive}
                  onCheckedChange={(checked) => setEditData({ ...editData, isActive: checked })}
                />
                <Label htmlFor="active">Active</Label>
              </div>
              {prompt && (
                <div className="text-sm text-muted-foreground">
                  Version: {prompt.version}
                </div>
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            {onCancel && (
              <Button variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button onClick={handleSave} disabled={isLoading}>
              {isLoading ? "Saving..." : mode === "edit" ? "Save Changes" : "Create Prompt"}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="test" className="space-y-4 mt-4">
          <PromptTester onTest={handleTest} content={editData.content || ""} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

================
File: src/components/prompts/PromptsManager.tsx
================
import { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { type Prompt, type PromptMetadata } from "@/db/schema/prompts";
import { PromptEditor } from "./PromptEditor";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface EditPromptData {
  title: string;
  type: string;
  content: string;
  description: string;
  isActive: boolean;
  settings: {};
  metadata: PromptMetadata;
}

interface PromptData {
  id: string;
  name: string;
  content: string;
  createdAt: string;
  updatedAt: string;
}

export function PromptsManager() {
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const fetchPrompts = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/prompts");
      if (!response.ok) {
        throw new Error("Failed to fetch prompts");
      }
      const data = await response.json();
      setPrompts(data);
    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch prompts',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchPrompts();
  }, [fetchPrompts]);

  const handleDeletePrompt = async (promptId: number) => {
    try {
      const response = await fetch(`/api/prompts/${promptId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete prompt");

      await fetchPrompts();
      toast({
        title: "Success",
        description: "Prompt deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete prompt",
        variant: "destructive",
      });
    }
  };

  const handleSavePrompt = async (promptData: Partial<Prompt>) => {
    try {
      setIsLoading(true);
      const url = selectedPrompt 
        ? `/api/prompts/${selectedPrompt.id}` 
        : "/api/prompts";
      
      const response = await fetch(url, {
        method: selectedPrompt ? "PUT" : "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(promptData),
      });

      if (!response.ok) {
        throw new Error("Failed to save prompt");
      }

      await fetchPrompts();
      setIsEditing(false);
      setSelectedPrompt(null);
      toast({
        title: "Success",
        description: `Prompt ${selectedPrompt ? "updated" : "created"} successfully`,
      });
    } catch (error) {
      console.error("Error saving prompt:", error);
      toast({
        title: "Error",
        description: "Failed to save prompt",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Prompts</h2>
        <Button onClick={() => setIsEditing(true)}>
          Create New Prompt
        </Button>
      </div>

      <Dialog open={isEditing} onOpenChange={(open) => !open && setIsEditing(false)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedPrompt ? "Edit Prompt" : "Create New Prompt"}</DialogTitle>
          </DialogHeader>
          <PromptEditor
            prompt={selectedPrompt || undefined}
            mode={selectedPrompt ? "edit" : "create"}
            onSave={handleSavePrompt}
            onCancel={() => {
              setIsEditing(false);
              setSelectedPrompt(null);
            }}
          />
        </DialogContent>
      </Dialog>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {prompts.map((prompt) => (
              <TableRow key={prompt.id}>
                <TableCell>{prompt.title}</TableCell>
                <TableCell>{prompt.type}</TableCell>
                <TableCell>{prompt.description}</TableCell>
                <TableCell>{prompt.isActive ? "Active" : "Inactive"}</TableCell>
                <TableCell className="text-right space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedPrompt(prompt);
                      setIsEditing(true);
                    }}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDeletePrompt(prompt.id)}
                  >
                    Delete
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

================
File: src/components/prompts/PromptTester.tsx
================
"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useLeads } from "@/hooks/use-leads";
import type { Lead } from "@/db/schema/leads";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";

export interface PromptTesterProps {
  content: string;
  onTest: (
    content: string, 
    variables: Record<string, string>,
    settings: {
      model: string;
      temperature: number;
      maxTokens: number;
    }
  ) => Promise<any>;
}

export function PromptTester({ content, onTest }: PromptTesterProps) {
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{
    subject: string;
    body: string;
    formattedPrompt: string;
  } | null>(null);
  const [settings, setSettings] = useState({
    model: "claude-3-5-haiku-20241022",
    temperature: 0.2,
    maxTokens: 4096
  });

  const { data: leads = [], isLoading: isLeadsLoading } = useLeads();
  const { toast } = useToast();

  useEffect(() => {
    if (leads.length > 0 && !selectedLead) {
      setSelectedLead(leads[0]);
    }
  }, [leads, selectedLead]);

  const handleTestPrompt = async () => {
    if (!selectedLead) return;
    
    setIsLoading(true);
    try {
      // Convert null values to empty strings to satisfy Record<string, string>
      const variables: Record<string, string> = {
        leadFirstName: selectedLead.firstName || '',
        leadLastName: selectedLead.lastName || '',
        leadCompany: selectedLead.company,
        leadEmail: selectedLead.email || '',
        lead_details: `Role: ${selectedLead.title || 'N/A'}\nStatus: ${selectedLead.status || 'N/A'}\nSource: ${selectedLead.leadSource || 'N/A'}`,
        lead_history: selectedLead.history ? JSON.stringify(selectedLead.history) : 'No history available',
        company_info: `Industry: ${selectedLead.industry || 'N/A'}\nSize: ${selectedLead.numberOfEmployees || 'N/A'}\nWebsite: ${selectedLead.website || 'N/A'}`
      };

      const data = await onTest(content, variables, settings);
      setResult(data);
    } catch (error) {
      console.error("Error testing prompt:", error);
      toast({
        variant: "destructive",
        title: "Error testing prompt",
        description: error instanceof Error ? error.message : "An unexpected error occurred"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-2">
        <label className="text-sm font-medium">Prompt Content</label>
        <Textarea
          value={content}
          readOnly
          className="h-[200px] min-h-[200px] bg-muted"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="flex flex-col space-y-2">
          <label className="text-sm font-medium">Model</label>
          <Select
            value={settings.model}
            onValueChange={(value) => setSettings(prev => ({ ...prev, model: value }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="claude-3-opus-20240229">Claude 3 Opus</SelectItem>
              <SelectItem value="claude-3-sonnet-20240229">Claude 3 Sonnet</SelectItem>
              <SelectItem value="claude-3-haiku-20240307">Claude 3 Haiku</SelectItem>
              <SelectItem value="claude-3-5-sonnet-20241022">Claude 3.5 Sonnet</SelectItem>
              <SelectItem value="claude-3-5-haiku-20241022">Claude 3.5 Haiku</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex flex-col space-y-2">
          <label className="text-sm font-medium">Temperature ({settings.temperature})</label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={settings.temperature}
            onChange={(e) => setSettings(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
            className="w-full"
          />
        </div>

        <div className="flex flex-col space-y-2">
          <label className="text-sm font-medium">Max Tokens</label>
          <input
            type="number"
            value={settings.maxTokens}
            onChange={(e) => setSettings(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
            min="1"
            max="4096"
            className="w-full p-2 border rounded"
          />
        </div>
      </div>

      <div className="flex flex-col space-y-2">
        <label className="text-sm font-medium">Select Lead</label>
        <Select
          value={selectedLead?.id.toString()}
          onValueChange={(value) => {
            const lead = leads.find((l) => l.id.toString() === value);
            if (lead) {
              setSelectedLead(lead);
            }
          }}
          disabled={isLeadsLoading}
        >
          <SelectTrigger>
            <SelectValue placeholder={isLeadsLoading ? "Loading leads..." : "Select a lead"}>
              {selectedLead ? `${selectedLead.firstName || ''} ${selectedLead.lastName || ''} - ${selectedLead.company}` : 
               isLeadsLoading ? "Loading leads..." : "Select a lead"}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {isLeadsLoading ? (
              <SelectItem value="loading" disabled>Loading leads...</SelectItem>
            ) : leads.map((lead) => (
              <SelectItem key={lead.id} value={lead.id.toString()}>
                {`${lead.firstName || ''} ${lead.lastName || ''} - ${lead.company}`}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Button 
        onClick={handleTestPrompt} 
        disabled={!selectedLead || isLoading || isLeadsLoading}
        className="w-full"
      >
        {isLoading ? "Testing..." : "Test Prompt"}
      </Button>

      {result && (
        <div className="space-y-4 mt-4">
           <Separator />
            <div className="space-y-2">
            <label className="text-sm font-medium">Formatted Prompt (Полный промпт с подставленными переменными)</label>
            <Textarea
              value={result.formattedPrompt}
              readOnly
              className="h-[300px] min-h-[300px] bg-muted whitespace-pre-wrap font-mono text-sm"
            />
          </div>
          <Separator />
          <h3 className="text-lg font-semibold">Generated Email</h3>
          <div className="space-y-2">
            <label className="text-sm font-medium">Subject</label>
            <Textarea
              value={result.subject}
              readOnly
              className="h-[40px] min-h-[40px] bg-muted"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Body</label>
            <Textarea
              value={result.body}
              readOnly
              className="h-[400px] min-h-[400px] bg-muted whitespace-pre-wrap"
            />
          </div>
        </div>
      )}
    </div>
  );
}

================
File: src/components/prompts/PromptVariableAnalyzer.tsx
================
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";

interface PromptVariableAnalyzerProps {
  content: string;
  onVariablesChange: (variables: string[]) => void;
}

export function PromptVariableAnalyzer({
  content,
  onVariablesChange,
}: PromptVariableAnalyzerProps) {
  const [variables, setVariables] = useState<string[]>([]);
  const [newVariable, setNewVariable] = useState("");

  useEffect(() => {
    // Анализируем промпт на наличие переменных в формате {{variable}}
    const matches = content.match(/{{([^}]+)}}/g);
    if (matches) {
      const extractedVariables = matches.map((match) =>
        match.replace(/[{}]/g, "")
      );
      setVariables(Array.from(new Set(extractedVariables)));
      onVariablesChange(Array.from(new Set(extractedVariables)));
    } else {
      setVariables([]);
      onVariablesChange([]);
    }
  }, [content, onVariablesChange]);

  const addVariable = () => {
    if (newVariable && !variables.includes(newVariable)) {
      const updatedVariables = [...variables, newVariable];
      setVariables(updatedVariables);
      onVariablesChange(updatedVariables);
      setNewVariable("");
    }
  };

  const removeVariable = (variable: string) => {
    const updatedVariables = variables.filter((v) => v !== variable);
    setVariables(updatedVariables);
    onVariablesChange(updatedVariables);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-end gap-2">
        <div className="flex-1">
          <Label>Добавить переменную</Label>
          <Input
            value={newVariable}
            onChange={(e) => setNewVariable(e.target.value)}
            placeholder="Имя переменной"
          />
        </div>
        <Button onClick={addVariable} disabled={!newVariable}>
          Добавить
        </Button>
      </div>

      <div>
        <Label>Найденные переменные</Label>
        <ScrollArea className="h-[100px] w-full border rounded-md p-2">
          <div className="flex flex-wrap gap-2">
            {variables.map((variable) => (
              <Badge
                key={variable}
                variant="secondary"
                className="cursor-pointer"
                onClick={() => removeVariable(variable)}
              >
                {variable} ×
              </Badge>
            ))}
            {variables.length === 0 && (
              <span className="text-sm text-gray-500">
                Нет найденных переменных
              </span>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}

================
File: src/components/prompts/PromptVariableHighlighter.tsx
================
"use client";

import React from 'react';

// Стили для подсветки
const highlightStyles = `
  .variable {
    border-radius: 3px;
    padding: 1px 2px;
    margin: 0 1px;
    display: inline-block;
  }
  
  .basic-var {
    color: #2563eb;
    background-color: rgba(37, 99, 235, 0.1);
    border: 1px solid rgba(37, 99, 235, 0.2);
  }
  
  .details-var {
    color: #0891b2;
    background-color: rgba(8, 145, 178, 0.1);
    border: 1px solid rgba(8, 145, 178, 0.2);
  }
  
  .communications-var {
    color: #7c3aed;
    background-color: rgba(124, 58, 237, 0.1);
    border: 1px solid rgba(124, 58, 237, 0.2);
  }
  
  .company-var {
    color: #059669;
    background-color: rgba(5, 150, 105, 0.1);
    border: 1px solid rgba(5, 150, 105, 0.2);
  }
  
  .other-var {
    color: #d97706;
    background-color: rgba(217, 119, 6, 0.1);
    border: 1px solid rgba(217, 119, 6, 0.2);
  }
`;

// Регулярные выражения для разных типов переменных
const basicVarRegex = /{{(leadFirstName|leadLastName|leadCompany|leadEmail|lead_details|lead_history|company_info|senderName)}}/g;
const detailsVarRegex = /{{(details_[a-zA-Z0-9_]+)}}/g;
const communicationsVarRegex = /{{(communications_[a-zA-Z0-9_]+)}}/g;
const companyVarRegex = /{{(company_[a-zA-Z0-9_]+)}}/g;
const otherVarRegex = /{{([a-zA-Z0-9_]+)}}/g;

// Функция для экранирования HTML
function escapeHtml(text: string) {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

// Функция для подсветки переменных в тексте
function highlightVariablesInText(text: string): string {
  // Сначала экранируем HTML
  let escapedText = escapeHtml(text);
  
  // Заменяем переменные на HTML с нужными классами
  escapedText = escapedText
    // Сначала заменяем основные переменные
    .replace(basicVarRegex, '<span class="variable basic-var">$&</span>')
    // Затем переменные details
    .replace(detailsVarRegex, '<span class="variable details-var">$&</span>')
    // Затем переменные communications
    .replace(communicationsVarRegex, '<span class="variable communications-var">$&</span>')
    // Затем переменные company
    .replace(companyVarRegex, '<span class="variable company-var">$&</span>')
    // И наконец, все остальные переменные
    .replace(otherVarRegex, (match) => {
      // Проверяем, была ли переменная уже обработана
      if (
        match.includes('class="variable')
      ) {
        return match;
      }
      return `<span class="variable other-var">${match}</span>`;
    });
  
  return escapedText;
}

interface PromptVariableHighlighterProps {
  text: string;
  className?: string;
}

export function PromptVariableHighlighter({ text, className = '' }: PromptVariableHighlighterProps) {
  // Подсвечиваем переменные
  const highlightedText = highlightVariablesInText(text);
  
  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: highlightStyles }} />
      <div
        className={`whitespace-pre-wrap ${className}`}
        dangerouslySetInnerHTML={{ __html: highlightedText }}
      />
    </>
  );
}

// Компонент-обертка для автоматического применения подсветки к строкам с переменными
export function HighlightVariables({ children }: { children: React.ReactNode }) {
  if (typeof children !== 'string') {
    return <>{children}</>;
  }

  // Проверяем, содержит ли текст переменные в формате {{variable}}
  if (!children.includes('{{')) {
    return <>{children}</>;
  }

  return <PromptVariableHighlighter text={children} />;
}

================
File: src/components/ui/alert.tsx
================
import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const alertVariants = cva(
  "relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground",
        destructive:
          "border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

const Alert = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>
>(({ className, variant, ...props }, ref) => (
  <div
    ref={ref}
    role="alert"
    className={cn(alertVariants({ variant }), className)}
    {...props}
  />
))
Alert.displayName = "Alert"

const AlertTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h5
    ref={ref}
    className={cn("mb-1 font-medium leading-none tracking-tight", className)}
    {...props}
  />
))
AlertTitle.displayName = "AlertTitle"

const AlertDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("text-sm [&_p]:leading-relaxed", className)}
    {...props}
  />
))
AlertDescription.displayName = "AlertDescription"

export { Alert, AlertTitle, AlertDescription }

================
File: src/components/ui/badge.tsx
================
import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",
        outline: "text-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }

================
File: src/components/ui/button.tsx
================
import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline:
          "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };

================
File: src/components/ui/card.tsx
================
import * as React from "react";
import { cn } from "@/lib/utils";

const Card = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "rounded-xl border bg-card text-card-foreground shadow",
      className
    )}
    {...props}
  />
));
Card.displayName = "Card";

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
));
CardHeader.displayName = "CardHeader";

const CardTitle = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("font-semibold leading-none tracking-tight", className)}
    {...props}
  />
));
CardTitle.displayName = "CardTitle";

const CardDescription = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
));
CardDescription.displayName = "CardDescription";

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
));
CardContent.displayName = "CardContent";

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-6 pt-0", className)}
    {...props}
  />
));
CardFooter.displayName = "CardFooter";

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };

================
File: src/components/ui/checkbox.tsx
================
"use client"

import * as React from "react"
import * as CheckboxPrimitive from "@radix-ui/react-checkbox"
import { Check } from "lucide-react"

import { cn } from "@/lib/utils"

const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>
>(({ className, ...props }, ref) => (
  <CheckboxPrimitive.Root
    ref={ref}
    className={cn(
      "peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",
      className
    )}
    {...props}
  >
    <CheckboxPrimitive.Indicator
      className={cn("flex items-center justify-center text-current")}
    >
      <Check className="h-4 w-4" />
    </CheckboxPrimitive.Indicator>
  </CheckboxPrimitive.Root>
))
Checkbox.displayName = CheckboxPrimitive.Root.displayName

export { Checkbox }

================
File: src/components/ui/command.tsx
================
"use client"

import * as React from "react"
import { DialogProps } from "@radix-ui/react-dialog"
import { Command as CommandPrimitive } from "cmdk"
import { Search } from "lucide-react"

import { cn } from "@/lib/utils"
import { Dialog, DialogContent } from "@/components/ui/dialog"

const Command = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive>
>(({ className, ...props }, ref) => (
  <CommandPrimitive
    ref={ref}
    className={cn(
      "flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",
      className
    )}
    {...props}
  />
))
Command.displayName = CommandPrimitive.displayName

interface CommandDialogProps extends DialogProps {}

const CommandDialog = ({ children, ...props }: CommandDialogProps) => {
  return (
    <Dialog {...props}>
      <DialogContent className="overflow-hidden p-0 shadow-lg">
        <Command className="[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5">
          {children}
        </Command>
      </DialogContent>
    </Dialog>
  )
}

const CommandInput = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.Input>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Input>
>(({ className, ...props }, ref) => (
  <div className="flex items-center border-b px-3" cmdk-input-wrapper="">
    <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
    <CommandPrimitive.Input
      ref={ref}
      className={cn(
        "flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      {...props}
    />
  </div>
))

CommandInput.displayName = CommandPrimitive.Input.displayName

const CommandList = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive.List>
>(({ className, ...props }, ref) => (
  <CommandPrimitive.List
    ref={ref}
    className={cn("max-h-[300px] overflow-y-auto overflow-x-hidden", className)}
    {...props}
  />
))

CommandList.displayName = CommandPrimitive.List.displayName

const CommandEmpty = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.Empty>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Empty>
>((props, ref) => (
  <CommandPrimitive.Empty
    ref={ref}
    className="py-6 text-center text-sm"
    {...props}
  />
))

CommandEmpty.displayName = CommandPrimitive.Empty.displayName

const CommandGroup = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.Group>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Group>
>(({ className, ...props }, ref) => (
  <CommandPrimitive.Group
    ref={ref}
    className={cn(
      "overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",
      className
    )}
    {...props}
  />
))

CommandGroup.displayName = CommandPrimitive.Group.displayName

const CommandSeparator = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <CommandPrimitive.Separator
    ref={ref}
    className={cn("-mx-1 h-px bg-border", className)}
    {...props}
  />
))
CommandSeparator.displayName = CommandPrimitive.Separator.displayName

const CommandItem = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Item>
>(({ className, ...props }, ref) => (
  <CommandPrimitive.Item
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    {...props}
  />
))

CommandItem.displayName = CommandPrimitive.Item.displayName

const CommandShortcut = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLSpanElement>) => {
  return (
    <span
      className={cn(
        "ml-auto text-xs tracking-widest text-muted-foreground",
        className
      )}
      {...props}
    />
  )
}
CommandShortcut.displayName = "CommandShortcut"

export {
  Command,
  CommandDialog,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandShortcut,
  CommandSeparator,
}

================
File: src/components/ui/dialog.tsx
================
"use client"

import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"

const Dialog = DialogPrimitive.Root

const DialogTrigger = DialogPrimitive.Trigger

const DialogPortal = DialogPrimitive.Portal

const DialogClose = DialogPrimitive.Close

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      "fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    {...props}
  />
))
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName

const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
        className
      )}
      {...props}
    >
      {children}
      <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </DialogPortal>
))
DialogContent.displayName = DialogPrimitive.Content.displayName

const DialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col space-y-1.5 text-center sm:text-left",
      className
    )}
    {...props}
  />
)
DialogHeader.displayName = "DialogHeader"

const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
      className
    )}
    {...props}
  />
)
DialogFooter.displayName = "DialogFooter"

const DialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn(
      "text-lg font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
DialogTitle.displayName = DialogPrimitive.Title.displayName

const DialogDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
DialogDescription.displayName = DialogPrimitive.Description.displayName

export {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogClose,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
}

================
File: src/components/ui/dropdown-menu.tsx
================
"use client"

import * as React from "react"
import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu"
import { Check, ChevronRight, Circle } from "lucide-react"

import { cn } from "@/lib/utils"

const DropdownMenu = DropdownMenuPrimitive.Root

const DropdownMenuTrigger = DropdownMenuPrimitive.Trigger

const DropdownMenuGroup = DropdownMenuPrimitive.Group

const DropdownMenuPortal = DropdownMenuPrimitive.Portal

const DropdownMenuSub = DropdownMenuPrimitive.Sub

const DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup

const DropdownMenuSubTrigger = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {
    inset?: boolean
  }
>(({ className, inset, children, ...props }, ref) => (
  <DropdownMenuPrimitive.SubTrigger
    ref={ref}
    className={cn(
      "flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",
      inset && "pl-8",
      className
    )}
    {...props}
  >
    {children}
    <ChevronRight className="ml-auto h-4 w-4" />
  </DropdownMenuPrimitive.SubTrigger>
))
DropdownMenuSubTrigger.displayName =
  DropdownMenuPrimitive.SubTrigger.displayName

const DropdownMenuSubContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>
>(({ className, ...props }, ref) => (
  <DropdownMenuPrimitive.SubContent
    ref={ref}
    className={cn(
      "z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
      className
    )}
    {...props}
  />
))
DropdownMenuSubContent.displayName =
  DropdownMenuPrimitive.SubContent.displayName

const DropdownMenuContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <DropdownMenuPrimitive.Portal>
    <DropdownMenuPrimitive.Content
      ref={ref}
      sideOffset={sideOffset}
      className={cn(
        "z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        className
      )}
      {...props}
    />
  </DropdownMenuPrimitive.Portal>
))
DropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName

const DropdownMenuItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {
    inset?: boolean
  }
>(({ className, inset, ...props }, ref) => (
  <DropdownMenuPrimitive.Item
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      inset && "pl-8",
      className
    )}
    {...props}
  />
))
DropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName

const DropdownMenuCheckboxItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>
>(({ className, children, checked, ...props }, ref) => (
  <DropdownMenuPrimitive.CheckboxItem
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    checked={checked}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <DropdownMenuPrimitive.ItemIndicator>
        <Check className="h-4 w-4" />
      </DropdownMenuPrimitive.ItemIndicator>
    </span>
    {children}
  </DropdownMenuPrimitive.CheckboxItem>
))
DropdownMenuCheckboxItem.displayName =
  DropdownMenuPrimitive.CheckboxItem.displayName

const DropdownMenuRadioItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>
>(({ className, children, ...props }, ref) => (
  <DropdownMenuPrimitive.RadioItem
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <DropdownMenuPrimitive.ItemIndicator>
        <Circle className="h-2 w-2 fill-current" />
      </DropdownMenuPrimitive.ItemIndicator>
    </span>
    {children}
  </DropdownMenuPrimitive.RadioItem>
))
DropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName

const DropdownMenuLabel = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {
    inset?: boolean
  }
>(({ className, inset, ...props }, ref) => (
  <DropdownMenuPrimitive.Label
    ref={ref}
    className={cn(
      "px-2 py-1.5 text-sm font-semibold",
      inset && "pl-8",
      className
    )}
    {...props}
  />
))
DropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName

const DropdownMenuSeparator = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <DropdownMenuPrimitive.Separator
    ref={ref}
    className={cn("-mx-1 my-1 h-px bg-muted", className)}
    {...props}
  />
))
DropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName

const DropdownMenuShortcut = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLSpanElement>) => {
  return (
    <span
      className={cn("ml-auto text-xs tracking-widest opacity-60", className)}
      {...props}
    />
  )
}
DropdownMenuShortcut.displayName = "DropdownMenuShortcut"

export {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuGroup,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuRadioGroup,
}

================
File: src/components/ui/form.tsx
================
import * as React from "react"
import * as LabelPrimitive from "@radix-ui/react-label"
import { Slot } from "@radix-ui/react-slot"
import {
  Controller,
  ControllerProps,
  FieldPath,
  FieldValues,
  FormProvider,
  useFormContext,
} from "react-hook-form"

import { cn } from "@/lib/utils"
import { Label } from "@/components/ui/label"

const Form = FormProvider

type FormFieldContextValue<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> = {
  name: TName
}

const FormFieldContext = React.createContext<FormFieldContextValue>(
  {} as FormFieldContextValue
)

const FormField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  ...props
}: ControllerProps<TFieldValues, TName>) => {
  return (
    <FormFieldContext.Provider value={{ name: props.name }}>
      <Controller {...props} />
    </FormFieldContext.Provider>
  )
}

const useFormField = () => {
  const fieldContext = React.useContext(FormFieldContext)
  const itemContext = React.useContext(FormItemContext)
  const { getFieldState, formState } = useFormContext()

  const fieldState = getFieldState(fieldContext.name, formState)

  if (!fieldContext) {
    throw new Error("useFormField should be used within <FormField>")
  }

  const { id } = itemContext

  return {
    id,
    name: fieldContext.name,
    formItemId: `${id}-form-item`,
    formDescriptionId: `${id}-form-item-description`,
    formMessageId: `${id}-form-item-message`,
    ...fieldState,
  }
}

type FormItemContextValue = {
  id: string
}

const FormItemContext = React.createContext<FormItemContextValue>(
  {} as FormItemContextValue
)

const FormItem = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const id = React.useId()

  return (
    <FormItemContext.Provider value={{ id }}>
      <div ref={ref} className={cn("space-y-2", className)} {...props} />
    </FormItemContext.Provider>
  )
})
FormItem.displayName = "FormItem"

const FormLabel = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>
>(({ className, ...props }, ref) => {
  const { error, formItemId } = useFormField()

  return (
    <Label
      ref={ref}
      className={cn(error && "text-destructive", className)}
      htmlFor={formItemId}
      {...props}
    />
  )
})
FormLabel.displayName = "FormLabel"

const FormControl = React.forwardRef<
  React.ElementRef<typeof Slot>,
  React.ComponentPropsWithoutRef<typeof Slot>
>(({ ...props }, ref) => {
  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()

  return (
    <Slot
      ref={ref}
      id={formItemId}
      aria-describedby={
        !error
          ? `${formDescriptionId}`
          : `${formDescriptionId} ${formMessageId}`
      }
      aria-invalid={!!error}
      {...props}
    />
  )
})
FormControl.displayName = "FormControl"

const FormDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => {
  const { formDescriptionId } = useFormField()

  return (
    <p
      ref={ref}
      id={formDescriptionId}
      className={cn("text-sm text-muted-foreground", className)}
      {...props}
    />
  )
})
FormDescription.displayName = "FormDescription"

const FormMessage = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, children, ...props }, ref) => {
  const { error, formMessageId } = useFormField()
  const body = error ? String(error?.message) : children

  if (!body) {
    return null
  }

  return (
    <p
      ref={ref}
      id={formMessageId}
      className={cn("text-sm font-medium text-destructive", className)}
      {...props}
    >
      {body}
    </p>
  )
})
FormMessage.displayName = "FormMessage"

export {
  useFormField,
  Form,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  FormField,
}

================
File: src/components/ui/input.tsx
================
import * as React from "react";
import { cn } from "@/lib/utils";

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<"input">>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
Input.displayName = "Input";

export { Input };

================
File: src/components/ui/label.tsx
================
"use client"

import * as React from "react"
import * as LabelPrimitive from "@radix-ui/react-label"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const labelVariants = cva(
  "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
)

const Label = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &
    VariantProps<typeof labelVariants>
>(({ className, ...props }, ref) => (
  <LabelPrimitive.Root
    ref={ref}
    className={cn(labelVariants(), className)}
    {...props}
  />
))
Label.displayName = LabelPrimitive.Root.displayName

export { Label }

================
File: src/components/ui/loader.tsx
================
import { cn } from "@/lib/utils";

interface LoaderProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "sm" | "md" | "lg";
}

export function Loader({ className, size = "md", ...props }: LoaderProps) {
  return (
    <div
      className={cn(
        "inline-block animate-spin rounded-full border-2 border-solid border-current border-r-transparent motion-reduce:animate-[spin_1.5s_linear_infinite]",
        {
          "h-4 w-4": size === "sm",
          "h-6 w-6": size === "md",
          "h-8 w-8": size === "lg",
        },
        className
      )}
      {...props}
    >
      <span className="sr-only">Loading...</span>
    </div>
  );
}

================
File: src/components/ui/navigation-menu.tsx
================
import * as React from "react"
import * as NavigationMenuPrimitive from "@radix-ui/react-navigation-menu"
import { cva } from "class-variance-authority"
import { ChevronDown } from "lucide-react"

import { cn } from "@/lib/utils"

const NavigationMenu = React.forwardRef<
  React.ElementRef<typeof NavigationMenuPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Root>
>(({ className, children, ...props }, ref) => (
  <NavigationMenuPrimitive.Root
    ref={ref}
    className={cn(
      "relative z-10 flex max-w-max flex-1 items-center justify-center",
      className
    )}
    {...props}
  >
    {children}
    <NavigationMenuViewport />
  </NavigationMenuPrimitive.Root>
))
NavigationMenu.displayName = NavigationMenuPrimitive.Root.displayName

const NavigationMenuList = React.forwardRef<
  React.ElementRef<typeof NavigationMenuPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.List>
>(({ className, ...props }, ref) => (
  <NavigationMenuPrimitive.List
    ref={ref}
    className={cn(
      "group flex flex-1 list-none items-center justify-center space-x-1",
      className
    )}
    {...props}
  />
))
NavigationMenuList.displayName = NavigationMenuPrimitive.List.displayName

const NavigationMenuItem = NavigationMenuPrimitive.Item

const navigationMenuTriggerStyle = cva(
  "group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
)

const NavigationMenuTrigger = React.forwardRef<
  React.ElementRef<typeof NavigationMenuPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <NavigationMenuPrimitive.Trigger
    ref={ref}
    className={cn(navigationMenuTriggerStyle(), "group", className)}
    {...props}
  >
    {children}{" "}
    <ChevronDown
      className="relative top-[1px] ml-1 h-3 w-3 transition duration-300 group-data-[state=open]:rotate-180"
      aria-hidden="true"
    />
  </NavigationMenuPrimitive.Trigger>
))
NavigationMenuTrigger.displayName = NavigationMenuPrimitive.Trigger.displayName

const NavigationMenuContent = React.forwardRef<
  React.ElementRef<typeof NavigationMenuPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Content>
>(({ className, ...props }, ref) => (
  <NavigationMenuPrimitive.Content
    ref={ref}
    className={cn(
      "left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto ",
      className
    )}
    {...props}
  />
))
NavigationMenuContent.displayName = NavigationMenuPrimitive.Content.displayName

const NavigationMenuLink = NavigationMenuPrimitive.Link

const NavigationMenuViewport = React.forwardRef<
  React.ElementRef<typeof NavigationMenuPrimitive.Viewport>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Viewport>
>(({ className, ...props }, ref) => (
  <div className={cn("absolute left-0 top-full flex justify-center")}>
    <NavigationMenuPrimitive.Viewport
      className={cn(
        "origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)]",
        className
      )}
      ref={ref}
      {...props}
    />
  </div>
))
NavigationMenuViewport.displayName =
  NavigationMenuPrimitive.Viewport.displayName

const NavigationMenuIndicator = React.forwardRef<
  React.ElementRef<typeof NavigationMenuPrimitive.Indicator>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Indicator>
>(({ className, ...props }, ref) => (
  <NavigationMenuPrimitive.Indicator
    ref={ref}
    className={cn(
      "top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in",
      className
    )}
    {...props}
  >
    <div className="relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md" />
  </NavigationMenuPrimitive.Indicator>
))
NavigationMenuIndicator.displayName =
  NavigationMenuPrimitive.Indicator.displayName

export {
  navigationMenuTriggerStyle,
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuItem,
  NavigationMenuContent,
  NavigationMenuTrigger,
  NavigationMenuLink,
  NavigationMenuIndicator,
  NavigationMenuViewport,
}

================
File: src/components/ui/pagination.tsx
================
import * as React from "react"
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react"

import { cn } from "@/lib/utils"
import { ButtonProps, buttonVariants } from "@/components/ui/button"

const Pagination = ({ className, ...props }: React.ComponentProps<"nav">) => (
  <nav
    role="navigation"
    aria-label="pagination"
    className={cn("mx-auto flex w-full justify-center", className)}
    {...props}
  />
)
Pagination.displayName = "Pagination"

const PaginationContent = React.forwardRef<
  HTMLUListElement,
  React.ComponentProps<"ul">
>(({ className, ...props }, ref) => (
  <ul
    ref={ref}
    className={cn("flex flex-row items-center gap-1", className)}
    {...props}
  />
))
PaginationContent.displayName = "PaginationContent"

const PaginationItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentProps<"li">
>(({ className, ...props }, ref) => (
  <li ref={ref} className={cn("", className)} {...props} />
))
PaginationItem.displayName = "PaginationItem"

type PaginationLinkProps = {
  isActive?: boolean
} & Pick<ButtonProps, "size"> &
  React.ComponentProps<"a">

const PaginationLink = ({
  className,
  isActive,
  size = "icon",
  ...props
}: PaginationLinkProps) => (
  <a
    aria-current={isActive ? "page" : undefined}
    className={cn(
      buttonVariants({
        variant: isActive ? "outline" : "ghost",
        size,
      }),
      className
    )}
    {...props}
  />
)
PaginationLink.displayName = "PaginationLink"

const PaginationPrevious = ({
  className,
  ...props
}: React.ComponentProps<typeof PaginationLink>) => (
  <PaginationLink
    aria-label="Go to previous page"
    size="default"
    className={cn("gap-1 pl-2.5", className)}
    {...props}
  >
    <ChevronLeft className="h-4 w-4" />
    <span>Previous</span>
  </PaginationLink>
)
PaginationPrevious.displayName = "PaginationPrevious"

const PaginationNext = ({
  className,
  ...props
}: React.ComponentProps<typeof PaginationLink>) => (
  <PaginationLink
    aria-label="Go to next page"
    size="default"
    className={cn("gap-1 pr-2.5", className)}
    {...props}
  >
    <span>Next</span>
    <ChevronRight className="h-4 w-4" />
  </PaginationLink>
)
PaginationNext.displayName = "PaginationNext"

const PaginationEllipsis = ({
  className,
  ...props
}: React.ComponentProps<"span">) => (
  <span
    aria-hidden
    className={cn("flex h-9 w-9 items-center justify-center", className)}
    {...props}
  >
    <MoreHorizontal className="h-4 w-4" />
    <span className="sr-only">More pages</span>
  </span>
)
PaginationEllipsis.displayName = "PaginationEllipsis"

export {
  Pagination,
  PaginationContent,
  PaginationLink,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
  PaginationEllipsis,
}

================
File: src/components/ui/popover.tsx
================
"use client"

import * as React from "react"
import * as PopoverPrimitive from "@radix-ui/react-popover"

import { cn } from "@/lib/utils"

const Popover = PopoverPrimitive.Root

const PopoverTrigger = PopoverPrimitive.Trigger

const PopoverContent = React.forwardRef<
  React.ElementRef<typeof PopoverPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>
>(({ className, align = "center", sideOffset = 4, ...props }, ref) => (
  <PopoverPrimitive.Portal>
    <PopoverPrimitive.Content
      ref={ref}
      align={align}
      sideOffset={sideOffset}
      className={cn(
        "z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        className
      )}
      {...props}
    />
  </PopoverPrimitive.Portal>
))
PopoverContent.displayName = PopoverPrimitive.Content.displayName

export { Popover, PopoverTrigger, PopoverContent }

================
File: src/components/ui/scroll-area.tsx
================
"use client"

import * as React from "react"
import * as ScrollAreaPrimitive from "@radix-ui/react-scroll-area"

import { cn } from "@/lib/utils"

const ScrollArea = React.forwardRef<
  React.ElementRef<typeof ScrollAreaPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>
>(({ className, children, ...props }, ref) => (
  <ScrollAreaPrimitive.Root
    ref={ref}
    className={cn("relative overflow-hidden", className)}
    {...props}
  >
    <ScrollAreaPrimitive.Viewport className="h-full w-full rounded-[inherit]">
      {children}
    </ScrollAreaPrimitive.Viewport>
    <ScrollBar />
    <ScrollAreaPrimitive.Corner />
  </ScrollAreaPrimitive.Root>
))
ScrollArea.displayName = ScrollAreaPrimitive.Root.displayName

const ScrollBar = React.forwardRef<
  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,
  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>
>(({ className, orientation = "vertical", ...props }, ref) => (
  <ScrollAreaPrimitive.ScrollAreaScrollbar
    ref={ref}
    orientation={orientation}
    className={cn(
      "flex touch-none select-none transition-colors",
      orientation === "vertical" &&
        "h-full w-2.5 border-l border-l-transparent p-[1px]",
      orientation === "horizontal" &&
        "h-2.5 flex-col border-t border-t-transparent p-[1px]",
      className
    )}
    {...props}
  >
    <ScrollAreaPrimitive.ScrollAreaThumb className="relative flex-1 rounded-full bg-border" />
  </ScrollAreaPrimitive.ScrollAreaScrollbar>
))
ScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName

export { ScrollArea, ScrollBar }

================
File: src/components/ui/select.tsx
================
"use client"

import * as React from "react"
import * as SelectPrimitive from "@radix-ui/react-select"
import { Check, ChevronDown, ChevronUp } from "lucide-react"

import { cn } from "@/lib/utils"

const Select = SelectPrimitive.Root

const SelectGroup = SelectPrimitive.Group

const SelectValue = SelectPrimitive.Value

const SelectTrigger = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Trigger
    ref={ref}
    className={cn(
      "flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
      className
    )}
    {...props}
  >
    {children}
    <SelectPrimitive.Icon asChild>
      <ChevronDown className="h-4 w-4 opacity-50" />
    </SelectPrimitive.Icon>
  </SelectPrimitive.Trigger>
))
SelectTrigger.displayName = SelectPrimitive.Trigger.displayName

const SelectScrollUpButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollUpButton
    ref={ref}
    className={cn(
      "flex cursor-default items-center justify-center py-1",
      className
    )}
    {...props}
  >
    <ChevronUp className="h-4 w-4" />
  </SelectPrimitive.ScrollUpButton>
))
SelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName

const SelectScrollDownButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollDownButton
    ref={ref}
    className={cn(
      "flex cursor-default items-center justify-center py-1",
      className
    )}
    {...props}
  >
    <ChevronDown className="h-4 w-4" />
  </SelectPrimitive.ScrollDownButton>
))
SelectScrollDownButton.displayName =
  SelectPrimitive.ScrollDownButton.displayName

const SelectContent = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>
>(({ className, children, position = "popper", ...props }, ref) => (
  <SelectPrimitive.Portal>
    <SelectPrimitive.Content
      ref={ref}
      className={cn(
        "relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        position === "popper" &&
          "data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
        className
      )}
      position={position}
      {...props}
    >
      <SelectScrollUpButton />
      <SelectPrimitive.Viewport
        className={cn(
          "p-1",
          position === "popper" &&
            "h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"
        )}
      >
        {children}
      </SelectPrimitive.Viewport>
      <SelectScrollDownButton />
    </SelectPrimitive.Content>
  </SelectPrimitive.Portal>
))
SelectContent.displayName = SelectPrimitive.Content.displayName

const SelectLabel = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Label
    ref={ref}
    className={cn("px-2 py-1.5 text-sm font-semibold", className)}
    {...props}
  />
))
SelectLabel.displayName = SelectPrimitive.Label.displayName

const SelectItem = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Item
    ref={ref}
    className={cn(
      "relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    {...props}
  >
    <span className="absolute right-2 flex h-3.5 w-3.5 items-center justify-center">
      <SelectPrimitive.ItemIndicator>
        <Check className="h-4 w-4" />
      </SelectPrimitive.ItemIndicator>
    </span>
    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
  </SelectPrimitive.Item>
))
SelectItem.displayName = SelectPrimitive.Item.displayName

const SelectSeparator = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Separator
    ref={ref}
    className={cn("-mx-1 my-1 h-px bg-muted", className)}
    {...props}
  />
))
SelectSeparator.displayName = SelectPrimitive.Separator.displayName

export {
  Select,
  SelectGroup,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
  SelectScrollUpButton,
  SelectScrollDownButton,
}

================
File: src/components/ui/separator.tsx
================
"use client"

import * as React from "react"
import * as SeparatorPrimitive from "@radix-ui/react-separator"

import { cn } from "@/lib/utils"

const Separator = React.forwardRef<
  React.ElementRef<typeof SeparatorPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>
>(
  (
    { className, orientation = "horizontal", decorative = true, ...props },
    ref
  ) => (
    <SeparatorPrimitive.Root
      ref={ref}
      decorative={decorative}
      orientation={orientation}
      className={cn(
        "shrink-0 bg-border",
        orientation === "horizontal" ? "h-[1px] w-full" : "h-full w-[1px]",
        className
      )}
      {...props}
    />
  )
)
Separator.displayName = SeparatorPrimitive.Root.displayName

export { Separator }

================
File: src/components/ui/skeleton.tsx
================
import { cn } from "@/lib/utils"

function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("animate-pulse rounded-md bg-primary/10", className)}
      {...props}
    />
  )
}

export { Skeleton }

================
File: src/components/ui/status-badge.tsx
================
import { cn } from "@/lib/utils";

type StatusBadgeVariant = "default" | "success" | "warning" | "error" | "info";

interface StatusBadgeProps {
  variant?: StatusBadgeVariant;
  children: React.ReactNode;
  className?: string;
}

const variantStyles: Record<StatusBadgeVariant, string> = {
  default: "bg-gray-100 text-gray-800",
  success: "bg-green-100 text-green-800",
  warning: "bg-yellow-100 text-yellow-800",
  error: "bg-red-100 text-red-800",
  info: "bg-blue-100 text-blue-800",
};

export function StatusBadge({ variant = "default", children, className }: StatusBadgeProps) {
  return (
    <span
      className={cn(
        "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",
        variantStyles[variant],
        className
      )}
    >
      {children}
    </span>
  );
}

================
File: src/components/ui/switch.tsx
================
"use client"

import * as React from "react"
import * as SwitchPrimitives from "@radix-ui/react-switch"

import { cn } from "@/lib/utils"

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>
>(({ className, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(
      "peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",
      className
    )}
    {...props}
    ref={ref}
  >
    <SwitchPrimitives.Thumb
      className={cn(
        "pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"
      )}
    />
  </SwitchPrimitives.Root>
))
Switch.displayName = SwitchPrimitives.Root.displayName

export { Switch }

================
File: src/components/ui/table.tsx
================
import * as React from "react"

import { cn } from "@/lib/utils"

const Table = React.forwardRef<
  HTMLTableElement,
  React.HTMLAttributes<HTMLTableElement>
>(({ className, ...props }, ref) => (
  <div className="relative w-full overflow-auto">
    <table
      ref={ref}
      className={cn("w-full caption-bottom text-sm", className)}
      {...props}
    />
  </div>
))
Table.displayName = "Table"

const TableHeader = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <thead ref={ref} className={cn("[&_tr]:border-b", className)} {...props} />
))
TableHeader.displayName = "TableHeader"

const TableBody = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tbody
    ref={ref}
    className={cn("[&_tr:last-child]:border-0", className)}
    {...props}
  />
))
TableBody.displayName = "TableBody"

const TableFooter = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tfoot
    ref={ref}
    className={cn(
      "border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",
      className
    )}
    {...props}
  />
))
TableFooter.displayName = "TableFooter"

const TableRow = React.forwardRef<
  HTMLTableRowElement,
  React.HTMLAttributes<HTMLTableRowElement>
>(({ className, ...props }, ref) => (
  <tr
    ref={ref}
    className={cn(
      "border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",
      className
    )}
    {...props}
  />
))
TableRow.displayName = "TableRow"

const TableHead = React.forwardRef<
  HTMLTableCellElement,
  React.ThHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
  <th
    ref={ref}
    className={cn(
      "h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",
      className
    )}
    {...props}
  />
))
TableHead.displayName = "TableHead"

const TableCell = React.forwardRef<
  HTMLTableCellElement,
  React.TdHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
  <td
    ref={ref}
    className={cn("p-4 align-middle [&:has([role=checkbox])]:pr-0", className)}
    {...props}
  />
))
TableCell.displayName = "TableCell"

const TableCaption = React.forwardRef<
  HTMLTableCaptionElement,
  React.HTMLAttributes<HTMLTableCaptionElement>
>(({ className, ...props }, ref) => (
  <caption
    ref={ref}
    className={cn("mt-4 text-sm text-muted-foreground", className)}
    {...props}
  />
))
TableCaption.displayName = "TableCaption"

export {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
}

================
File: src/components/ui/tabs.tsx
================
"use client"

import * as React from "react"
import * as TabsPrimitive from "@radix-ui/react-tabs"

import { cn } from "@/lib/utils"

const Tabs = TabsPrimitive.Root

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(
      "inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",
      className
    )}
    {...props}
  />
))
TabsList.displayName = TabsPrimitive.List.displayName

const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(
      "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",
      className
    )}
    {...props}
  />
))
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(
      "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
      className
    )}
    {...props}
  />
))
TabsContent.displayName = TabsPrimitive.Content.displayName

export {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
}

================
File: src/components/ui/textarea.tsx
================
import * as React from "react"

import { cn } from "@/lib/utils"

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, ...props }, ref) => {
    return (
      <textarea
        className={cn(
          "flex min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Textarea.displayName = "Textarea"

export { Textarea }

================
File: src/components/ui/toast.tsx
================
"use client"

import * as React from "react"
import * as ToastPrimitives from "@radix-ui/react-toast"
import { cva, type VariantProps } from "class-variance-authority"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"

const ToastProvider = ToastPrimitives.Provider

const ToastViewport = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Viewport>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Viewport
    ref={ref}
    className={cn(
      "fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",
      className
    )}
    {...props}
  />
))
ToastViewport.displayName = ToastPrimitives.Viewport.displayName

const toastVariants = cva(
  "group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",
  {
    variants: {
      variant: {
        default: "border bg-background text-foreground",
        destructive:
          "destructive group border-destructive bg-destructive text-destructive-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

const Toast = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &
    VariantProps<typeof toastVariants>
>(({ className, variant, ...props }, ref) => {
  return (
    <ToastPrimitives.Root
      ref={ref}
      className={cn(toastVariants({ variant }), className)}
      {...props}
    />
  )
})
Toast.displayName = ToastPrimitives.Root.displayName

const ToastAction = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Action>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Action
    ref={ref}
    className={cn(
      "inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",
      className
    )}
    {...props}
  />
))
ToastAction.displayName = ToastPrimitives.Action.displayName

const ToastClose = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Close>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Close
    ref={ref}
    className={cn(
      "absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",
      className
    )}
    toast-close=""
    {...props}
  >
    <X className="h-4 w-4" />
  </ToastPrimitives.Close>
))
ToastClose.displayName = ToastPrimitives.Close.displayName

const ToastTitle = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Title>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Title
    ref={ref}
    className={cn("text-sm font-semibold [&+div]:text-xs", className)}
    {...props}
  />
))
ToastTitle.displayName = ToastPrimitives.Title.displayName

const ToastDescription = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Description>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Description
    ref={ref}
    className={cn("text-sm opacity-90", className)}
    {...props}
  />
))
ToastDescription.displayName = ToastPrimitives.Description.displayName

type ToastProps = React.ComponentPropsWithoutRef<typeof Toast>

type ToastActionElement = React.ReactElement<typeof ToastAction>

export {
  type ToastProps,
  type ToastActionElement,
  ToastProvider,
  ToastViewport,
  Toast,
  ToastTitle,
  ToastDescription,
  ToastClose,
  ToastAction,
}

================
File: src/components/ui/toaster.tsx
================
"use client"

import { useToast } from "@/hooks/use-toast"
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from "@/components/ui/toast"
import React from "react"

export function Toaster() {
  const { toasts } = useToast()
  
  React.useEffect(() => {
    console.log('Toasts updated:', toasts)
  }, [toasts])

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, ...props }) {
        return (
          <Toast key={id} {...props}>
            <div className="grid gap-1">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && (
                <ToastDescription>{description}</ToastDescription>
              )}
            </div>
            {action}
            <ToastClose />
          </Toast>
        )
      })}
      <ToastViewport />
    </ToastProvider>
  )
}

================
File: src/components/ui/use-toast.ts
================
'use client';

// Inspired by react-hot-toast library
import * as React from "react"

import type {
  ToastActionElement,
  ToastProps,
} from "@/components/ui/toast"

const TOAST_LIMIT = 3
const TOAST_REMOVE_DELAY = 10000

type ToasterToast = ToastProps & {
  id: string
  title?: React.ReactNode
  description?: React.ReactNode
  action?: ToastActionElement
}

const _actionTypes = {
  ADD_TOAST: "ADD_TOAST",
  UPDATE_TOAST: "UPDATE_TOAST",
  DISMISS_TOAST: "DISMISS_TOAST",
  REMOVE_TOAST: "REMOVE_TOAST",
} as const

let count = 0

function genId() {
  count = (count + 1) % Number.MAX_VALUE
  return count.toString()
}

type ActionType = typeof _actionTypes

type Action =
  | {
      type: ActionType["ADD_TOAST"]
      toast: ToasterToast
    }
  | {
      type: ActionType["UPDATE_TOAST"]
      toast: Partial<ToasterToast>
    }
  | {
      type: ActionType["DISMISS_TOAST"]
      toastId?: ToasterToast["id"]
    }
  | {
      type: ActionType["REMOVE_TOAST"]
      toastId?: ToasterToast["id"]
    }

interface State {
  toasts: ToasterToast[]
}

const toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()

const addToRemoveQueue = (toastId: string) => {
  if (toastTimeouts.has(toastId)) {
    return
  }

  const timeout = setTimeout(() => {
    toastTimeouts.delete(toastId)
    dispatch({
      type: "REMOVE_TOAST",
      toastId: toastId,
    })
  }, TOAST_REMOVE_DELAY)

  toastTimeouts.set(toastId, timeout)
}

export const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case "ADD_TOAST":
      const newState = {
        ...state,
        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),
      }
      console.log('Toast added:', newState.toasts)
      return newState

    case "UPDATE_TOAST":
      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === action.toast.id ? { ...t, ...action.toast } : t
        ),
      }

    case "DISMISS_TOAST": {
      const { toastId } = action

      // ! Side effects ! - This could be extracted into a dismissToast() action,
      // but I'll keep it here for simplicity
      if (toastId) {
        addToRemoveQueue(toastId)
      } else {
        state.toasts.forEach((toast) => {
          addToRemoveQueue(toast.id)
        })
      }

      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === toastId || toastId === undefined
            ? {
                ...t,
                open: false,
              }
            : t
        ),
      }
    }
    case "REMOVE_TOAST":
      if (action.toastId === undefined) {
        return {
          ...state,
          toasts: [],
        }
      }
      return {
        ...state,
        toasts: state.toasts.filter((t) => t.id !== action.toastId),
      }
  }
}

const listeners: Array<(state: State) => void> = []

let memoryState: State = { toasts: [] }

function dispatch(action: Action) {
  memoryState = reducer(memoryState, action)
  listeners.forEach((listener) => {
    listener(memoryState)
  })
}

type Toast = Omit<ToasterToast, "id">

function toast({ ...props }: Toast) {
  const id = genId()

  const update = (props: ToasterToast) =>
    dispatch({
      type: "UPDATE_TOAST",
      toast: { ...props, id },
    })
  const dismiss = () => dispatch({ type: "DISMISS_TOAST", toastId: id })

  dispatch({
    type: "ADD_TOAST",
    toast: {
      ...props,
      id,
      open: true,
      onOpenChange: (open) => {
        if (!open) dismiss()
      },
    },
  })

  return {
    id: id,
    dismiss,
    update,
  }
}

function useToast() {
  const [state, setState] = React.useState<State>(memoryState)

  React.useEffect(() => {
    listeners.push(setState)
    return () => {
      const index = listeners.indexOf(setState)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }, [state])

  return {
    ...state,
    toast,
    dismiss: (toastId?: string) => dispatch({ type: "DISMISS_TOAST", toastId }),
  }
}

export { useToast, toast }

================
File: src/components/navigation.tsx
================
"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  HomeIcon,
  DocumentTextIcon,
  SparklesIcon,
  UserGroupIcon,
  Cog6ToothIcon,
  ChevronDownIcon,
  UserCircleIcon,
  EnvelopeIcon,
} from "@heroicons/react/24/outline";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useSession, signOut } from "next-auth/react";

export default function Navigation() {
  const pathname = usePathname();
  const { data: session } = useSession();

  if (!session) {
    return null;
  }

  const isActiveLink = (path: string) => {
    if (path === "/") {
      return pathname === path;
    }
    return pathname.startsWith(path);
  };

  return (
    <nav className="fixed top-0 left-0 right-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 z-50">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          <NavigationMenu>
            <NavigationMenuList>
              <NavigationMenuItem>
                <Link href="/" legacyBehavior passHref>
                  <NavigationMenuLink
                    className={cn(
                      navigationMenuTriggerStyle(),
                      "flex items-center gap-2",
                      isActiveLink("/")
                        ? "text-primary"
                        : "text-muted-foreground"
                    )}
                  >
                    <HomeIcon className="h-4 w-4" />
                    <span>Home</span>
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <Link href="/prompts" legacyBehavior passHref>
                  <NavigationMenuLink
                    className={cn(
                      navigationMenuTriggerStyle(),
                      "flex items-center gap-2",
                      isActiveLink("/prompts")
                        ? "text-primary"
                        : "text-muted-foreground"
                    )}
                  >
                    <DocumentTextIcon className="h-4 w-4" />
                    <span>Prompts</span>
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <Link href="/agents" legacyBehavior passHref>
                  <NavigationMenuLink
                    className={cn(
                      navigationMenuTriggerStyle(),
                      "flex items-center gap-2",
                      isActiveLink("/agents")
                        ? "text-primary"
                        : "text-muted-foreground"
                    )}
                  >
                    <SparklesIcon className="h-4 w-4" />
                    <span>Agents</span>
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <Link href="/leads" legacyBehavior passHref>
                  <NavigationMenuLink
                    className={cn(
                      navigationMenuTriggerStyle(),
                      "flex items-center gap-2",
                      isActiveLink("/leads")
                        ? "text-primary"
                        : "text-muted-foreground"
                    )}
                  >
                    <UserGroupIcon className="h-4 w-4" />
                    <span>Leads</span>
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <Link href="/emails" legacyBehavior passHref>
                  <NavigationMenuLink
                    className={cn(
                      navigationMenuTriggerStyle(),
                      "flex items-center gap-2",
                      isActiveLink("/emails")
                        ? "text-primary"
                        : "text-muted-foreground"
                    )}
                  >
                    <EnvelopeIcon className="h-4 w-4" />
                    <span>Emails</span>
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>

          <div className="flex items-center gap-2">
            {session?.user && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center gap-2">
                    {session.user.email ?? session.user.id ?? session.user.name}
                    <UserCircleIcon className="h-5 w-5" />
                    <ChevronDownIcon className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem asChild>
                    <Link href="/settings" className="flex items-center gap-2">
                      <Cog6ToothIcon className="h-4 w-4" />
                      <span>Settings</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => signOut()}>
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}

================
File: src/db/schema/agents.ts
================
import {
    pgTable,
    serial,
    text,
    timestamp,
    boolean,
    varchar,
    jsonb,
    integer,
} from "drizzle-orm/pg-core";
import { prompts } from "./prompts";

export const agents = pgTable("agents", {
    id: serial("id").primaryKey(),
    name: varchar("name", { length: 255 }).notNull(),
    description: text("description").notNull(),
    prompt: text("prompt").notNull(),
    promptId: integer("prompt_id").references(() => prompts.id, { onDelete: "set null" }),
    settings: jsonb("settings").notNull().default({}),
    metadata: jsonb("metadata").notNull().default({}),
    isActive: boolean("is_active").default(true).notNull(),
    version: integer("version").default(1).notNull(),
    createdBy: varchar("created_by", { length: 255 }).notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

================
File: src/db/schema/email-configs.ts
================
import { pgTable, timestamp, text, integer, varchar } from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { users } from "./users";

export const emailConfigs = pgTable("email_configs", {
    id: varchar("id", { length: 191 }).primaryKey().notNull(),
    smtp: text("smtp").notNull(),
    port: integer("port").notNull(),
    email: text("email").notNull(),
    password: text("password").notNull(),
    createdBy: integer("created_by")
        .notNull()
        .references(() => users.id),
    createdAt: timestamp("created_at")
        .notNull()
        .default(sql`CURRENT_TIMESTAMP`),
    updatedAt: timestamp("updated_at")
        .notNull()
        .default(sql`CURRENT_TIMESTAMP`),
});

================
File: src/db/schema/email-logs.ts
================
import { pgTable, serial, text, timestamp, integer, jsonb } from "drizzle-orm/pg-core";
import { emails } from "./emails";

export const emailLogs = pgTable("email_logs", {
    id: serial("id").primaryKey(),
    emailId: integer("email_id")
        .notNull()
        .references(() => emails.id, { onDelete: "cascade" }),
    rawPrompt: text("raw_prompt").notNull(),
    formattedPrompt: text("formatted_prompt").notNull(),
    variables: jsonb("variables").notNull(),
    aiResponse: jsonb("ai_response").notNull(),
    executionTimeMs: integer("execution_time_ms"),
    modelName: text("model_name"),
    promptTokens: integer("prompt_tokens"),
    completionTokens: integer("completion_tokens"),
    totalTokens: integer("total_tokens"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
});

================
File: src/db/schema/emails.ts
================
import { pgTable, serial, text, timestamp, boolean, varchar, integer } from "drizzle-orm/pg-core";
import { leads } from "./leads";

export const emails = pgTable("emails", {
    id: serial("id").primaryKey(),
    leadId: integer("lead_id")
        .notNull()
        .references(() => leads.id, { onDelete: "cascade" }),
    subject: varchar("subject", { length: 255 }).notNull(),
    content: text("content").notNull(),
    prompt: text("prompt").notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    createdBy: varchar("created_by", { length: 255 }).default(""),
    sent: boolean("sent").default(false).notNull(),
    sentAt: timestamp("sent_at"),
});

export const emailHistory = pgTable("email_history", {
    id: serial("id").primaryKey(),
    emailId: integer("email_id").references(() => emails.id),
    status: text("status").notNull(), // draft, sent, failed
    sentAt: timestamp("sent_at"),
    error: text("error"),
    createdAt: timestamp("created_at").defaultNow(),
});

================
File: src/db/schema/leads.ts
================
import { InferModel, sql } from "drizzle-orm";
import {
    text,
    timestamp,
    pgTable,
    json,
    varchar,
    integer,
    serial,
} from "drizzle-orm/pg-core";

export const leads = pgTable("leads", {
    id: serial("id").primaryKey(),
    salesforceId: varchar("salesforce_id", { length: 255 }),
    firstName: varchar("first_name", { length: 255 }),
    lastName: varchar("last_name", { length: 255 }),
    email: varchar("email", { length: 255 }),
    company: text("company").notNull(),
    title: varchar("title", { length: 255 }),
    phone: varchar("phone", { length: 255 }),
    industry: varchar("industry", { length: 255 }),
    rating: varchar("rating", { length: 255 }),
    leadSource: varchar("lead_source", { length: 255 }),
    description: text("description"),
    website: varchar("website", { length: 255 }),
    numberOfEmployees: integer("number_of_employees"),
    status: text("status"),
    details: json("details").$type<any>(),
    history: json("history").$type<any>(),
    company_info: json("company_info").$type<any>(),
    communications: json("communications").$type<any>(),
    lastModifiedDate: timestamp("last_modified_date"),
    createdBy: varchar("created_by", { length: 255 }).notNull(),
    createdAt: timestamp("created_at")
        .default(sql`CURRENT_TIMESTAMP`)
        .notNull(),
    updatedAt: timestamp("updated_at")
        .default(sql`CURRENT_TIMESTAMP`)
        .notNull(),
});

export type Lead = InferModel<typeof leads>;
export type NewLead = InferModel<typeof leads, "insert">;

================
File: src/db/schema/prompts.ts
================
import { pgTable, serial, text, varchar, boolean, timestamp, jsonb } from 'drizzle-orm/pg-core';

export const promptTypes = {
  SYSTEM: 'system',
  USER: 'user',
  ASSISTANT: 'assistant',
} as const;

export type PromptType = typeof promptTypes[keyof typeof promptTypes];

export interface PromptSettings {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string[];
}

export type PromptMetadata = {
  variables?: string[];
  requiredFields?: string[];
  examples?: string[];
  lastTestResult?: {
    success: boolean;
    message?: string;
    timestamp: string;
  };
  [key: string]: unknown;
};

export const prompts = pgTable('prompts', {
  id: serial('id').primaryKey(),
  title: varchar('title', { length: 255 }).notNull(),
  type: varchar('type', { length: 50 }).notNull().default(promptTypes.SYSTEM),
  content: text('content').notNull(),
  description: text('description'),
  isActive: boolean('is_active').default(true).notNull(),
  version: serial('version').notNull(),
  settings: jsonb('settings').notNull().default({}),
  metadata: jsonb('metadata').notNull().default({}),
  createdBy: varchar('created_by', { length: 255 }).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export type Prompt = {
  id: number;
  title: string;
  type: string;
  content: string;
  description: string | null;
  settings: PromptSettings;
  metadata: PromptMetadata;
  isActive: boolean;
  version: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
};

export type NewPrompt = {
  title: string;
  type: string;
  content: string;
  description: string | null;
  settings: PromptSettings;
  metadata: PromptMetadata;
  isActive?: boolean;
  version?: number;
  createdBy: string;
};

================
File: src/db/schema/users.ts
================
import { pgTable, serial, timestamp, varchar } from "drizzle-orm/pg-core";

export const users = pgTable("users", {
    id: serial("id").primaryKey(),
    email: varchar("email", { length: 256 }).notNull().unique(),
    password: varchar("password", { length: 256 }).notNull(),
    name: varchar("name", { length: 256 }),
    company: varchar("company", { length: 256 }),  // Add this line
    createdAt: timestamp("created_at").defaultNow(),
    updatedAt: timestamp("updated_at").defaultNow(),
});

================
File: src/db/create-email-configs.ts
================
import { drizzle } from "drizzle-orm/postgres-js";
import { sql } from "drizzle-orm";
import postgres from "postgres";
import * as dotenv from "dotenv";
import path from "path";

// Load environment variables from .env file
dotenv.config({ path: path.resolve(process.cwd(), ".env") });

async function createEmailConfigsTable() {
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
        throw new Error("DATABASE_URL is not set in .env file");
    }

    const connection = postgres(databaseUrl, { ssl: { rejectUnauthorized: true } });
    const db = drizzle(connection);

    try {
        await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "email_configs" (
        "id" varchar(191) PRIMARY KEY NOT NULL,
        "smtp" text NOT NULL,
        "port" integer NOT NULL,
        "email" text NOT NULL,
        "password" text NOT NULL,
        "created_by" varchar(191) NOT NULL,
        "created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
        "updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
      );
    `);
    } catch (error) {
        console.error("Error creating email configs table:", error);
    } finally {
        await connection.end();
    }
}

createEmailConfigsTable().catch(console.error);

================
File: src/db/index.ts
================
import { drizzle } from "drizzle-orm/node-postgres";
import { Pool } from "pg";
import * as schema from "./schema";

if (!process.env.DATABASE_URL) {
    throw new Error("DATABASE_URL is not set");
}

// Create a persistent database instance
const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: {
        rejectUnauthorized: true,
    },
});

// // Initialize the database
// export const initDB = async () => {
//   try {
//     // Create tables if they don't exist
//     await pool.query(`
//       CREATE TABLE IF NOT EXISTS leads (
//         id SERIAL PRIMARY KEY,
//         salesforce_id VARCHAR(18) UNIQUE NOT NULL,
//         first_name TEXT,
//         last_name TEXT,
//         email TEXT,
//         company TEXT,
//         title TEXT,
//         phone TEXT,
//         status TEXT,
//         industry TEXT,
//         rating TEXT,
//         lead_source TEXT,
//         description TEXT,
//         website TEXT,
//         number_of_employees INTEGER,
//         last_modified_date TIMESTAMP,
//         created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
//         updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
//       );

//       CREATE TABLE IF NOT EXISTS prompts (
//         id SERIAL PRIMARY KEY,
//         title TEXT NOT NULL,
//         content TEXT NOT NULL,
//         created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
//         updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
//       );

//       CREATE TABLE IF NOT EXISTS emails (
//         id SERIAL PRIMARY KEY,
//         lead_id VARCHAR(18) NOT NULL REFERENCES leads(sf_id),
//         subject TEXT NOT NULL,
//         content TEXT NOT NULL,
//         sent BOOLEAN DEFAULT FALSE,
//         created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
//         updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
//         prompt_id INTEGER REFERENCES prompts(id)
//       );

//       CREATE TABLE IF NOT EXISTS email_history (
//         id SERIAL PRIMARY KEY,
//         email_id INTEGER REFERENCES emails(id),
//         status TEXT NOT NULL,
//         sent_at TIMESTAMP,
//         error TEXT,
//         created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
//       );
//     `);

//     console.log("Database initialized successfully");
//   } catch (error) {
//     console.error("Error initializing database:", error);
//     throw error;
//   }
// };

export const db = drizzle(pool, { schema });

// Helper function to test the database connection
export const testConnection = async () => {
    try {
        const client = await pool.connect();
        console.log("Successfully connected to the database");
        client.release();
        return true;
    } catch (error) {
        console.error("Error connecting to the database:", error);
        return false;
    }
};

================
File: src/db/migrate.ts
================
import { drizzle } from "drizzle-orm/postgres-js";
import { migrate } from "drizzle-orm/postgres-js/migrator";
import postgres from "postgres";
import * as dotenv from "dotenv";
import path from "path";

// Load environment variables from .env file
dotenv.config({ path: path.resolve(process.cwd(), ".env") });

const runMigration = async () => {
  const databaseUrl = process.env.DATABASE_URL;
  if (!databaseUrl) {
    throw new Error("DATABASE_URL is not set in .env file");
  }

  console.log("Connecting to database...");
  const connection = postgres(databaseUrl, {
    ssl: {
      rejectUnauthorized: true,
    },
  });
  const db = drizzle(connection);

  console.log("Running migrations...");
  await migrate(db, {
    migrationsFolder: "drizzle",
  });

  console.log("Migrations completed");
  await connection.end();
};

runMigration().catch((err) => {
  console.error("Migration failed:", err);
  process.exit(1);
});

================
File: src/db/schema.ts
================
export * from "./schema/leads"
export * from "./schema/agents"
export * from "./schema/email-configs"
export * from "./schema/emails"
export * from "./schema/prompts"
export * from "./schema/users"

================
File: src/hooks/use-lead-details.ts
================
import { useQuery } from "@tanstack/react-query";
import { Lead } from "@/db/schema/leads";

export interface LeadDetails {
    lead: Lead;
    history: {
        type: string;
        date: string;
        description: string;
    }[];
    opportunities: {
        id: string;
        name: string;
        stage: string;
        amount: number;
        closeDate: string;
    }[];
    activities: {
        id: string;
        type: string;
        subject: string;
        date: string;
        description: string;
    }[];
}

export function useLeadDetails(leadId: string | null) {
    return useQuery<LeadDetails>({
        queryKey: ["lead-details", leadId],
        enabled: !!leadId,
        queryFn: async () => {
            const response = await fetch(`/api/leads/${leadId}/details`);
            if (!response.ok) {
                const error = await response.text();
                console.error("Failed to fetch lead details:", error);
                throw new Error("Failed to fetch lead details");
            }
            const data = await response.json();
            return data;
        },
    });
}

================
File: src/hooks/use-leads.ts
================
import { Lead } from '@/db/schema/leads';
import { useQuery } from '@tanstack/react-query';

// interface Lead {
//   id: number;
//   firstName: string;
//   lastName: string;
//   company: string;
//   email: string;
//   status?: string;
//   role?: string;
//   source?: string;
//   lastContactDate?: string;
//   interactionCount?: number;
//   lastAction?: string;
//   industry?: string;
//   companySize?: string;
//   website?: string;
//   details: any;
//   history: any;
//   company_info: any;
//   createdBy: string;
//   createdAt: Date;
//   updatedAt: Date;
// }

interface LeadsResponse {
    leads: Lead[];
    pagination?: {
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    };
}

async function fetchLeads(): Promise<Lead[]> {
    // Не передаем параметры пагинации, чтобы получить все лиды
    const response = await fetch('/api/leads');
    if (!response.ok) {
        throw new Error('Failed to fetch leads');
    }
    const data: LeadsResponse = await response.json();
    return data.leads;
}

export function useLeads() {
    return useQuery<Lead[]>({
        queryKey: ['leads'],
        queryFn: fetchLeads,
        staleTime: 30 * 1000, // Кэшируем на 30 секунд
    });
}

================
File: src/hooks/use-prompts.ts
================
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { PromptSettings, PromptMetadata, PromptType } from '@/db/schema/prompts';

interface Prompt {
  id: number;
  title: string;
  type: PromptType;
  content: string;
  description: string | null;
  isActive: boolean;
  version: number;
  settings: PromptSettings;
  metadata: PromptMetadata;
  createdAt: Date;
  updatedAt: Date;
}

interface CreatePromptData {
  title: string;
  type: PromptType;
  content: string;
  description: string | null;
  settings?: PromptSettings;
  metadata?: PromptMetadata;
}

interface UpdatePromptData extends CreatePromptData {
  id: number;
  isActive?: boolean;
}

interface TestPromptData {
  content: string;
  settings: {
    model: string;
    temperature: number;
    maxTokens: number;
  };
  variables?: Record<string, string>;
}

export function usePrompts() {
  return useQuery<Prompt[]>({
    queryKey: ['prompts'],
    queryFn: async () => {
      const response = await fetch('/api/prompts');
      if (!response.ok) {
        throw new Error('Failed to fetch prompts');
      }
      return response.json();
    },
  });
}

export function useCreatePrompt() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreatePromptData) => {
      const response = await fetch('/api/prompts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to create prompt');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['prompts'] });
    },
  });
}

export function useUpdatePrompt() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdatePromptData) => {
      const response = await fetch(`/api/prompts/${data.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to update prompt');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['prompts'] });
    },
  });
}

export function useDeletePrompt() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number) => {
      const response = await fetch(`/api/prompts/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete prompt');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['prompts'] });
    },
  });
}

export function useTestPrompt() {
  return useMutation({
    mutationFn: async (data: TestPromptData) => {
      // Тестируем промпт
      const response = await fetch('/api/v1/prompts/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to test prompt');
      }

      return response.json();
    },
  });
}

================
File: src/hooks/use-toast.ts
================
"use client"

// Inspired by react-hot-toast library
import * as React from "react"

import type {
  ToastActionElement,
  ToastProps,
} from "@/components/ui/toast"

const TOAST_LIMIT = 1
const TOAST_REMOVE_DELAY = 1000000

type ToasterToast = ToastProps & {
  id: string
  title?: React.ReactNode
  description?: React.ReactNode
  action?: ToastActionElement
}

const actionTypes = {
  ADD_TOAST: "ADD_TOAST",
  UPDATE_TOAST: "UPDATE_TOAST",
  DISMISS_TOAST: "DISMISS_TOAST",
  REMOVE_TOAST: "REMOVE_TOAST",
} as const

let count = 0

function genId() {
  count = (count + 1) % Number.MAX_SAFE_INTEGER
  return count.toString()
}

type ActionType = typeof actionTypes

type Action =
  | {
      type: ActionType["ADD_TOAST"]
      toast: ToasterToast
    }
  | {
      type: ActionType["UPDATE_TOAST"]
      toast: Partial<ToasterToast>
    }
  | {
      type: ActionType["DISMISS_TOAST"]
      toastId?: ToasterToast["id"]
    }
  | {
      type: ActionType["REMOVE_TOAST"]
      toastId?: ToasterToast["id"]
    }

interface State {
  toasts: ToasterToast[]
}

const toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()

const addToRemoveQueue = (toastId: string) => {
  if (toastTimeouts.has(toastId)) {
    return
  }

  const timeout = setTimeout(() => {
    toastTimeouts.delete(toastId)
    dispatch({
      type: "REMOVE_TOAST",
      toastId: toastId,
    })
  }, TOAST_REMOVE_DELAY)

  toastTimeouts.set(toastId, timeout)
}

export const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case "ADD_TOAST":
      return {
        ...state,
        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),
      }

    case "UPDATE_TOAST":
      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === action.toast.id ? { ...t, ...action.toast } : t
        ),
      }

    case "DISMISS_TOAST": {
      const { toastId } = action

      // ! Side effects ! - This could be extracted into a dismissToast() action,
      // but I'll keep it here for simplicity
      if (toastId) {
        addToRemoveQueue(toastId)
      } else {
        state.toasts.forEach((toast) => {
          addToRemoveQueue(toast.id)
        })
      }

      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === toastId || toastId === undefined
            ? {
                ...t,
                open: false,
              }
            : t
        ),
      }
    }
    case "REMOVE_TOAST":
      if (action.toastId === undefined) {
        return {
          ...state,
          toasts: [],
        }
      }
      return {
        ...state,
        toasts: state.toasts.filter((t) => t.id !== action.toastId),
      }
  }
}

const listeners: Array<(state: State) => void> = []

let memoryState: State = { toasts: [] }

function dispatch(action: Action) {
  memoryState = reducer(memoryState, action)
  listeners.forEach((listener) => {
    listener(memoryState)
  })
}

type Toast = Omit<ToasterToast, "id">

function toast({ ...props }: Toast) {
  const id = genId()

  const update = (props: ToasterToast) =>
    dispatch({
      type: "UPDATE_TOAST",
      toast: { ...props, id },
    })
  const dismiss = () => dispatch({ type: "DISMISS_TOAST", toastId: id })

  dispatch({
    type: "ADD_TOAST",
    toast: {
      ...props,
      id,
      open: true,
      onOpenChange: (open) => {
        if (!open) dismiss()
      },
    },
  })

  return {
    id: id,
    dismiss,
    update,
  }
}

function useToast() {
  const [state, setState] = React.useState<State>(memoryState)

  React.useEffect(() => {
    listeners.push(setState)
    return () => {
      const index = listeners.indexOf(setState)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }, [state])

  return {
    ...state,
    toast,
    dismiss: (toastId?: string) => dispatch({ type: "DISMISS_TOAST", toastId }),
  }
}

export { useToast, toast }

================
File: src/hooks/useToast.ts
================
import { useToast as useToastUI } from "@/components/ui/use-toast";

export interface ToastProps {
  title?: string;
  description: string;
  variant?: 'default' | 'destructive';
}

export const useToast = () => {
  const { toast: toastUI } = useToastUI();

  const toast = ({ title, description, variant = 'default' }: ToastProps) => {
    toastUI({
      title,
      description,
      variant,
    });
  };

  return { toast };
};

================
File: src/lib/auth.ts
================
import { db } from "@/db";
import { users } from "@/db/schema";
import { compare } from "bcryptjs";
import { eq } from "drizzle-orm";
import { AuthOptions } from "next-auth";
import Credentials from "next-auth/providers/credentials";
import Salesforce from "next-auth/providers/salesforce";
import { getServerSession } from "next-auth";

export const authOptions: AuthOptions = {
    providers: [
        Salesforce({
            clientId: process.env.NEXT_SALESFORCE_CLIENT_ID!,
            clientSecret: process.env.NEXT_SALESFORCE_CLIENT_SECRET!,
            authorization: { params: { scope: "api refresh_token web" } },
        }),
        Credentials({
            credentials: {
                email: {},
                password: {},
            },
            async authorize(_credentials, _req) {
                const response = await db
                    .select()
                    .from(users)
                    .where(eq(users.email, _credentials?.email || ""))
                    .limit(1);
                const user = response[0];
                const passwordCorrect = await compare(
                    _credentials?.password || "",
                    user.password
                );

                if (!passwordCorrect) {
                    return null;
                }

                return {
                    id: user.id.toString(),
                    email: user.email,
                    name: user.name,
                };
            },
        }),

    ],
    pages: {
        signIn: "/login",
    },
    session: {
        strategy: "jwt",
    },
    callbacks: {
        async session({ session, token }) {
            if (session?.user && token?.sub) {
                session.user.id = token.sub;
            }
            if (token) {
                session.accessToken = token.accessToken as string;
                session.refreshToken = token.refreshToken as string;
                session.instanceUrl = token.instanceUrl as string;
            }
            return session;
        },
        async jwt({ token, account }) {
            if (account) {
                token.accessToken = account.access_token as string;
                token.refreshToken = account.refresh_token as string;
                token.instanceUrl = account.instance_url as string;
            }
            return token;
        },
        async signIn({ account }) {
            if (account?.provider === "salesforce") {
                return true;
            }
            return true;
        },
    },
};

export const auth = () => getServerSession(authOptions);

================
File: src/lib/utils.ts
================
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

================
File: src/providers/auth-provider.tsx
================
"use client";

import { SessionProvider } from "next-auth/react";

export function AuthProvider({ children }: { children: React.ReactNode }) {
  return <SessionProvider>{children}</SessionProvider>;
}

================
File: src/providers/providers.tsx
================
"use client";

import { QueryProvider } from "./query-provider";
import { SessionProvider } from "next-auth/react";
import { Suspense } from "react";

function ErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <div>
      <Suspense fallback={<div>Loading...</div>}>
        {children}
      </Suspense>
    </div>
  );
}

export function Providers({
  children,
  session,
}: {
  children: React.ReactNode;
  session: any;
}) {
  return (
    <ErrorBoundary>
      <SessionProvider session={session}>
        <QueryProvider>
          {children}
        </QueryProvider>
      </SessionProvider>
    </ErrorBoundary>
  );
}

================
File: src/providers/query-provider.tsx
================
'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState } from 'react';

export function QueryProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000, // 1 minute
            refetchOnWindowFocus: false,
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}

================
File: src/services/anthropic.ts
================
import Anthropic from '@anthropic-ai/sdk';

const SYSTEM_PROMPT = `You are an AI co-pilot for SalesFlow.Team, a cutting-edge service that automates sales emails and follow-ups using AI. Your primary function is to craft highly personalized, effective cold emails for high-value prospects for any cmr users.
Your goal is to generate emails that stand out in crowded inboxes, demonstrate deep understanding of the prospect's needs, and compel them to take action.

Always start your response with "Subject: " followed by a compelling subject line.
After the subject line, add a blank line and then write the email body.`;

export type AnthropicErrorType = 
  | 'invalid_request_error'
  | 'authentication_error'
  | 'permission_error'
  | 'not_found_error'
  | 'request_too_large'
  | 'rate_limit_error'
  | 'api_error'
  | 'overloaded_error'
  | 'temporary_unavailable';

export class APIError extends Error {
  constructor(
    public message: string,
    public status: number,
    public type: AnthropicErrorType,
    public requestId?: string,
    public error?: any
  ) {
    super(message);
    this.name = 'APIError';
  }
}

export class AnthropicService {
  private client: Anthropic;

  constructor() {
    if (!process.env.NEXT_ANTHROPIC_API_KEY) {
      throw new Error('NEXT_ANTHROPIC_API_KEY is required');
    }

    this.client = new Anthropic({
      apiKey: process.env.NEXT_ANTHROPIC_API_KEY,
    });
  }

  private parseEmailResponse(response: string) {
    const lines = response.split('\n');
    let subject = '';
    let body = '';
    
    // Ищем строку с темой
    const subjectIndex = lines.findIndex(line => line.toLowerCase().startsWith('subject:'));
    if (subjectIndex !== -1) {
      subject = lines[subjectIndex].replace(/^subject:\s*/i, '').trim();
      // Удаляем строку с темой и пустые строки в начале
      body = lines.slice(subjectIndex + 1).join('\n').trim();
    } else {
      body = response.trim();
    }
    
    return { subject, body };
  }

  async generateEmail(prompt: string, settings = {}) {
    try {
      console.log('try to request to anthropic')
      const message = await this.client.messages.create({
        model: 'claude-3-5-haiku-20241022',
        max_tokens: 1024,
        ...settings,
        system: SYSTEM_PROMPT,
        messages: [{
          role: 'user',
          content: prompt
        }]
      });
      console.log('response from anthropic', message)
      // Извлекаем текст из первого блока контента
      const textBlock = message.content.find(block => block.type === 'text');
      if (!textBlock || !('text' in textBlock)) {
        throw new Error('No text content found in the response');
      }

      const response = textBlock.text;
      console.log('response from anthropic', response)
      const { subject, body } = this.parseEmailResponse(response);
      console.log('parsed response', { subject, body })
      return {
        responseContent: response,
        subject,
        body,
        usage: {
          total_tokens: message.usage.input_tokens + message.usage.output_tokens,
          prompt_tokens: message.usage.input_tokens,
          completion_tokens: message.usage.output_tokens
        }
      };
    } catch (error: any) {
      console.error('Error generating email with Anthropic:', error);
      
      const errorResponse = error.error?.error || {};
      const requestId = error.headers?.['x-request-id'];
      
      switch (error.status) {
        case 400:
          throw new APIError(
            errorResponse.message || 'Неверный формат запроса',
            400,
            'invalid_request_error',
            requestId,
            errorResponse
          );
          
        case 401:
          throw new APIError(
            errorResponse.message || 'Ошибка аутентификации API ключа',
            401,
            'authentication_error',
            requestId,
            errorResponse
          );
          
        case 403:
          throw new APIError(
            errorResponse.message || 'Отказано в доступе',
            403,
            'permission_error',
            requestId,
            errorResponse
          );
          
        case 404:
          throw new APIError(
            errorResponse.message || 'Ресурс не найден',
            404,
            'not_found_error',
            requestId,
            errorResponse
          );
          
        case 413:
          throw new APIError(
            errorResponse.message || 'Слишком большой запрос',
            413,
            'request_too_large',
            requestId,
            errorResponse
          );
          
        case 429:
          throw new APIError(
            errorResponse.message || 'Превышен лимит запросов',
            429,
            'rate_limit_error',
            requestId,
            errorResponse
          );

        case 502:
          throw new APIError(
            errorResponse.message || 'Сервис временно недоступен',
            502,
            'temporary_unavailable',
            requestId,
            errorResponse
          );
          
        case 529:
          throw new APIError(
            errorResponse.message || 'Сервис временно перегружен',
            529,
            'overloaded_error',
            requestId,
            errorResponse
          );
          
        default:
          throw new APIError(
            errorResponse.message || 'Внутренняя ошибка сервера',
            error.status || 500,
            'api_error',
            requestId,
            errorResponse
          );
      }
    }
  }
}

export const anthropic = new AnthropicService();

================
File: src/services/email.ts
================
import { Anthropic } from "@anthropic-ai/sdk";
import { salesforce } from "./salesforce";
import { db } from "../db";
import { emailHistory, emails, leads, emailConfigs } from "../db/schema";
import { eq } from "drizzle-orm";
import nodemailer from 'nodemailer';

// Initialize Anthropic client
const anthropic = new Anthropic({
  apiKey: process.env.NEXT_ANTHROPIC_API_KEY!,
});

// Helper function to safely get text from content blocks
function getTextFromContent(
  content: Anthropic.Messages.ContentBlock[]
): string {
  return content
    .filter(
      (block): block is Anthropic.Messages.TextBlock => block.type === "text"
    )
    .map((block) => block.text)
    .join("");
}

interface CompanyInfo {
  domain: string;
  name: string;
  industry: string;
  description: string;
  uses_salesforce: string;
  company_size: string;
  key_people: string;
  recent_news: string;
}

interface EmailGenerationResult {
  lead_id: string;
  email_id: number;
  subject: string;
  body: string;
  token_usage: {
    input_tokens: number;
    output_tokens: number;
    total_tokens: number;
    input_cost: number;
    output_cost: number;
    total_cost: number;
  };
}

interface WithCreatedDate {
  Id?: string;
  Subject?: string;
  Description?: string;
  CreatedDate: string;
  WhoId?: string;
  attributes?: {
    type: string;
  };
}

// Default prompt template
const AFTER_WEBINAR_PROMPT = `
CONTEXT:
We are SalesFlow.Team service, Sales and Follow ups AI co-pilot (Sales Forced by AI). Our target is to schedule a demo with SFT prospects. As Nick, co-founder of SalesFlow.Team (SFT), craft a highly personalized cold email (as part of a 4 mail sequence) to a high-value prospect (also may be called sft prospect, recipient, target, lead, Lead, SFT lead) who is a Salesforce user, using context (below) and Samantha McKenna's "Show Me You Know Me" strategy and the specific requirements for SFT.
Use the following sft lead general data and communication history (grabbed via API from our CRM) for your analysis. This part starts with: "Lead Details: "
Mostly you will see mails sent from Nick to SFT lead.
IMPORTANT in Lead history CRM marks email already sent to Lead as completed Task (\"event_type\": \"Task\")
Note that typically communication action (mail) from CRM history pushed to this prompt below starts with event_data and communication content after. Mail sent date is below the mail contents – as ActivityDate
LEAD DATA AND COMMUNICATIONS HISTORY FROM SALESFORCE. Use it for understanding the sequence of your new mail in outreach chain, and what was already told to prospect:

Lead Details: {{lead_details}}
Lead History: {{lead_history}}
Company Info: {{company_info}}

Please, understand if you will craft 1st or 2nd or 3rd mail - based on the Lead communication history. When you see the "event_type": "Task" and "activity_date": some recent dates it means that the mail or mails already were sent to Target some time in the past. IF SO YOU MUST MUST MUST reference to previous mails sent to Target.
Incorporate the following 7 elements from McKenna's strategy, while addressing specific needs:
1. Subject Line: Create a highly personalized, short, clear, and impactful subject line that references something unique about the prospect and captures attention.
2. First Sentence: Craft an engaging opener that avoids clichés, briefly introduces you as Nick from SalesFlow.Team, and shows you've done your homework on the prospect.
3. Transition: Authentically connect your opener to SFT's offering, leading into your pitch about AI-powered sales communication.
4. Challenge Focus: Highlight a specific challenge the prospect likely faces in their sales or fundraising process, emphasizing how SFT can address it.
5. Value Proposition: Present a compelling case for SFT, emphasizing what the prospect stands to lose without this AI co-pilot. Tailor this based on whether they're a commercial company (focus on increased sales and re-engaging dormant leads) or a nonprofit (emphasize efficient fundraising and outreach).
6. Address Objections: Acknowledge and preemptively address potential objections the prospect might have about adopting a new AI tool in their Salesforce workflow.
7. Call-to-Action: Include a clear and compelling CTA to schedule a 15-minute demo, giving the prospect agency in setting up the meeting.
Throughout the emails, emphasize SFT's key features and benefits:
- AI-generated intro emails from sft prospect to his / her leads contacts customers, follow-ups, quotes, and sales emails based on specific contact context, deal stage and full communication history tracked in Salesforce
- Context-aware drafting within Salesforce and AI-generated auto follow-ups at any deal stage (cold outreach, warming up, value prop, sales, post sales, customer care, new opportunities etc)
- Consideration of deal stages and specific items from past communications
- Ability to revive "dead leads" with personalized content
- Highlight the game-changing nature of these features.
- Mention successful pilot results, including increased response rates (up to 50% improvement)
Additional considerations:
Tailor the message depending on the industry:
• For commercial companies, emphasize increased sales via focused lead management and personalized, context-driven communications.
• For nonprofits, focus on more efficient fundraising and outreach through highly tailored messaging.
• If the target is a Salesforce partner (e.g., ISV, reseller, or consulting partner), suggest a potential partnership or white-labeling option.
• Request a 15-minute demo to showcase the product's features in action.
- If the target is a Salesforce partner, suggest a potential partnership or white-labeling option
- Mention that SFT is early-stage but has completed several successful pilots
- ANALIZE THE MAIL SEQUENCE and understand which is this mail in outreach chain - (you see history, remember "event_type": "Task" means mail sent and Activity date). If this is the 2nd or 3rd or 4rth outreach message  DO NOT REPEAT EXACTLY ON WHAT WAS ALREADY TOLD AND YOU MUST politely mention the previous emails.
- For 2nd mail - Important – please - reference the previous emails. - Highlight additional benefits of SFT, such as: - AI considering specific items from communication history. - Ability to automate communications throughout the sales cycle. - Reiterate the unique value in reviving "dead leads." - Include a clear call-to-action. Tone: Courteous, professional, and focused.
- For 3rd mail - Politely reference the previous emails. The email should: - Acknowledge that they may be busy. - Share a compelling success story or pilot result demonstrating how SFT has benefited similar organizations. - Create a sense of urgency if appropriate (e.g., limited pilot spots). - Provide a soft call-to-action. Tone: Understanding, respectful, and encouraging. Add additional value - for maybe junior sales - AI Co-pilot help.
- For 4th 5th and 6th mails - Politely reference the previous emails. The email should: - Be respectful and professional. -- Briefly summarize the key benefits of SFT. - Allow the recipient an easy way to opt-out or reconnect in the future. - Maintain a positive tone.
- If the company name is clear, mention it in the email body
- Keep the entire email under 200 words
- Maintain a human-style (not verified as AI generated), professional, engaging, and respectful tone
Analyze the recipient's company website and LinkedIn profile for additional context to personalize the message further.
If this is potentially the wrong contact, include a request to forward the email to the appropriate sales or marketing lead.
Do not include any links.

Add that if recipient does not want to be connected any more - just reply with "stop" or "unsub" to this mail
Your response should be in the following format exactly, with no additional text or explanations:
Subject: [Your subject line here]
[Email body]
`;

export class EmailService {
  private static formatLeadHistory(historyData: WithCreatedDate[]): string {
    const emailHistory = historyData
      .filter((record) => {
        return (
          record.attributes?.type === "Task" &&
          record.Subject?.startsWith("Email:") &&
          record.WhoId
        );
      })
      .map((record) => {
        const createdDate = new Date(record.CreatedDate);
        const recipientName = record.Description?.split(",")[0] || "Unknown";
        const recipientEmail = `${recipientName
          .toLowerCase()
          .replace(" ", ".")}`;

        return {
          lead_id: record.WhoId,
          from_email: "<EMAIL>",
          to_email: recipientEmail,
          date: createdDate,
          subject: record.Subject?.replace("Email: ", "") || "",
          body: record.Description || "",
        };
      })
      .sort((a, b) => b.date.getTime() - a.date.getTime());

    return (
      emailHistory
        .map(
          (email) => `
------------------------------------
From: ${email.from_email}
Date: ${email.date.toUTCString()}
Subject: ${email.subject}

${email.body}`
        )
        .join("\n") || "No email history found."
    );
  }

  static async getCompanyInfo(
    emailDomain: string | undefined,
    companyName: string | undefined
  ): Promise<CompanyInfo> {
    const domain = emailDomain
      ? emailDomain.split("@").pop()?.toLowerCase()
      : "unknown";
    const company = companyName || "Unknown Company";

    const emailProviders = [
      "gmail.com",
      "yahoo.com",
      "hotmail.com",
      "outlook.com",
    ];
    if (domain && emailProviders.includes(domain)) {
      return {
        domain: "Unknown",
        name: company,
        industry: "Unknown",
        description: "Unknown",
        uses_salesforce: "Unknown",
        company_size: "Unknown",
        key_people: "Unknown",
        recent_news: "Unknown",
      };
    }

    try {
      const prompt = `
        You are an AI assistant tasked with gathering information about a company. Provide a structured summary in JSON format for the following company:

        Company Name: ${company}
        Domain: ${domain}

        Please include the following details:
        - Overview: products/services, industry
        - Recent news (6 months)
        - Key leaders
        - Size, growth
        - CRM/Salesforce use
        - Industry challenges
        - Sales/marketing approach
        - Unique facts

        Ensure all fields are filled, using "Unknown" if information is not available.
      `;

      const message = await anthropic.messages.create({
        model: "claude-3-haiku-20240307",
        max_tokens: 1024,
        temperature: 0.2,
        messages: [{ role: "user", content: prompt }],
      });

      const content = getTextFromContent(message.content);
      if (!content) {
        throw new Error("No content received from Anthropic API");
      }

      return JSON.parse(content);
    } catch (error) {
      console.error("Error getting company info:", error);
      return {
        domain: domain || "Unknown",
        name: company,
        industry: "Unknown",
        description: "Unknown",
        uses_salesforce: "Unknown",
        company_size: "Unknown",
        key_people: "Unknown",
        recent_news: "Unknown",
      };
    }
  }

  static async generatePersonalizedEmail(
    salesforceId: string,
    customPrompt?: string
  ): Promise<EmailGenerationResult> {
    try {
      const lead = await db
        .select()
        .from(leads)
        .where(eq(leads.salesforceId, salesforceId))
        .limit(1);

      if (!lead || !lead[0]) {
        throw new Error(`Lead with Salesforce ID ${salesforceId} not found`);
      }

      const sfLead = await salesforce.getLeadById(salesforceId);
      const leadHistory = await salesforce.getLeadComprehensiveHistory(
        salesforceId
      );
      const _formattedHistory = this.formatLeadHistory(leadHistory);
      const companyInfo = await this.getCompanyInfo(
        sfLead.Email,
        sfLead.Company
      );

      const basePrompt = customPrompt || AFTER_WEBINAR_PROMPT;
      const prompt = basePrompt
        .replace("{{lead_details}}", JSON.stringify(sfLead, null, 2))
        .replace("{{lead_history}}", JSON.stringify(leadHistory, null, 2))
        .replace("{{company_info}}", JSON.stringify(companyInfo, null, 2));

      const message = await anthropic.messages.create({
        model: "claude-3-sonnet-20240229",
        max_tokens: 3000,
        temperature: 0.2,
        messages: [{ role: "user", content: prompt }],
      });

      const content = getTextFromContent(message.content);
      const generatedEmail =
        content ||
        "Subject: Follow-up regarding SalesFlow.Team\n\nDefault email body";

      const lines = generatedEmail.split("\n");
      const subject = lines[0].replace("Subject:", "").trim();
      const body = lines.slice(2).join("\n").trim();

      const leadInternalId = lead[0].id;

      const [savedEmail] = await db
        .insert(emails)
        .values({
          leadId: Number(leadInternalId),
          subject,
          content: body,
          prompt: customPrompt || body,
          sent: false,
        })
        .returning();

      return {
        lead_id: salesforceId,
        email_id: savedEmail.id,
        subject,
        body,
        token_usage: {
          input_tokens: 0,
          output_tokens: 0,
          total_tokens: 0,
          input_cost: 0,
          output_cost: 0,
          total_cost: 0,
        },
      };
    } catch (error) {
      console.error("Error generating personalized email:", error);
      throw error;
    }
  }

  static async sendEmail(
    emailId?: number,
    salesforceId?: string,
    emailConfigId?: string
  ): Promise<[string, any]> {
    if (!emailId && !salesforceId) {
      throw new Error("Either emailId or salesforceId must be provided");
    }

    let tokenUsage = null;
    let subject: string;
    let body: string;
    let targetSalesforceId: string;
    let recipientEmail: string;

    if (emailId && !salesforceId) {
      const emailWithLead = await db
        .select({
          email: emails,
          lead: leads,
        })
        .from(emails)
        .where(eq(emails.id, emailId))
        .innerJoin(leads, eq(emails.leadId, leads.id))
        .limit(1);

      if (!emailWithLead || !emailWithLead[0]) {
        throw new Error(`No email found with id ${emailId}`);
      }

      targetSalesforceId = emailWithLead[0].lead.salesforceId || '';
      subject = emailWithLead[0].email.subject;
      body = emailWithLead[0].email.content;
      recipientEmail = emailWithLead[0].lead.email || '';
    } else if (salesforceId) {
      const emailData = await this.generatePersonalizedEmail(salesforceId);
      emailId = emailData.email_id;
      targetSalesforceId = emailData.lead_id;
      subject = emailData.subject;
      body = emailData.body;
      tokenUsage = emailData.token_usage;

      const lead = await salesforce.getLeadById(salesforceId);
      recipientEmail = lead.Email || '';
    } else {
      // Get email data directly from the database
      const emailData = await db
        .select({
          email: emails,
          lead: leads,
        })
        .from(emails)
        .where(eq(emails.id, emailId!))
        .innerJoin(leads, eq(emails.leadId, leads.id))
        .limit(1);

      if (!emailData || !emailData[0]) {
        throw new Error(`No email found with id ${emailId}`);
      }

      targetSalesforceId = emailData[0].lead.salesforceId || '';
      subject = emailData[0].email.subject;
      body = emailData[0].email.content;
      recipientEmail = emailData[0].lead.email || '';
    }

    try {
      if (emailConfigId) {
        // Get email configuration
        const config = await db
          .select()
          .from(emailConfigs)
          .where(eq(emailConfigs.id, emailConfigId))
          .limit(1);

        if (!config || !config[0]) {
          throw new Error(`No email configuration found with id ${emailConfigId}`);
        }

        const emailConfig = config[0];

        // Validate required SMTP configuration
        if (!emailConfig.smtp || !emailConfig.email || !emailConfig.password) {
          throw new Error("Invalid SMTP configuration: missing required fields");
        }

        // Parse port number and set secure option
        const port = parseInt(emailConfig.port.toString(), 10);
        if (isNaN(port)) {
          throw new Error("Invalid SMTP port number");
        }

        // Create nodemailer transporter with proper configuration
        const transporter = nodemailer.createTransport({
          host: emailConfig.smtp,
          port: port,
          secure: port === 465, // true for 465, false for other ports
          auth: {
            user: emailConfig.email,
            pass: emailConfig.password,
          },
          tls: {
            // Do not fail on invalid certificates
            rejectUnauthorized: false
          }
        });

        // Verify SMTP connection configuration
        await transporter.verify();

        // Обработка нескольких email адресов, если они указаны через запятую
        const emailRecipients = recipientEmail.split(',').map(email => email.trim()).filter(Boolean);

        // Send email using nodemailer
        const info = await transporter.sendMail({
          from: emailConfig.email,
          to: emailRecipients.join(', '),
          subject: subject,
          text: body,
        });

        if (!info.messageId) {
          throw new Error("Failed to send email: no message ID received");
        }

        // Update email status in database
        await db
          .update(emails)
          .set({
            sent: true,
          })
          .where(eq(emails.id, emailId!));

        await db
          .update(emailHistory)
          .set({
            sentAt: new Date(),
          })
          .where(eq(emailHistory.emailId, emailId!));

        return [
          `Email was successfully sent using custom email configuration (Message ID: ${info.messageId})`,
          tokenUsage,
        ];
      } else {
        // Fallback to Salesforce email sending
        const result = await salesforce.sendEmail({
          targetLeadId: targetSalesforceId,
          subject,
          body,
        });

        if (result.success) {
          await db
            .update(emails)
            .set({
              sent: true,
            })
            .where(eq(emails.id, emailId!));

          await db
            .update(emailHistory)
            .set({
              sentAt: new Date(),
            })
            .where(eq(emailHistory.emailId, emailId!));

          return [
            `Email was successfully sent and logged as a task. Task ID: ${result.taskId}`,
            tokenUsage,
          ];
        } else {
          return ["Failed to send email.", tokenUsage];
        }
      }
    } catch (error) {
      console.error("Error sending email:", error);
      throw error;
    }
  }
}

================
File: src/services/leads.ts
================
import { leads } from "@/db/schema";

export type Lead = typeof leads.$inferSelect;
export type NewLead = typeof leads.$inferInsert;

export const LeadsService = {
    async getLeads(params?: {
        page?: number;
        limit?: number;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
        search?: string;
        ids?: number[];
    }) {
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.set('page', params.page.toString());
        if (params?.limit) searchParams.set('limit', params.limit.toString());
        if (params?.sortBy) searchParams.set('sortBy', params.sortBy);
        if (params?.sortOrder) searchParams.set('sortOrder', params.sortOrder);
        if (params?.search) searchParams.set('search', params.search);
        if (params?.ids?.length) searchParams.set('ids', params.ids.join(','));

        const response = await fetch(`/api/leads?${searchParams.toString()}`);
        if (!response.ok) {
            throw new Error('Failed to fetch leads');
        }
        const data = await response.json();

        // Проверяем структуру данных
        if (!Array.isArray(data?.leads)) {
            throw new Error('Invalid leads data format');
        }

        return {
            leads: data.leads,
            pagination: data.pagination || {
                total: data.leads.length,
                page: 1,
                limit: data.leads.length,
                totalPages: 1
            }
        };
    },

    async getLead(id: number) {
        const response = await fetch(`/api/leads/${id}`);
        if (!response.ok) {
            throw new Error('Failed to fetch lead');
        }
        return await response.json();
    },

    async createLead(data: NewLead) {
        const response = await fetch('/api/leads', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        });
        if (!response.ok) {
            throw new Error('Failed to create lead');
        }
        return await response.json();
    },

    async updateLead(id: number, data: Partial<NewLead>) {
        const response = await fetch(`/api/leads/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        });
        if (!response.ok) {
            throw new Error('Failed to update lead');
        }
        return await response.json();
    },

    async deleteLead(id: number) {
        const response = await fetch(`/api/leads/${id}`, {
            method: 'DELETE',
        });
        if (!response.ok) {
            throw new Error('Failed to delete lead');
        }
        return await response.json();
    },
}

================
File: src/services/openai.ts
================
import { GeneratedEmail } from '@/types';

// Constants
const OPENROUTER_API = {
    URL: 'https://openrouter.ai/api/v1/chat/completions',
    MODELS: {
        GPT4: 'openai/gpt-4',
        GPT35: 'openai/gpt-3.5-turbo',
        CLAUDE: 'anthropic/claude-3-opus',
        HAIKU: 'anthropic/claude-3.5-haiku-20241022'
    } as const,
} as const;

type OpenRouterModel = typeof OPENROUTER_API.MODELS[keyof typeof OPENROUTER_API.MODELS];

interface EmailGenerationServiceConfig {
    model?: OpenRouterModel;
    siteUrl?: string;
    siteName?: string;
}

// Custom errors
class EmailGenerationError extends Error {
    constructor(message: string, public readonly cause?: unknown) {
        super(message);
        this.name = 'EmailGenerationError';
    }
}

interface EmailGenerationInput {
    firstName: string | null;
    lastName: string | null;
    company: string;
    title: string | null;
    status: string | null;
    salesforceId: string;
    email: string | null;
}

interface OpenRouterResponse {
    choices: Array<{
        message: {
            content: string;
            role: string;
        };
    }>;
}

class EmailGenerationService {
    private apiKey: string;
    private siteUrl: string;
    private siteName: string;
    private model: OpenRouterModel;

    constructor(config: EmailGenerationServiceConfig = {}) {
        const apiKey = process.env.NEXT_OPENROUTER_API_KEY ?? '';
        if (!apiKey) {
            throw new EmailGenerationError('NEXT_OPENROUTER_API_KEY environment variable is not set');
        }

        this.apiKey = apiKey;
        this.siteUrl = config.siteUrl || process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
        this.siteName = config.siteName || 'Email Generator';
        this.model = config.model || OPENROUTER_API.MODELS.HAIKU;
    }

    private createPrompt(lead: EmailGenerationInput): string {
        return `Generate a personalized sales email for:
Name: ${lead.firstName} ${lead.lastName}
${lead.company ? `Company: ${lead.company}\n` : ''}
${lead.title ? `Title: ${lead.title}\n` : ''}
${lead.status ? `Lead Status: ${lead.status}\n` : ''}

The email should be:
1. Professional and engaging
2. Personalized based on their role and company
3. Clear and concise
4. Include a specific call to action
5. No more than 3 paragraphs

Generate both subject line and email content.`;
    }

    private async makeApiRequest(prompt: string): Promise<OpenRouterResponse> {
        const response = await fetch(OPENROUTER_API.URL, {
            method: "POST",
            headers: {
                "Authorization": `Bearer ${this.apiKey}`,
                "HTTP-Referer": this.siteUrl,
                "X-Title": this.siteName,
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                "model": this.model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            })
        });

        if (!response.ok) {
            const error = await response.json().catch(() => ({ message: 'Unknown error' }));
            throw new EmailGenerationError('API request failed', error);
        }

        return response.json();
    }

    private parseResponse(data: OpenRouterResponse): { subject: string; content: string } {
        const generatedText = data.choices[0]?.message?.content;
        if (!generatedText) {
            throw new EmailGenerationError('No content in response');
        }

        const [subject, ...contentParts] = generatedText.split('\n').filter(Boolean);
        return {
            subject: subject.replace(/^Subject:\s*/i, ''),
            content: contentParts.join('\n').trim()
        };
    }

    async generateEmail(lead: EmailGenerationInput): Promise<GeneratedEmail> {
        try {
            const prompt = this.createPrompt(lead);
            const data = await this.makeApiRequest(prompt);
            const { subject, content } = this.parseResponse(data);

            return {
                subject,
                content,
                prompt
            };
        } catch (error) {
            console.error('Error generating email:', error);
            if (error instanceof EmailGenerationError) {
                throw error;
            }
            throw new EmailGenerationError('Failed to generate email', error);
        }
    }
}

// Экспортируем константы для возможности использования в других местах
export const MODELS = OPENROUTER_API.MODELS;

// Создаем экземпляр сервиса с Haiku по умолчанию
export const emailGenerator = new EmailGenerationService({
    model: OPENROUTER_API.MODELS.HAIKU
});

================
File: src/services/prompts.ts
================
export interface PromptVariables {
  [key: string]: string | undefined;
  senderName?: string;
}

export function formatPrompt(template: string, variables: PromptVariables): string {
  let result = template;

  // Заменяем все переменные на их значения
  Object.entries(variables).forEach(([key, value]) => {
    if (value) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value);
    }
  });

  // Заменяем [Your Name] на имя отправителя или Nick по умолчанию
  result = result.replace(/\[Your Name\]/g, variables.senderName || 'Nick');

  return result;
}

export const COLD_EMAIL_PROMPT = `CONTEXT:
We are SalesFlow.Team service, Sales and Follow ups AI co-pilot (Sales Forced by AI). Our target is to schedule a demo with SFT prospects. As {{senderName}}, co-founder of SalesFlow.Team (SFT), craft a highly personalized cold email (as part of a 4 mail sequence) to a high-value prospect (also may be called sft prospect, recipient, target, lead, Lead, SFT lead) who is a Salesforce user, using context (below) and Samantha McKenna's "Show Me You Know Me" strategy and the specific requirements for SFT.
Use the following sft lead general data and communication history (grabbed via API from our CRM) for your analysis. This part starts with: "Lead Details: "
Mostly you will see mails sent from {{senderName}} to SFT lead.
IMPORTANT in Lead history CRM marks email already sent to Lead as completed Task ("event_type": "Task")
Note that typically communication action (mail) from CRM history pushed to this prompt below starts with event_data and communication content after. Mail sent date is below the mail contents – as ActivityDate
LEAD DATA AND COMMUNICATIONS HISTORY FROM SALESFORCE. Use it for understanding the sequence of your new mail in outreach chain, and what was already told to prospect:

Lead Details: {{lead_details}}
Lead History: {{lead_history}}
Company Info: {{company_info}}

Please, understand if you will craft 1st or 2nd or 3rd mail - based on the Lead communication history. When you see the "event_type": "Task" and "activity_date": some recent dates it means that the mail or mails already were sent to Target some time in the past. IF SO YOU MUST MUST MUST reference to previous mails sent to Target.
Incorporate the following 7 elements from McKenna's strategy, while addressing specific needs:
1. Subject Line: Create a highly personalized, short, clear, and impactful subject line that references something unique about the prospect and captures attention.
2. First Sentence: Craft an engaging opener that avoids clichés, briefly introduces you as {{senderName}} from SalesFlow.Team, and shows you've done your homework on the prospect.
3. Transition: Authentically connect your opener to SFT's offering, leading into your pitch about AI-powered sales communication.
4. Challenge Focus: Highlight a specific challenge the prospect likely faces in their sales or fundraising process, emphasizing how SFT can address it.
5. Value Proposition: Present a compelling case for SFT, emphasizing what the prospect stands to lose without this AI co-pilot. Tailor this based on whether they're a commercial company (focus on increased sales and re-engaging dormant leads) or a nonprofit (emphasize efficient fundraising and outreach).
6. Address Objections: Acknowledge and preemptively address potential objections the prospect might have about adopting a new AI tool in their Salesforce workflow.
7. Call-to-Action: Include a clear and compelling CTA to schedule a 15-minute demo, giving the prospect agency in setting up the meeting.
Throughout the emails, emphasize SFT's key features and benefits:
- AI-generated intro emails from sft prospect to his / her leads contacts customers, follow-ups, quotes, and sales emails based on specific contact context, deal stage and full communication history tracked in Salesforce
- Context-aware drafting within Salesforce and AI-generated auto follow-ups at any deal stage (cold outreach, warming up, value prop, sales, post sales, customer care, new opportunities etc)
- Consideration of deal stages and specific items from past communications
- Ability to revive "dead leads" with personalized content
- Highlight the game-changing nature of these features.
- Mention successful pilot results, including increased response rates (up to 50% improvement)`;

export const DEMO_PROMPT = `Dear {{leadFirstName}},

As a fellow Salesforce user, I understand the challenges of managing a high-volume sales pipeline while delivering personalized communications at scale. That's why I'm excited to introduce you to SalesFlow.Team (SFT), an AI-powered sales co-pilot that seamlessly integrates with your Salesforce workflow.

SFT's cutting-edge technology leverages the full context of your lead and customer interactions to generate highly tailored emails, follow-ups, and sales content. By considering your deal stage, communication history, and specific pain points, our AI ensures every touchpoint is relevant, engaging, and tailored to your prospects' unique needs.

I imagine {{leadCompany}} faces similar obstacles in {{lead_details}} – from nurturing cold leads to reviving dormant opportunities. SFT's AI can breathe new life into your sales cycle, automating personalized outreach at scale while freeing your team to focus on high-value activities.

With SFT, you'll never miss an opportunity to re-engage a promising lead or lose momentum in a deal due to generic, impersonal communications. Our successful pilots have demonstrated up to 50% improvement in response rates, translating to increased sales and revenue.

I'd love to schedule a 15-minute demo to showcase how SFT can transform your sales process. When would be a convenient time for you this week?

If you'd prefer not to connect, simply reply with "stop" or "unsub" to this email.`;

================
File: src/services/promptsManager.ts
================
import { db } from "@/db";
import { prompts, type Prompt as DbPrompt, type PromptSettings, type PromptMetadata } from "@/db/schema/prompts";
import { eq } from "drizzle-orm";

export type Prompt = DbPrompt;

export class PromptsManagerService {
  static async getPrompts(): Promise<Prompt[]> {
    try {
      const results = await db.select().from(prompts).orderBy(prompts.updatedAt);
      return results.map(prompt => ({
        ...prompt,
        settings: prompt.settings as PromptSettings,
        metadata: prompt.metadata as PromptMetadata
      }));
    } catch (error) {
      console.error("Error fetching prompts:", error);
      throw new Error("Failed to fetch prompts");
    }
  }

  static async getPromptById(id: number): Promise<Prompt | null> {
    try {
      const [result] = await db
        .select()
        .from(prompts)
        .where(eq(prompts.id, id))
        .limit(1);
      
      if (!result) return null;
      
      return {
        ...result,
        settings: result.settings as PromptSettings,
        metadata: result.metadata as PromptMetadata
      };
    } catch (error) {
      console.error("Error fetching prompt:", error);
      throw new Error("Failed to fetch prompt");
    }
  }

  static async createPrompt(prompt: Omit<Prompt, "id" | "createdAt" | "updatedAt">): Promise<Prompt> {
    try {
      const [result] = await db.insert(prompts).values(prompt).returning();
      return {
        ...result,
        settings: result.settings as PromptSettings,
        metadata: result.metadata as PromptMetadata
      };
    } catch (error) {
      console.error("Error creating prompt:", error);
      throw new Error("Failed to create prompt");
    }
  }

  static async updatePrompt(id: number, prompt: Partial<Omit<Prompt, "id" | "createdAt" | "updatedAt">>): Promise<Prompt> {
    try {
      const [result] = await db
        .update(prompts)
        .set({ ...prompt, updatedAt: new Date() })
        .where(eq(prompts.id, id))
        .returning();

      return {
        ...result,
        settings: result.settings as PromptSettings,
        metadata: result.metadata as PromptMetadata
      };
    } catch (error) {
      console.error("Error updating prompt:", error);
      throw new Error("Failed to update prompt");
    }
  }

  static async deletePrompt(id: number): Promise<void> {
    try {
      await db.delete(prompts).where(eq(prompts.id, id));
    } catch (error) {
      console.error("Error deleting prompt:", error);
      throw new Error("Failed to delete prompt");
    }
  }
}

================
File: src/services/salesforce.ts
================
import * as jsforce from "jsforce";
import { SalesforceTokens } from "@/types";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

// Define Lead interface to match Salesforce Lead object structure
interface SalesforceLead {
    Id: string;
    FirstName?: string;
    LastName?: string;
    Email?: string;
    Company?: string;
    Title?: string;
    Phone?: string;
    Status?: string;
    LastModifiedDate?: string;
    CreatedDate?: string;
    Rating?: string;
    Industry?: string;
    LeadSource?: string;
    Description?: string;
    Website?: string;
    NumberOfEmployees?: number;
    Street?: string;
    City?: string;
    State?: string;
    PostalCode?: string;
    Country?: string;
}

interface SendEmailParams {
    targetLeadId: string;
    subject: string;
    body: string;
}

interface SendEmailResult {
    success: boolean;
    taskId?: string;
    error?: string;
}

interface EmailSimpleResponse {
    isSuccess: boolean;
    errors?: Array<{ message: string }>;
}

interface WithCreatedDate {
    CreatedDate: string;
}

class SalesforceService {
    private conn: jsforce.Connection;
    private oauth2: jsforce.OAuth2;
    private isAuthenticated: boolean = false;

    constructor() {
        // Initialize OAuth2 configuration
        this.oauth2 = new jsforce.OAuth2({
            clientId: process.env.NEXT_SALESFORCE_CLIENT_ID!,
            clientSecret: process.env.NEXT_SALESFORCE_CLIENT_SECRET!,
            redirectUri: `${process.env.NEXTAUTH_URL}/api/v1/salesforce/auth/callback`,
        });

        // Initialize connection
        this.conn = new jsforce.Connection({
            oauth2: this.oauth2,
        });
    }

    private async getSession() {
        try {
            const session = await getServerSession(authOptions);
            if (!session?.accessToken || !session?.instanceUrl) {
                const error = new Error("Not authenticated with Salesforce");
                (error as any).redirectTo = "/api/auth/signin";
                throw error;
            }
            return session;
        } catch (error) {
            console.error("Error getting session:", error);
            throw error;
        }
    }

    private async ensureAuth(): Promise<void> {
        try {
            const session = await this.getSession();

            if (!session.accessToken || !session.instanceUrl) {
                throw new Error("Missing Salesforce tokens");
            }

            // Set up connection with session tokens
            this.conn = new jsforce.Connection({
                oauth2: this.oauth2,
                accessToken: session.accessToken,
                instanceUrl: session.instanceUrl,
                refreshToken: session.refreshToken,
            });

            // Verify the connection
            await this.conn.identity();
            this.isAuthenticated = true;
        } catch (error) {
            console.error("Error ensuring auth:", error);
            this.isAuthenticated = false;
            throw error;
        }
    }

    async getCurrentUserId(): Promise<string> {
        await this.ensureAuth();
        const userInfo = await this.conn.identity();
        return userInfo.user_id;
    }

    async getLeads(
        lastSyncDate?: Date
    ): Promise<(jsforce.Lead & { displayName: string })[]> {
        try {
            await this.ensureAuth();

            let query = `
        SELECT Id, FirstName, LastName, Email, Company, Title, Phone, Status,
               LastModifiedDate, CreatedDate, Rating, Industry, LeadSource,
               Description, Website, NumberOfEmployees
        FROM Lead
        WHERE IsDeleted = false
      `;

            if (lastSyncDate) {
                query += ` AND LastModifiedDate > ${lastSyncDate.toISOString()}`;
            }

            query += " ORDER BY LastModifiedDate DESC LIMIT 1000";

            const result = await this.conn.query<SalesforceLead>(query);

            // Transform the records to include a display name
            return result.records.map((lead) => ({
                ...lead,
                displayName: `${lead.FirstName || ""} ${lead.LastName || ""} - ${lead.Company || "No Company"
                    }`.trim(),
            }));
        } catch (error) {
            console.error("Error fetching leads:", error);
            throw error;
        }
    }

    async getLeadById(id: string): Promise<jsforce.Lead> {
        try {
            await this.ensureAuth();

            const result = await this.conn.query<jsforce.Lead>(`
        SELECT Id, FirstName, LastName, Email, Company, Title, Phone, Status,
               LastModifiedDate, CreatedDate, Rating, Industry, LeadSource
        FROM Lead
        WHERE Id = '${id}'
        AND IsDeleted = false
      `);

            if (!result.records.length) {
                throw new Error("Lead not found");
            }

            return result.records[0] as jsforce.Lead;
        } catch (error) {
            console.error("Error fetching lead:", error);
            throw error;
        }
    }

    async updateLead(
        id: string,
        data: Partial<jsforce.Lead>
    ): Promise<jsforce.SaveResult> {
        try {
            await this.ensureAuth();

            const result = (await this.conn.sobject("Lead").update({
                Id: id,
                ...data,
            })) as jsforce.SaveResult;

            if (!result.success) {
                throw new Error("Failed to update lead");
            }

            return result;
        } catch (error) {
            console.error("Error updating lead:", error);
            throw error;
        }
    }

    async searchLeads(searchTerm: string): Promise<jsforce.SearchRecord[]> {
        try {
            await this.ensureAuth();

            const sosl = `
        FIND {${searchTerm}}
        IN ALL FIELDS
        RETURNING Lead(
          Id, FirstName, LastName, Email, Company, Title, Phone, Status,
          LastModifiedDate, CreatedDate, Rating, Industry, LeadSource
          WHERE IsDeleted = false
        )
        LIMIT 1000
      `;

            const result = await this.conn.search<jsforce.SearchRecord>(sosl);
            return result.searchRecords;
        } catch (error) {
            console.error("Error searching leads:", error);
            throw error;
        }
    }

    async describeLead(): Promise<jsforce.DescribeSObjectResult> {
        try {
            await this.ensureAuth();
            return await this.conn.describe();
        } catch (error) {
            console.error("Error describing Lead object:", error);
            throw error;
        }
    }

    async getLeadComprehensiveHistory(leadId: string) {
        try {
            await this.ensureAuth();

            // GET TASKS
            const tasks = await this.conn.query<WithCreatedDate>(
                `SELECT Id, Subject, Description, ActivityDate, Status, WhoId, CreatedDate 
       FROM Task 
       WHERE WhoId = '${leadId}' 
       ORDER BY CreatedDate ASC`
            );

            // GET EVENTS
            const events = await this.conn.query<WithCreatedDate>(
                `SELECT Id, Subject, Description, StartDateTime, EndDateTime, ActivityDate, WhoId, CreatedDate 
       FROM Event 
       WHERE WhoId = '${leadId}' 
       ORDER BY CreatedDate ASC`
            );

            // GET FIELD HISTORY
            const fieldHistory = await this.conn.query<WithCreatedDate>(
                `SELECT Field, OldValue, NewValue, CreatedDate 
       FROM LeadHistory 
       WHERE LeadId = '${leadId}' 
       ORDER BY CreatedDate ASC`
            );

            const allHistory = [
                ...tasks.records,
                ...events.records,
                ...fieldHistory.records,
            ].sort(
                (a, b) =>
                    new Date(a.CreatedDate).getTime() - new Date(b.CreatedDate).getTime()
            );

            return allHistory;
        } catch (error) {
            console.error("Failed to retrieve lead history", error);
            throw error;
        }
    }

    getAuthorizationUrl() {
        // Генерируем code verifier для PKCE
        const codeVerifier = [...Array(96)]
            .map(() => Math.random().toString(36)[2])
            .join('');

        // Создаем code challenge из code verifier
        const codeChallenge = codeVerifier; // В реальном приложении здесь должен быть SHA256

        // Получаем URL авторизации с PKCE
        const url = this.oauth2.getAuthorizationUrl({
            scope: 'api refresh_token',
            code_challenge: codeChallenge,
            code_challenge_method: 'plain', // В реальном приложении должно быть 'S256'
        });

        return { url, codeVerifier };
    }

    async getAccessToken(code: string, codeVerifier: string): Promise<SalesforceTokens> {
        try {
            const userInfo = await this.oauth2.requestToken(code, { code_verifier: codeVerifier });
            return {
                access_token: userInfo.access_token,
                refresh_token: userInfo.refresh_token,
                instance_url: userInfo.instance_url,
            };
        } catch (error) {
            console.error('Error getting access token:', error);
            throw error;
        }
    }

    setTokens(accessToken: string, refreshToken?: string, instanceUrl?: string) {
        this.conn = new jsforce.Connection({
            oauth2: this.oauth2,
            accessToken,
            refreshToken,
            instanceUrl,
        });
    }

    async sendEmail(params: SendEmailParams): Promise<SendEmailResult> {
        try {
            await this.ensureAuth();

            const lead = await this.getLeadById(params.targetLeadId);
            const leadEmail = lead.Email;

            if (!leadEmail) {
                throw new Error("No email was found");
            }

            // Send email using Salesforce API
            const response = (await this.conn.request({
                method: "POST",
                url: "/actions/standard/emailSimple",
                headers: {
                    Authorization: "Bearer " + this.conn.accessToken,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    inputs: [
                        {
                            emailSubject: params.subject,
                            emailBody: params.body,
                            emailAddresses: leadEmail,
                        },
                    ],
                }),
            })) as EmailSimpleResponse[];

            if (!response[0]?.isSuccess) {
                return {
                    success: false,
                    error: response[0]?.errors?.[0]?.message || "Failed to send email",
                };
            }

            // Log email as a task
            const taskResult = await this.conn.sobject("Task").create({
                WhoId: params.targetLeadId,
                Subject: `Email: ${params.subject}`,
                Description: params.body,
                ActivityDate: new Date().toISOString(),
                Status: "Completed",
                Priority: "Normal",
                TaskSubtype: "Email",
            });

            return {
                success: true,
                taskId: taskResult.id,
            };
        } catch (error) {
            console.error("Error sending email:", error);
            return {
                success: false,
                error:
                    error instanceof Error ? error.message : "Unknown error occurred",
            };
        }
    }
}

// Export a singleton instance
export const salesforce = new SalesforceService();

================
File: src/trigger/example.ts
================
import { logger, task, wait } from "@trigger.dev/sdk/v3";

export const helloWorldTask = task({
  id: "hello-world",
  // Set an optional maxDuration to prevent tasks from running indefinitely
  maxDuration: 300, // Stop executing after 300 secs (5 mins) of compute
  run: async (payload: any, { ctx }) => {
    logger.log("Hello, world!", { payload, ctx });

    await wait.for({ seconds: 5 });

    return {
      message: "Hello, world!",
    }
  },
});

================
File: src/types/agent.ts
================
export type LLMProvider = 'openai' | 'anthropic';

export type OpenAIModel = 
  | 'gpt-4'
  | 'gpt-4-turbo'
  | 'gpt-3.5-turbo'
  | 'chatgpt-4o-latest'
  | 'gpt-4o-mini'
  | 'o1-mini'
  | 'o1-preview';

export type AnthropicModel = 
  | 'claude-3-5-sonnet-20241022'
  | 'claude-3-5-haiku-20241022';

export type LLMModel = OpenAIModel | AnthropicModel;

export interface AgentSettings {
  llmProvider: LLMProvider;
  model: LLMModel;
  temperature: number;
  maxTokens: number;
  [key: string]: any;
}

export interface AgentMetadata {
  lastUpdate?: {
    timestamp: string;
    user: string;
  };
  lastTest?: {
    timestamp: string;
    result: string;
  };
  [key: string]: any;
}

export interface Agent {
  id: number;
  name: string;
  description: string;
  prompt: string;
  promptId?: number;
  settings: AgentSettings;
  metadata: AgentMetadata;
  isActive: boolean;
  version: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export type CreateAgentInput = Omit<
  Agent,
  'id' | 'createdBy' | 'createdAt' | 'updatedAt' | 'version' | 'metadata'
>;

export type UpdateAgentInput = Partial<CreateAgentInput>;

================
File: src/types/index.ts
================
export interface Lead {
  id?: number;
  salesforceId: string;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  company?: string | null;
  title?: string | null;
  phone?: string | null;
  status?: string | null;
  industry?: string | null;
  rating?: string | null;
  leadSource?: string | null;
  description?: string | null;
  website?: string | null;
  numberOfEmployees?: number | null;
  lastModifiedDate?: Date | null;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface GeneratedEmail {
  subject: string;
  content: string;
  prompt: string;
}

export interface SalesforceTokens {
  access_token: string;
  refresh_token?: string;
  instance_url: string;
}

export interface EmailTemplate {
  id: number;
  title: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface EmailConfig {
  id: string;
  smtp: string;
  port: number;
  email: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

================
File: src/types/jsforce.d.ts
================
declare module 'jsforce' {
    export interface ConnectionOptions {
        oauth2?: OAuth2;
        accessToken?: string;
        refreshToken?: string;
        instanceUrl?: string;
    }

    export interface TokenResponse {
        access_token: string;
        instance_url: string;
        refresh_token?: string;
    }

    export interface SaveResult {
        success: boolean;
        id: string;
        errors: string[];
    }

    export interface SearchRecord {
        Id: string;
        [key: string]: any;
    }

    export interface DescribeSObjectResult {
        name: string;
        label: string;
        fields: any[];
    }

    export class Connection {
        constructor(options?: ConnectionOptions);
        oauth2?: OAuth2;
        
        // Identity methods
        identity(callback?: (err: Error, res: any) => void): Promise<any>;
        
        // Query methods
        query<T>(soql: string): Promise<{ records: T[] }>;
        search<T = SearchRecord>(sosl: string): Promise<{ searchRecords: T[] }>;
        
        // SObject methods
        sobject(type: string): any;
        describe(): Promise<DescribeSObjectResult>;

        // Request methods
        request(options: any): Promise<any>;
        instanceUrl: string;
        accessToken: string;
    }

    export class OAuth2 {
        constructor(options: {
            clientId: string;
            clientSecret: string;
            redirectUri: string;
        });

        getAuthorizationUrl(params?: { 
            scope?: string; 
            state?: string;
            prompt?: 'login' | 'consent' | 'select_account' | 'none';
            code_challenge?: string;
            code_challenge_method?: 'S256' | 'plain';
        }): string;
        
        requestToken(code: string, params?: {
            code_verifier?: string;
        }): Promise<TokenResponse>;
        
        refreshToken(refreshToken: string): Promise<TokenResponse>;
    }

    export interface Lead {
        Id: string;
        FirstName?: string;
        LastName?: string;
        Email?: string;
        Company?: string;
        Title?: string;
        Phone?: string;
        Status?: string;
        [key: string]: any;
    }
}

================
File: src/types/next-auth.d.ts
================
import { _NextAuth, _JWT } from "next-auth";

declare module "next-auth" {
  // Extend session to hold the access_token
  interface Session {
    user: {
      id: string
    } & DefaultSession["user"];
    accessToken?: string;
    refreshToken?: string;
    instanceUrl?: string;
  }

  interface User {
    id: string;
    email: string;
    name?: string | null;
  }
}

declare module "next-auth/jwt" {
  // Extend token to hold the access_token before it gets put into session
  interface JWT {
    id: string;
    email: string;
    name?: string | null;
    accessToken?: string;
    refreshToken?: string;
    instanceUrl?: string;
  }
}

================
File: src/utils/pkce.ts
================
import crypto from 'crypto';

export function generateCodeVerifier(): string {
  return crypto
    .randomBytes(32)
    .toString('base64')
    .replace(/[^a-zA-Z0-9]/g, '')
    .substring(0, 128);
}

export function generateCodeChallenge(verifier: string): string {
  return crypto
    .createHash('sha256')
    .update(verifier)
    .digest('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

================
File: src/middleware.ts
================
import { withAuth } from "next-auth/middleware";

export default withAuth({
    pages: {
        signIn: "/login",
    },
    callbacks: {
        authorized({ token }) {
            return !!token;
        },
    },
});

export const config = {
    matcher: [
        "/api/:path*",
        "/",
        "/agents/:path*",
        "/prompts/:path*",
        "/settings/:path*",
        "/leads/:path*",
        "/test-functions/:path*",
    ]
};

================
File: drizzle.config.ts
================
import type { Config } from 'drizzle-kit';
import * as dotenv from 'dotenv';
dotenv.config();

if (!process.env.DATABASE_URL) {
    throw new Error('DATABASE_URL is not set');
}

export default {
    schema: './src/db/schema',
    out: './drizzle',
    dialect: 'postgresql',
    dbCredentials: {
        url: process.env.DATABASE_URL,
        ssl: {
            rejectUnauthorized: true,
        },
    },
} satisfies Config;

================
File: README.md
================
This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

================
File: trigger.config.ts
================
import { defineConfig } from "@trigger.dev/sdk/v3";

export default defineConfig({
  project: "proj_xqnowmscshmhkyxmwakg",
  runtime: "node",
  logLevel: "log",
  // The max compute seconds a task is allowed to run. If the task run exceeds this duration, it will be stopped.
  // You can override this on an individual task.
  // See https://trigger.dev/docs/runs/max-duration
  maxDuration: 3600,
  retries: {
    enabledInDev: true,
    default: {
      maxAttempts: 3,
      minTimeoutInMs: 1000,
      maxTimeoutInMs: 10000,
      factor: 2,
      randomize: true,
    },
  },
  dirs: ["./src/trigger"],
});



================================================================
End of Codebase
================================================================

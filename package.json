{"name": "email-gen", "version": "0.1.0", "private": true, "packageManager": "pnpm@8.15.5", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "shadcn": "pnpm dlx shadcn-ui@latest add", "type-check": "tsc --noEmit", "check": "yarn type-check && yarn lint", "db:generate": "npx drizzle-kit generate", "db:push": "drizzle-kit push", "db:migrate": "tsx ./src/db/migrate.ts", "db:studio": "npx drizzle-kit studio"}, "dependencies": {"@anthropic-ai/sdk": "^0.32.1", "@auth/core": "^0.37.4", "@auth/drizzle-adapter": "^1.7.4", "@electric-sql/pglite": "^0.2.13", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.9.1", "@netlify/blobs": "^8.1.1", "@netlify/functions": "^2.6.0", "@netlify/plugin-nextjs": "^5.8.1", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.3", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.8", "@shadcn/ui": "^0.0.4", "@tanstack/react-query": "^5.28.4", "@tanstack/react-query-devtools": "^5.28.4", "@tavily/core": "^0.3.2", "@trigger.dev/sdk": "3.3.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.11.10", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.38.3", "drizzle-zod": "^0.7.0", "geist": "^1.3.1", "jose": "^5.9.6", "jsforce": "^3.6.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.363.0", "next": "14.1.3", "next-auth": "^4.24.7", "nodemailer": "^6.9.13", "openai": "^4.77.0", "pg": "^8.13.1", "postgres": "^3.4.3", "qs": "^6.13.1", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.51.1", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "tsx": "^4.7.1", "typescript-eslint": "^8.18.2", "uuid": "^10.0.0", "zod": "^3.22.4"}, "devDependencies": {"@trigger.dev/build": "3.3.17", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "autoprefixer": "^10.0.1", "drizzle-kit": "^0.30.1", "eslint": "^8", "eslint-config-next": "14.1.3", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "typescript": "5.7.2"}, "license": "MIT"}
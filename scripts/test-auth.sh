#!/bin/bash

# Script to test authentication on production server

BASE_URL="https://app.salesflow.team"
DEBUG_ENDPOINT="/api/v1/auth/debug"
WHATSAPP_ENDPOINT="/api/v1/settings/whatsapp"

echo "🔍 Testing authentication on $BASE_URL"
echo "================================================"

# Test 1: Debug endpoint without authentication
echo "📋 Test 1: Debug endpoint (no auth)"
echo "Endpoint: $BASE_URL$DEBUG_ENDPOINT"
echo "Expected: 200 OK with debug info"
echo ""

curl -s -w "Status: %{http_code}\n" \
     -H "Accept: application/json" \
     "$BASE_URL$DEBUG_ENDPOINT" | jq '.' 2>/dev/null || echo "Response is not valid JSON"

echo ""
echo "================================================"

# Test 2: WhatsApp API without authentication
echo "📋 Test 2: WhatsApp API (no auth)"
echo "Endpoint: $BASE_URL$WHATSAPP_ENDPOINT"
echo "Expected: 307 redirect to login"
echo ""

curl -s -w "Status: %{http_code}\nRedirect: %{redirect_url}\n" \
     -H "Accept: application/json" \
     "$BASE_URL$WHATSAPP_ENDPOINT"

echo ""
echo "================================================"

# Test 3: WhatsApp API with invalid token
echo "📋 Test 3: WhatsApp API (invalid token)"
echo "Endpoint: $BASE_URL$WHATSAPP_ENDPOINT"
echo "Expected: 307 redirect to login or 401 Unauthorized"
echo ""

curl -s -w "Status: %{http_code}\nRedirect: %{redirect_url}\n" \
     -H "Accept: application/json" \
     -H "Cookie: __Secure-next-auth.session-token=invalid_token" \
     "$BASE_URL$WHATSAPP_ENDPOINT"

echo ""
echo "================================================"

# Test 4: Check if NextAuth endpoints are accessible
echo "📋 Test 4: NextAuth endpoints"
echo "Endpoint: $BASE_URL/api/auth/session"
echo "Expected: 200 OK"
echo ""

curl -s -w "Status: %{http_code}\n" \
     -H "Accept: application/json" \
     "$BASE_URL/api/auth/session"

echo ""
echo "================================================"

echo "✅ Authentication tests completed!"
echo ""
echo "📝 Notes:"
echo "- If debug endpoint returns 200, the API is accessible"
echo "- If WhatsApp API returns 307, middleware is working correctly"
echo "- If NextAuth session endpoint returns 200, NextAuth is configured properly"
echo ""
echo "🔧 To test with valid authentication:"
echo "1. Login to the application in browser"
echo "2. Copy the session token from browser cookies"
echo "3. Run: curl -H 'Cookie: __Secure-next-auth.session-token=YOUR_TOKEN' $BASE_URL$WHATSAPP_ENDPOINT"

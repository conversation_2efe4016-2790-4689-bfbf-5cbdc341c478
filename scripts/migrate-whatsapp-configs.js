#!/usr/bin/env node

/**
 * Migration script to update whatsapp_configs.created_by from users.email to users.id
 * 
 * This script:
 * 1. Connects to the database
 * 2. Applies the migration SQL
 * 3. Verifies the migration was successful
 * 4. Provides rollback instructions if needed
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database connection from environment variables
const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

async function runMigration() {
    const client = await pool.connect();
    
    try {
        console.log('🔄 Starting WhatsApp configs migration...');
        
        // Read migration SQL
        const migrationPath = path.join(__dirname, '../src/db/migrations/0004_migrate_whatsapp_configs_created_by.sql');
        const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
        
        console.log('📋 Migration SQL loaded');
        
        // Check current state before migration
        console.log('🔍 Checking current state...');
        const beforeResult = await client.query(`
            SELECT 
                COUNT(*) as total_configs,
                COUNT(DISTINCT created_by) as unique_creators
            FROM whatsapp_configs
        `);
        
        console.log(`📊 Current state: ${beforeResult.rows[0].total_configs} configs, ${beforeResult.rows[0].unique_creators} unique creators`);
        
        // Check if migration is needed
        const schemaCheck = await client.query(`
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'whatsapp_configs' 
            AND column_name = 'created_by'
        `);
        
        if (schemaCheck.rows[0]?.data_type === 'integer') {
            console.log('✅ Migration already applied - created_by is already integer type');
            return;
        }
        
        console.log('🚀 Applying migration...');
        
        // Apply migration
        await client.query(migrationSQL);
        
        console.log('✅ Migration applied successfully!');
        
        // Verify migration
        console.log('🔍 Verifying migration...');
        
        const afterResult = await client.query(`
            SELECT 
                COUNT(*) as total_configs,
                COUNT(DISTINCT created_by) as unique_creators
            FROM whatsapp_configs
        `);
        
        const schemaVerify = await client.query(`
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'whatsapp_configs' 
            AND column_name = 'created_by'
        `);
        
        console.log(`📊 After migration: ${afterResult.rows[0].total_configs} configs, ${afterResult.rows[0].unique_creators} unique creators`);
        console.log(`📋 Schema: created_by is now ${schemaVerify.rows[0]?.data_type}`);
        
        if (beforeResult.rows[0].total_configs === afterResult.rows[0].total_configs) {
            console.log('✅ Data integrity verified - no records lost');
        } else {
            console.log('⚠️  Warning: Record count changed during migration');
        }
        
        console.log('🎉 Migration completed successfully!');
        
    } catch (error) {
        console.error('❌ Migration failed:', error);
        console.log('🔄 Rolling back...');
        
        try {
            await client.query('ROLLBACK');
            console.log('✅ Rollback successful');
        } catch (rollbackError) {
            console.error('❌ Rollback failed:', rollbackError);
        }
        
        throw error;
    } finally {
        client.release();
        await pool.end();
    }
}

// Run migration if called directly
if (require.main === module) {
    runMigration()
        .then(() => {
            console.log('✅ Migration script completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Migration script failed:', error);
            process.exit(1);
        });
}

module.exports = { runMigration };

#!/bin/bash

# Script to test authentication locally with user creation and login

BASE_URL="http://localhost:3001"
TEST_EMAIL="<EMAIL>"
TEST_PASSWORD="testpassword123"
TEST_NAME="Test User"

echo "🔍 Testing local authentication flow"
echo "================================================"

# Test 1: Create a test user
echo "📋 Test 1: Creating test user"
echo "Email: $TEST_EMAIL"
echo "Password: $TEST_PASSWORD"
echo ""

REGISTER_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
     -H "Content-Type: application/json" \
     -d "{\"email\":\"$TEST_EMAIL\",\"password\":\"$TEST_PASSWORD\",\"name\":\"$TEST_NAME\"}" \
     "$BASE_URL/api/auth/register")

HTTP_STATUS=$(echo $REGISTER_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $REGISTER_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

echo "Status: $HTTP_STATUS"
echo "Response: $RESPONSE_BODY"

if [ "$HTTP_STATUS" -eq 201 ] || [ "$HTTP_STATUS" -eq 400 ]; then
    echo "✅ User creation successful or user already exists"
else
    echo "❌ User creation failed"
    exit 1
fi

echo ""
echo "================================================"

# Test 2: Try to sign in with credentials
echo "📋 Test 2: Testing credentials sign in"
echo "Note: This will use NextAuth credentials provider"
echo ""

# We need to use NextAuth's sign in endpoint
SIGNIN_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "email=$TEST_EMAIL&password=$TEST_PASSWORD&csrfToken=test&callbackUrl=/" \
     -c cookies.txt \
     "$BASE_URL/api/auth/signin/credentials")

HTTP_STATUS=$(echo $SIGNIN_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $SIGNIN_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

echo "Status: $HTTP_STATUS"
echo "Response length: ${#RESPONSE_BODY}"

# Check if we got cookies
if [ -f cookies.txt ]; then
    echo "Cookies saved:"
    cat cookies.txt | grep -E "(session-token|csrf)"
    echo ""
else
    echo "No cookies file created"
fi

echo ""
echo "================================================"

# Test 3: Get CSRF token first
echo "📋 Test 3: Getting CSRF token"
echo ""

CSRF_RESPONSE=$(curl -s -c csrf_cookies.txt "$BASE_URL/api/auth/csrf")
echo "CSRF Response: $CSRF_RESPONSE"

# Extract CSRF token
CSRF_TOKEN=$(echo $CSRF_RESPONSE | grep -o '"csrfToken":"[^"]*"' | cut -d'"' -f4)
echo "CSRF Token: $CSRF_TOKEN"

echo ""
echo "================================================"

# Test 4: Try sign in with CSRF token
echo "📋 Test 4: Sign in with CSRF token"
echo ""

if [ ! -z "$CSRF_TOKEN" ]; then
    SIGNIN_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
         -H "Content-Type: application/x-www-form-urlencoded" \
         -d "email=$TEST_EMAIL&password=$TEST_PASSWORD&csrfToken=$CSRF_TOKEN&callbackUrl=/" \
         -b csrf_cookies.txt \
         -c auth_cookies.txt \
         "$BASE_URL/api/auth/signin/credentials")

    HTTP_STATUS=$(echo $SIGNIN_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    RESPONSE_BODY=$(echo $SIGNIN_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

    echo "Status: $HTTP_STATUS"
    echo "Response length: ${#RESPONSE_BODY}"

    if [ -f auth_cookies.txt ]; then
        echo "Auth cookies:"
        cat auth_cookies.txt
        echo ""
    fi
else
    echo "❌ No CSRF token received"
fi

echo ""
echo "================================================"

# Test 5: Test session endpoint
echo "📋 Test 5: Testing session endpoint"
echo ""

if [ -f auth_cookies.txt ]; then
    SESSION_RESPONSE=$(curl -s -b auth_cookies.txt "$BASE_URL/api/auth/session")
    echo "Session response: $SESSION_RESPONSE"
else
    SESSION_RESPONSE=$(curl -s "$BASE_URL/api/auth/session")
    echo "Session response (no cookies): $SESSION_RESPONSE"
fi

echo ""
echo "================================================"

# Test 6: Test debug endpoint with cookies
echo "📋 Test 6: Testing debug endpoint with cookies"
echo ""

if [ -f auth_cookies.txt ]; then
    DEBUG_RESPONSE=$(curl -s -b auth_cookies.txt "$BASE_URL/api/v1/auth/debug")
    echo "Debug response: $DEBUG_RESPONSE"
else
    echo "No auth cookies available for debug test"
fi

echo ""
echo "================================================"

# Test 7: Test WhatsApp API with cookies
echo "📋 Test 7: Testing WhatsApp API with cookies"
echo ""

if [ -f auth_cookies.txt ]; then
    WHATSAPP_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
         -b auth_cookies.txt \
         "$BASE_URL/api/v1/settings/whatsapp")

    HTTP_STATUS=$(echo $WHATSAPP_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    RESPONSE_BODY=$(echo $WHATSAPP_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

    echo "Status: $HTTP_STATUS"
    echo "Response: $RESPONSE_BODY"
else
    echo "No auth cookies available for WhatsApp API test"
fi

echo ""
echo "================================================"

# Cleanup
echo "🧹 Cleaning up temporary files"
rm -f cookies.txt csrf_cookies.txt auth_cookies.txt

echo "✅ Local authentication tests completed!"
echo ""
echo "📝 Summary:"
echo "- Test user creation/registration"
echo "- CSRF token retrieval"
echo "- Credentials sign in flow"
echo "- Session validation"
echo "- API authentication with cookies"

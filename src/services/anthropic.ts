// import Anthropic from '@anthropic-ai/sdk';
import { createChatCompletion } from '@/lib/anthropic';

// const SYSTEM_PROMPT = `You are an AI co-pilot for SalesFlow.Team, a cutting-edge service that automates sales emails and follow-ups using AI. Your primary function is to craft highly personalized, effective cold emails for high-value prospects for any cmr users.
// Your goal is to generate emails that stand out in crowded inboxes, demonstrate deep understanding of the prospect's needs, and compel them to take action.

// Always start your response with "Subject: " followed by a compelling subject line.
// After the subject line, add a blank line and then write the email body.`;
const SYSTEM_PROMPT = 'You are an AI assistant, which helps to user awesome and perfect things which user want. Make it step by step, with all details and explanations.'

export type AnthropicErrorType = 
  | 'invalid_request_error'
  | 'authentication_error'
  | 'permission_error'
  | 'not_found_error'
  | 'request_too_large'
  | 'rate_limit_error'
  | 'api_error'
  | 'overloaded_error'
  | 'temporary_unavailable';

export class APIError extends Error {
  constructor(
    public message: string,
    public status: number,
    public type: AnthropicErrorType,
    public requestId?: string,
    public error?: any
  ) {
    super(message);
    this.name = 'APIError';
  }
}

export class AnthropicService {
  // private client: Anthropic;

  // constructor() {
  //   if (!process.env.NEXT_ANTHROPIC_API_KEY) {
  //     throw new Error('NEXT_ANTHROPIC_API_KEY is required');
  //   }

  //   this.client = new Anthropic({
  //     apiKey: process.env.NEXT_ANTHROPIC_API_KEY,
  //   });
  // }

  private parseEmailResponse(response: string) {
    const lines = response.split('\n');
    let subject = '';
    let body = '';
    
    // Ищем строку с темой
    const subjectIndex = lines.findIndex(line => line.toLowerCase().startsWith('subject:'));
    if (subjectIndex !== -1) {
      subject = lines[subjectIndex].replace(/^subject:\s*/i, '').trim();
      // Удаляем строку с темой и пустые строки в начале
      body = lines.slice(subjectIndex + 1).join('\n').trim();
    } else {
      body = response.trim();
    }
    
    return { subject, body };
  }

  async generateEmail(prompt: string, settings = {}) {
    try {
      console.log('try to request to anthropic')
      console.log('settings', settings)
      const response = await createChatCompletion({
        model: 'anthropic/claude-3.5-haiku-20241022',
        max_tokens: 1024,
        ...settings,
        system: SYSTEM_PROMPT,
        messages: [{
          role: 'user',
          content: prompt
        }]
      });
      // console.log('response from anthropic', message)
      // // Извлекаем текст из первого блока контента
      // const textBlock = message.content.find(block => block.type === 'text');
      // if (!textBlock || !('text' in textBlock)) {
      //   throw new Error('No text content found in the response');
      // }

      // const response = textBlock.text;
      console.log('response from anthropic', response)
      const { subject, body } = this.parseEmailResponse(response);
      console.log('parsed response', { subject, body })
      return {
        responseContent: response,
        subject,
        body,
        
        // usage: {
        //   total_tokens: message.usage.input_tokens + message.usage.output_tokens,
        //   prompt_tokens: message.usage.input_tokens,
        //   completion_tokens: message.usage.output_tokens
        // }
      };
    } catch (error: any) {
      console.error('Error generating email with Anthropic:', error);
      
      const errorResponse = error.error?.error || {};
      const requestId = error.headers?.['x-request-id'];
      
      switch (error.status) {
        case 400:
          throw new APIError(
            errorResponse.message || 'Неверный формат запроса',
            400,
            'invalid_request_error',
            requestId,
            errorResponse
          );
          
        case 401:
          throw new APIError(
            errorResponse.message || 'Ошибка аутентификации API ключа',
            401,
            'authentication_error',
            requestId,
            errorResponse
          );
          
        case 403:
          throw new APIError(
            errorResponse.message || 'Отказано в доступе',
            403,
            'permission_error',
            requestId,
            errorResponse
          );
          
        case 404:
          throw new APIError(
            errorResponse.message || 'Ресурс не найден',
            404,
            'not_found_error',
            requestId,
            errorResponse
          );
          
        case 413:
          throw new APIError(
            errorResponse.message || 'Слишком большой запрос',
            413,
            'request_too_large',
            requestId,
            errorResponse
          );
          
        case 429:
          throw new APIError(
            errorResponse.message || 'Превышен лимит запросов',
            429,
            'rate_limit_error',
            requestId,
            errorResponse
          );

        case 502:
          throw new APIError(
            errorResponse.message || 'Сервис временно недоступен',
            502,
            'temporary_unavailable',
            requestId,
            errorResponse
          );
          
        case 529:
          throw new APIError(
            errorResponse.message || 'Сервис временно перегружен',
            529,
            'overloaded_error',
            requestId,
            errorResponse
          );
          
        default:
          throw new APIError(
            errorResponse.message || 'Внутренняя ошибка сервера',
            error.status || 500,
            'api_error',
            requestId,
            errorResponse
          );
      }
    }
  }
}

export const anthropic = new AnthropicService();

export interface PromptVariables {
  [key: string]: string | undefined;
  senderName?: string;
}

export function formatPrompt(template: string, variables: PromptVariables): string {
  let result = template;

  // Заменяем все переменные на их значения
  Object.entries(variables).forEach(([key, value]) => {
    if (value) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value);
    }
  });

  // Заменяем [Your Name] на имя отправителя или Nick по умолчанию
  result = result.replace(/\[Your Name\]/g, variables.senderName || 'Nick');

  return result;
}

export const COLD_EMAIL_PROMPT = `CONTEXT:
We are SalesFlow.Team service, Sales and Follow ups AI co-pilot (Sales Forced by AI). Our target is to schedule a demo with SFT prospects. As {{senderName}}, co-founder of SalesFlow.Team (SFT), craft a highly personalized cold email (as part of a 4 mail sequence) to a high-value prospect (also may be called sft prospect, recipient, target, lead, Lead, SFT lead) who is a Salesforce user, using context (below) and <PERSON>'s "Show Me You Know Me" strategy and the specific requirements for SFT.
Use the following sft lead general data and communication history (grabbed via API from our CRM) for your analysis. This part starts with: "Lead Details: "
Mostly you will see mails sent from {{senderName}} to SFT lead.
IMPORTANT in Lead history CRM marks email already sent to Lead as completed Task ("event_type": "Task")
Note that typically communication action (mail) from CRM history pushed to this prompt below starts with event_data and communication content after. Mail sent date is below the mail contents – as ActivityDate
LEAD DATA AND COMMUNICATIONS HISTORY FROM SALESFORCE. Use it for understanding the sequence of your new mail in outreach chain, and what was already told to prospect:

Lead Details: {{lead_details}}
Lead History: {{lead_history}}
Company Info: {{company_info}}

Please, understand if you will craft 1st or 2nd or 3rd mail - based on the Lead communication history. When you see the "event_type": "Task" and "activity_date": some recent dates it means that the mail or mails already were sent to Target some time in the past. IF SO YOU MUST MUST MUST reference to previous mails sent to Target.
Incorporate the following 7 elements from McKenna's strategy, while addressing specific needs:
1. Subject Line: Create a highly personalized, short, clear, and impactful subject line that references something unique about the prospect and captures attention.
2. First Sentence: Craft an engaging opener that avoids clichés, briefly introduces you as {{senderName}} from SalesFlow.Team, and shows you've done your homework on the prospect.
3. Transition: Authentically connect your opener to SFT's offering, leading into your pitch about AI-powered sales communication.
4. Challenge Focus: Highlight a specific challenge the prospect likely faces in their sales or fundraising process, emphasizing how SFT can address it.
5. Value Proposition: Present a compelling case for SFT, emphasizing what the prospect stands to lose without this AI co-pilot. Tailor this based on whether they're a commercial company (focus on increased sales and re-engaging dormant leads) or a nonprofit (emphasize efficient fundraising and outreach).
6. Address Objections: Acknowledge and preemptively address potential objections the prospect might have about adopting a new AI tool in their Salesforce workflow.
7. Call-to-Action: Include a clear and compelling CTA to schedule a 15-minute demo, giving the prospect agency in setting up the meeting.
Throughout the emails, emphasize SFT's key features and benefits:
- AI-generated intro emails from sft prospect to his / her leads contacts customers, follow-ups, quotes, and sales emails based on specific contact context, deal stage and full communication history tracked in Salesforce
- Context-aware drafting within Salesforce and AI-generated auto follow-ups at any deal stage (cold outreach, warming up, value prop, sales, post sales, customer care, new opportunities etc)
- Consideration of deal stages and specific items from past communications
- Ability to revive "dead leads" with personalized content
- Highlight the game-changing nature of these features.
- Mention successful pilot results, including increased response rates (up to 50% improvement)`;

export const DEMO_PROMPT = `Dear {{leadFirstName}},

As a fellow Salesforce user, I understand the challenges of managing a high-volume sales pipeline while delivering personalized communications at scale. That's why I'm excited to introduce you to SalesFlow.Team (SFT), an AI-powered sales co-pilot that seamlessly integrates with your Salesforce workflow.

SFT's cutting-edge technology leverages the full context of your lead and customer interactions to generate highly tailored emails, follow-ups, and sales content. By considering your deal stage, communication history, and specific pain points, our AI ensures every touchpoint is relevant, engaging, and tailored to your prospects' unique needs.

I imagine {{leadCompany}} faces similar obstacles in {{lead_details}} – from nurturing cold leads to reviving dormant opportunities. SFT's AI can breathe new life into your sales cycle, automating personalized outreach at scale while freeing your team to focus on high-value activities.

With SFT, you'll never miss an opportunity to re-engage a promising lead or lose momentum in a deal due to generic, impersonal communications. Our successful pilots have demonstrated up to 50% improvement in response rates, translating to increased sales and revenue.

I'd love to schedule a 15-minute demo to showcase how SFT can transform your sales process. When would be a convenient time for you this week?

If you'd prefer not to connect, simply reply with "stop" or "unsub" to this email.`;

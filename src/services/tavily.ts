/**
 * Сервис для работы с Tavily API
 */
const apiKey = process.env.NEXT_TAVILY_API_KEY;

console.log('apiKey', apiKey)

/**
 * Извлекает данные из URL с помощью Tavily API
 * @param url URL для извлечения данных
 * @param apiKey Ключ API Tavily
 * @param language Язык извлечения (по умолчанию 'ru')
 * @returns Результат извлечения данных
 */
export async function extractDataFromUrl(url: string) {
  if (!url) {
    return { success: false, error: 'URL не указан' };
  }
  console.log('apiKey 2', apiKey)
  try {
    const options = {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        urls: url,
        include_images: false,
        extract_depth: 'basic'
      })
    };
    
    const response = await fetch('https://api.tavily.com/extract', options);
    
    if (!response.ok) {
      throw new Error(`API вернул ошибку: ${response.status}`);
    }
    
    const extractResults = await response.json();
    return { success: true, data: extractResults };
  } catch (error) {
    console.error('Ошибка при извлечении данных из Tavily:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Неизвестная ошибка при извлечении данных' 
    };
  }
} 
import { db } from "@/db";
import { operations, OperationStatus, OperationType } from "@/db/schema/operations";
import { eq } from "drizzle-orm";

/**
 * Service for managing long-running operations
 */
export class OperationsService {
  /**
   * Create a new operation
   * @param type Type of operation
   * @param details Optional details about the operation
   * @returns The created operation with UUID
   */
  static async createOperation(type: OperationType, details?: string) {
    const [operation] = await db
      .insert(operations)
      .values({
        type,
        status: 'PENDING',
        details,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return operation;
  }

  /**
   * Update an operation's status
   * @param uuid UUID of the operation
   * @param status New status
   * @param error Optional error message (for ERROR status)
   * @returns The updated operation
   */
  static async updateOperationStatus(uuid: string, status: OperationStatus, error?: string) {
    const updateValues: any = {
      status,
      updatedAt: new Date(),
    };

    // Add error if provided
    if (error) {
      updateValues.error = error;
    }

    // Set completedAt if operation is completed
    if (status === 'SUCCESS' || status === 'ERROR' || status === 'CANCELLED') {
      updateValues.completedAt = new Date();
    }

    const [updatedOperation] = await db
      .update(operations)
      .set(updateValues)
      .where(eq(operations.uuid, uuid))
      .returning();

    return updatedOperation;
  }

  /**
   * Get an operation by UUID
   * @param uuid UUID of the operation
   * @returns The operation or null if not found
   */
  static async getOperation(uuid: string) {
    const [operation] = await db
      .select()
      .from(operations)
      .where(eq(operations.uuid, uuid))
      .limit(1);

    return operation || null;
  }

  /**
   * Get all operations of a specific type
   * @param type Type of operations to get
   * @param limit Maximum number of operations to return
   * @returns Array of operations
   */
  static async getOperationsByType(type: OperationType, limit = 100) {
    const operationsList = await db
      .select()
      .from(operations)
      .where(eq(operations.type, type))
      .limit(limit)
      .orderBy(operations.createdAt);

    return operationsList;
  }

  /**
   * Get all operations with a specific status
   * @param status Status of operations to get
   * @param limit Maximum number of operations to return
   * @returns Array of operations
   */
  static async getOperationsByStatus(status: OperationStatus, limit = 100) {
    const operationsList = await db
      .select()
      .from(operations)
      .where(eq(operations.status, status))
      .limit(limit)
      .orderBy(operations.createdAt);

    return operationsList;
  }
}

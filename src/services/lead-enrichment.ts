import { db } from "@/db";
import { leads } from "@/db/schema/leads";
import { eq } from "drizzle-orm";

/**
 * Service for lead enrichment functionality
 */
export class LeadEnrichmentService {
  /**
   * Process lead enrichment synchronously
   * @param operationUuid Optional UUID of the operation (not used when webhook creates the operation)
   * @param leadId ID of the lead to enrich
   * @param webhookUrl Webhook URL to send lead data to
   * @param lang Language for enrichment (default: "ru")
   * @returns The enrichment result with the UUID from the webhook
   */
  static async processLeadEnrichment(
    _operationUuid: string | null,
    leadId: number,
    webhookUrl: string,
    lang: string = "ru"
  ) {
    try {
      // Get lead data
      const [lead] = await db
        .select()
        .from(leads)
        .where(eq(leads.id, leadId))
        .limit(1);

      if (!lead) {
        return { success: false, error: "Lead not found" };
      }

      console.log(`Sending lead data to webhook: ${webhookUrl}`);

      // Add timeout to the fetch request to prevent hanging
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      try {
        const response = await fetch(webhookUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            leadId: lead.id,
            lang: lang,
          }),
          signal: controller.signal,
        });

        // Clear the timeout
        clearTimeout(timeoutId);

        if (!response.ok) {
          console.warn(`Webhook returned non-OK status: ${response.status}`);
          return {
            success: true,
            warning: `Webhook returned status ${response.status}, but operation completed`
          };
        }

        // Log the response status and headers for debugging
        console.log(`Webhook response status: ${response.status}`);
        console.log(`Webhook response headers:`, Object.fromEntries([...response.headers.entries()]));

        // Try to read the response as text first
        let responseText;
        try {
          responseText = await response.text();
          console.log(`Webhook response body: ${responseText}`);
        } catch (error) {
          const textError = error as Error;
          console.warn(`Could not read webhook response as text: ${textError.message}`);
          return {
            success: true,
            warning: "Could not read webhook response"
          };
        }

        // Try to parse the response as JSON if it's not empty
        let webhookResponse: any = {};
        if (responseText && responseText.trim()) {
          try {
            webhookResponse = JSON.parse(responseText);
            console.log("Parsed webhook response:", webhookResponse);
          } catch (error) {
            const parseError = error as Error;
            console.warn(`Could not parse webhook response as JSON: ${parseError.message}`);
            console.log(`Raw response: ${responseText}`);

            // Return success but without UUID
            return { success: true };
          }
        } else {
          console.log("Webhook returned empty response");
          return { success: true };
        }

        // Update lead with webhook response
        if (webhookResponse.output_format) {
          // Parse the output_format as text with paragraph breaks
          const formattedDescription = webhookResponse.output_format
            .replace(/\\n/g, "\n")
            .replace(/\n\n/g, "\n");

          console.log("Updating lead description with formatted output");

          // Update lead description with the formatted output
          await db
            .update(leads)
            .set({
              description: formattedDescription,
              updatedAt: new Date(),
            })
            .where(eq(leads.id, leadId));
        } else if (webhookResponse.text) {
          // Alternative format: direct text field
          console.log("Updating lead description with text field");

          await db
            .update(leads)
            .set({
              description: webhookResponse.text,
              updatedAt: new Date(),
            })
            .where(eq(leads.id, leadId));
        } else if (responseText && responseText.trim()) {
          // Fallback: use the raw response text as the description
          console.log("Using raw response text as description");

          // Try to determine if it's JSON-like content
          const isJsonLike = responseText.trim().startsWith('{') ||
                            responseText.trim().startsWith('[');

          // Only use raw text if it doesn't look like JSON
          if (!isJsonLike) {
            await db
              .update(leads)
              .set({
                description: responseText.substring(0, 5000), // Limit to 5000 chars in case it's very large
                updatedAt: new Date(),
              })
              .where(eq(leads.id, leadId));
          } else {
            console.log("Response appears to be JSON but couldn't be parsed, not using as description");
          }
        }

        // Return the webhook response with UUID and status
        return {
          success: true,
          uuid: webhookResponse.uuid,
          status: webhookResponse.status
        };
      } catch (fetchError: any) {
        // Clear the timeout if it's an error other than timeout
        clearTimeout(timeoutId);

        // For AbortError (timeout), provide a specific message
        if (fetchError.name === 'AbortError') {
          console.log("Webhook request timed out after 10 seconds");
          return {
            success: true,
            warning: "Webhook request timed out"
          };
        }

        console.error("Error calling webhook:", fetchError);
        return {
          success: true,
          warning: `Webhook error: ${fetchError.message || String(fetchError)}`
        };
      }
    } catch (error: any) {
      console.error("Error in lead enrichment:", error);
      return {
        success: false,
        error: `Lead enrichment error: ${error.message || String(error)}`
      };
    }
  }
}

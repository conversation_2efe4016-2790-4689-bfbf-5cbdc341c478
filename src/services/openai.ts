import { GeneratedEmail } from '@/types';

// Constants
const OPENROUTER_API = {
    URL: 'https://openrouter.ai/api/v1/chat/completions',
    MODELS: {
        GPT4: 'openai/gpt-4',
        GPT35: 'openai/gpt-3.5-turbo',
        CLAUDE: 'anthropic/claude-3-opus',
        HAIKU: 'anthropic/claude-3.5-haiku-20241022'
    } as const,
} as const;

type OpenRouterModel = typeof OPENROUTER_API.MODELS[keyof typeof OPENROUTER_API.MODELS];

interface EmailGenerationServiceConfig {
    model?: OpenRouterModel;
    siteUrl?: string;
    siteName?: string;
}

// Custom errors
class EmailGenerationError extends Error {
    constructor(message: string, public readonly cause?: unknown) {
        super(message);
        this.name = 'EmailGenerationError';
    }
}

interface EmailGenerationInput {
    firstName: string | null;
    lastName: string | null;
    company: string;
    title: string | null;
    status: string | null;
    salesforceId: string;
    email: string | null;
}

interface OpenRouterResponse {
    choices: Array<{
        message: {
            content: string;
            role: string;
        };
    }>;
}

class EmailGenerationService {
    private apiKey: string;
    private siteUrl: string;
    private siteName: string;
    private model: OpenRouterModel;

    constructor(config: EmailGenerationServiceConfig = {}) {
        const apiKey = process.env.NEXT_OPENROUTER_API_KEY ?? '';
        if (!apiKey) {
            throw new EmailGenerationError('NEXT_OPENROUTER_API_KEY environment variable is not set');
        }

        this.apiKey = apiKey;
        this.siteUrl = config.siteUrl || process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
        this.siteName = config.siteName || 'Email Generator';
        this.model = config.model || OPENROUTER_API.MODELS.HAIKU;
    }

    private createPrompt(lead: EmailGenerationInput): string {
        return `Generate a personalized sales email for:
Name: ${lead.firstName} ${lead.lastName}
${lead.company ? `Company: ${lead.company}\n` : ''}
${lead.title ? `Title: ${lead.title}\n` : ''}
${lead.status ? `Lead Status: ${lead.status}\n` : ''}

The email should be:
1. Professional and engaging
2. Personalized based on their role and company
3. Clear and concise
4. Include a specific call to action
5. No more than 3 paragraphs

Generate both subject line and email content.`;
    }

    private async makeApiRequest(prompt: string): Promise<OpenRouterResponse> {
        const response = await fetch(OPENROUTER_API.URL, {
            method: "POST",
            headers: {
                "Authorization": `Bearer ${this.apiKey}`,
                "HTTP-Referer": this.siteUrl,
                "X-Title": this.siteName,
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                "model": this.model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            })
        });

        if (!response.ok) {
            const error = await response.json().catch(() => ({ message: 'Unknown error' }));
            throw new EmailGenerationError('API request failed', error);
        }

        return response.json();
    }

    private parseResponse(data: OpenRouterResponse): { subject: string; content: string } {
        const generatedText = data.choices[0]?.message?.content;
        if (!generatedText) {
            throw new EmailGenerationError('No content in response');
        }

        const [subject, ...contentParts] = generatedText.split('\n').filter(Boolean);
        return {
            subject: subject.replace(/^Subject:\s*/i, ''),
            content: contentParts.join('\n').trim()
        };
    }

    async generateEmail(lead: EmailGenerationInput): Promise<GeneratedEmail> {
        try {
            const prompt = this.createPrompt(lead);
            const data = await this.makeApiRequest(prompt);
            const { subject, content } = this.parseResponse(data);

            return {
                subject,
                content,
                prompt
            };
        } catch (error) {
            console.error('Error generating email:', error);
            if (error instanceof EmailGenerationError) {
                throw error;
            }
            throw new EmailGenerationError('Failed to generate email', error);
        }
    }
}

// Экспортируем константы для возможности использования в других местах
export const MODELS = OPENROUTER_API.MODELS;

// Создаем экземпляр сервиса с Haiku по умолчанию
export const emailGenerator = new EmailGenerationService({
    model: OPENROUTER_API.MODELS.HAIKU
});

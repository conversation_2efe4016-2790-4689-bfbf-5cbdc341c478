import { db } from "@/db";
import { prompts, type Prompt as Db<PERSON>rom<PERSON>, type PromptSettings, type PromptMetadata } from "@/db/schema/prompts";
import { eq } from "drizzle-orm";

export type Prompt = DbPrompt;

export class PromptsManagerService {
  static async getPrompts(): Promise<Prompt[]> {
    try {
      const results = await db.select().from(prompts).orderBy(prompts.updatedAt);
      return results.map(prompt => ({
        ...prompt,
        settings: prompt.settings as PromptSettings,
        metadata: prompt.metadata as PromptMetadata
      }));
    } catch (error) {
      console.error("Error fetching prompts:", error);
      throw new Error("Failed to fetch prompts");
    }
  }

  static async getPromptById(id: number): Promise<Prompt | null> {
    try {
      const [result] = await db
        .select()
        .from(prompts)
        .where(eq(prompts.id, id))
        .limit(1);
      
      if (!result) return null;
      
      return {
        ...result,
        settings: result.settings as PromptSettings,
        metadata: result.metadata as PromptMetadata
      };
    } catch (error) {
      console.error("Error fetching prompt:", error);
      throw new Error("Failed to fetch prompt");
    }
  }

  static async createPrompt(prompt: Omit<Prompt, "id" | "createdAt" | "updatedAt">): Promise<Prompt> {
    try {
      const [result] = await db.insert(prompts).values(prompt).returning();
      return {
        ...result,
        settings: result.settings as PromptSettings,
        metadata: result.metadata as PromptMetadata
      };
    } catch (error) {
      console.error("Error creating prompt:", error);
      throw new Error("Failed to create prompt");
    }
  }

  static async updatePrompt(id: number, prompt: Partial<Omit<Prompt, "id" | "createdAt" | "updatedAt">>): Promise<Prompt> {
    try {
      const [result] = await db
        .update(prompts)
        .set({ ...prompt, updatedAt: new Date() })
        .where(eq(prompts.id, id))
        .returning();

      return {
        ...result,
        settings: result.settings as PromptSettings,
        metadata: result.metadata as PromptMetadata
      };
    } catch (error) {
      console.error("Error updating prompt:", error);
      throw new Error("Failed to update prompt");
    }
  }

  static async deletePrompt(id: number): Promise<void> {
    try {
      await db.delete(prompts).where(eq(prompts.id, id));
    } catch (error) {
      console.error("Error deleting prompt:", error);
      throw new Error("Failed to delete prompt");
    }
  }
}

import { Anthropic } from "@anthropic-ai/sdk";
import { salesforce } from "./salesforce";
import { db } from "../db";
import { emailHistory, emails, leads, emailConfigs } from "../db/schema";
import { eq } from "drizzle-orm";
import nodemailer from 'nodemailer';

// Initialize Anthropic client
const anthropic = new Anthropic({
  apiKey: process.env.NEXT_ANTHROPIC_API_KEY!,
});

// Helper function to safely get text from content blocks
function getTextFromContent(
  content: Anthropic.Messages.ContentBlock[]
): string {
  return content
    .filter(
      (block): block is Anthropic.Messages.TextBlock => block.type === "text"
    )
    .map((block) => block.text)
    .join("");
}

interface CompanyInfo {
  domain: string;
  name: string;
  industry: string;
  description: string;
  uses_salesforce: string;
  company_size: string;
  key_people: string;
  recent_news: string;
}

interface EmailGenerationResult {
  lead_id: string;
  email_id: number;
  subject: string;
  body: string;
  token_usage: {
    input_tokens: number;
    output_tokens: number;
    total_tokens: number;
    input_cost: number;
    output_cost: number;
    total_cost: number;
  };
}

interface WithCreatedDate {
  Id?: string;
  Subject?: string;
  Description?: string;
  CreatedDate: string;
  WhoId?: string;
  attributes?: {
    type: string;
  };
}

// Default prompt template
const AFTER_WEBINAR_PROMPT = `
CONTEXT:
We are SalesFlow.Team service, Sales and Follow ups AI co-pilot (Sales Forced by AI). Our target is to schedule a demo with SFT prospects. As Nick, co-founder of SalesFlow.Team (SFT), craft a highly personalized cold email (as part of a 4 mail sequence) to a high-value prospect (also may be called sft prospect, recipient, target, lead, Lead, SFT lead) who is a Salesforce user, using context (below) and Samantha McKenna's "Show Me You Know Me" strategy and the specific requirements for SFT.
Use the following sft lead general data and communication history (grabbed via API from our CRM) for your analysis. This part starts with: "Lead Details: "
Mostly you will see mails sent from Nick to SFT lead.
IMPORTANT in Lead history CRM marks email already sent to Lead as completed Task (\"event_type\": \"Task\")
Note that typically communication action (mail) from CRM history pushed to this prompt below starts with event_data and communication content after. Mail sent date is below the mail contents – as ActivityDate
LEAD DATA AND COMMUNICATIONS HISTORY FROM SALESFORCE. Use it for understanding the sequence of your new mail in outreach chain, and what was already told to prospect:

Lead Details: {{lead_details}}
Lead History: {{lead_history}}
Company Info: {{company_info}}

Please, understand if you will craft 1st or 2nd or 3rd mail - based on the Lead communication history. When you see the "event_type": "Task" and "activity_date": some recent dates it means that the mail or mails already were sent to Target some time in the past. IF SO YOU MUST MUST MUST reference to previous mails sent to Target.
Incorporate the following 7 elements from McKenna's strategy, while addressing specific needs:
1. Subject Line: Create a highly personalized, short, clear, and impactful subject line that references something unique about the prospect and captures attention.
2. First Sentence: Craft an engaging opener that avoids clichés, briefly introduces you as Nick from SalesFlow.Team, and shows you've done your homework on the prospect.
3. Transition: Authentically connect your opener to SFT's offering, leading into your pitch about AI-powered sales communication.
4. Challenge Focus: Highlight a specific challenge the prospect likely faces in their sales or fundraising process, emphasizing how SFT can address it.
5. Value Proposition: Present a compelling case for SFT, emphasizing what the prospect stands to lose without this AI co-pilot. Tailor this based on whether they're a commercial company (focus on increased sales and re-engaging dormant leads) or a nonprofit (emphasize efficient fundraising and outreach).
6. Address Objections: Acknowledge and preemptively address potential objections the prospect might have about adopting a new AI tool in their Salesforce workflow.
7. Call-to-Action: Include a clear and compelling CTA to schedule a 15-minute demo, giving the prospect agency in setting up the meeting.
Throughout the emails, emphasize SFT's key features and benefits:
- AI-generated intro emails from sft prospect to his / her leads contacts customers, follow-ups, quotes, and sales emails based on specific contact context, deal stage and full communication history tracked in Salesforce
- Context-aware drafting within Salesforce and AI-generated auto follow-ups at any deal stage (cold outreach, warming up, value prop, sales, post sales, customer care, new opportunities etc)
- Consideration of deal stages and specific items from past communications
- Ability to revive "dead leads" with personalized content
- Highlight the game-changing nature of these features.
- Mention successful pilot results, including increased response rates (up to 50% improvement)
Additional considerations:
Tailor the message depending on the industry:
• For commercial companies, emphasize increased sales via focused lead management and personalized, context-driven communications.
• For nonprofits, focus on more efficient fundraising and outreach through highly tailored messaging.
• If the target is a Salesforce partner (e.g., ISV, reseller, or consulting partner), suggest a potential partnership or white-labeling option.
• Request a 15-minute demo to showcase the product's features in action.
- If the target is a Salesforce partner, suggest a potential partnership or white-labeling option
- Mention that SFT is early-stage but has completed several successful pilots
- ANALIZE THE MAIL SEQUENCE and understand which is this mail in outreach chain - (you see history, remember "event_type": "Task" means mail sent and Activity date). If this is the 2nd or 3rd or 4rth outreach message  DO NOT REPEAT EXACTLY ON WHAT WAS ALREADY TOLD AND YOU MUST politely mention the previous emails.
- For 2nd mail - Important – please - reference the previous emails. - Highlight additional benefits of SFT, such as: - AI considering specific items from communication history. - Ability to automate communications throughout the sales cycle. - Reiterate the unique value in reviving "dead leads." - Include a clear call-to-action. Tone: Courteous, professional, and focused.
- For 3rd mail - Politely reference the previous emails. The email should: - Acknowledge that they may be busy. - Share a compelling success story or pilot result demonstrating how SFT has benefited similar organizations. - Create a sense of urgency if appropriate (e.g., limited pilot spots). - Provide a soft call-to-action. Tone: Understanding, respectful, and encouraging. Add additional value - for maybe junior sales - AI Co-pilot help.
- For 4th 5th and 6th mails - Politely reference the previous emails. The email should: - Be respectful and professional. -- Briefly summarize the key benefits of SFT. - Allow the recipient an easy way to opt-out or reconnect in the future. - Maintain a positive tone.
- If the company name is clear, mention it in the email body
- Keep the entire email under 200 words
- Maintain a human-style (not verified as AI generated), professional, engaging, and respectful tone
Analyze the recipient's company website and LinkedIn profile for additional context to personalize the message further.
If this is potentially the wrong contact, include a request to forward the email to the appropriate sales or marketing lead.
Do not include any links.

Add that if recipient does not want to be connected any more - just reply with "stop" or "unsub" to this mail
Your response should be in the following format exactly, with no additional text or explanations:
Subject: [Your subject line here]
[Email body]
`;

export class EmailService {
  private static formatLeadHistory(historyData: WithCreatedDate[]): string {
    const emailHistory = historyData
      .filter((record) => {
        return (
          record.attributes?.type === "Task" &&
          record.Subject?.startsWith("Email:") &&
          record.WhoId
        );
      })
      .map((record) => {
        const createdDate = new Date(record.CreatedDate);
        const recipientName = record.Description?.split(",")[0] || "Unknown";
        const recipientEmail = `${recipientName
          .toLowerCase()
          .replace(" ", ".")}`;

        return {
          lead_id: record.WhoId,
          from_email: "<EMAIL>",
          to_email: recipientEmail,
          date: createdDate,
          subject: record.Subject?.replace("Email: ", "") || "",
          body: record.Description || "",
        };
      })
      .sort((a, b) => b.date.getTime() - a.date.getTime());

    return (
      emailHistory
        .map(
          (email) => `
------------------------------------
From: ${email.from_email}
Date: ${email.date.toUTCString()}
Subject: ${email.subject}

${email.body}`
        )
        .join("\n") || "No email history found."
    );
  }

  static async getCompanyInfo(
    emailDomain: string | undefined,
    companyName: string | undefined
  ): Promise<CompanyInfo> {
    const domain = emailDomain
      ? emailDomain.split("@").pop()?.toLowerCase()
      : "unknown";
    const company = companyName || "Unknown Company";

    const emailProviders = [
      "gmail.com",
      "yahoo.com",
      "hotmail.com",
      "outlook.com",
    ];
    if (domain && emailProviders.includes(domain)) {
      return {
        domain: "Unknown",
        name: company,
        industry: "Unknown",
        description: "Unknown",
        uses_salesforce: "Unknown",
        company_size: "Unknown",
        key_people: "Unknown",
        recent_news: "Unknown",
      };
    }

    try {
      const prompt = `
        You are an AI assistant tasked with gathering information about a company. Provide a structured summary in JSON format for the following company:

        Company Name: ${company}
        Domain: ${domain}

        Please include the following details:
        - Overview: products/services, industry
        - Recent news (6 months)
        - Key leaders
        - Size, growth
        - CRM/Salesforce use
        - Industry challenges
        - Sales/marketing approach
        - Unique facts

        Ensure all fields are filled, using "Unknown" if information is not available.
      `;

      const message = await anthropic.messages.create({
        model: "claude-3-haiku-20240307",
        max_tokens: 1024,
        temperature: 0.2,
        messages: [{ role: "user", content: prompt }],
      });

      const content = getTextFromContent(message.content);
      if (!content) {
        throw new Error("No content received from Anthropic API");
      }

      return JSON.parse(content);
    } catch (error) {
      console.error("Error getting company info:", error);
      return {
        domain: domain || "Unknown",
        name: company,
        industry: "Unknown",
        description: "Unknown",
        uses_salesforce: "Unknown",
        company_size: "Unknown",
        key_people: "Unknown",
        recent_news: "Unknown",
      };
    }
  }

  static async generatePersonalizedEmail(
    salesforceId: string,
    customPrompt?: string
  ): Promise<EmailGenerationResult> {
    try {
      const lead = await db
        .select()
        .from(leads)
        .where(eq(leads.salesforceId, salesforceId))
        .limit(1);

      if (!lead || !lead[0]) {
        throw new Error(`Lead with Salesforce ID ${salesforceId} not found`);
      }

      const sfLead = await salesforce.getLeadById(salesforceId);
      const leadHistory = await salesforce.getLeadComprehensiveHistory(
        salesforceId
      );
      const _formattedHistory = this.formatLeadHistory(leadHistory);
      const companyInfo = await this.getCompanyInfo(
        sfLead.Email,
        sfLead.Company
      );

      const basePrompt = customPrompt || AFTER_WEBINAR_PROMPT;
      const prompt = basePrompt
        .replace("{{lead_details}}", JSON.stringify(sfLead, null, 2))
        .replace("{{lead_history}}", JSON.stringify(leadHistory, null, 2))
        .replace("{{company_info}}", JSON.stringify(companyInfo, null, 2));

      const message = await anthropic.messages.create({
        model: "claude-3-sonnet-20240229",
        max_tokens: 3000,
        temperature: 0.2,
        messages: [{ role: "user", content: prompt }],
      });

      const content = getTextFromContent(message.content);
      const generatedEmail =
        content ||
        "Subject: Follow-up regarding SalesFlow.Team\n\nDefault email body";

      const lines = generatedEmail.split("\n");
      const subject = lines[0].replace("Subject:", "").trim();
      const body = lines.slice(2).join("\n").trim();

      const leadInternalId = lead[0].id;

      const [savedEmail] = await db
        .insert(emails)
        .values({
          leadId: Number(leadInternalId),
          subject,
          content: body,
          prompt: customPrompt || body,
          sent: false,
        })
        .returning();

      return {
        lead_id: salesforceId,
        email_id: savedEmail.id,
        subject,
        body,
        token_usage: {
          input_tokens: 0,
          output_tokens: 0,
          total_tokens: 0,
          input_cost: 0,
          output_cost: 0,
          total_cost: 0,
        },
      };
    } catch (error) {
      console.error("Error generating personalized email:", error);
      throw error;
    }
  }

  static async sendEmail(
    emailId?: number,
    salesforceId?: string,
    emailConfigId?: string
  ): Promise<[string, any]> {
    if (!emailId && !salesforceId) {
      throw new Error("Either emailId or salesforceId must be provided");
    }

    let tokenUsage = null;
    let subject: string;
    let body: string;
    let targetSalesforceId: string;
    let recipientEmail: string;

    if (emailId && !salesforceId) {
      const emailWithLead = await db
        .select({
          email: emails,
          lead: leads,
        })
        .from(emails)
        .where(eq(emails.id, emailId))
        .innerJoin(leads, eq(emails.leadId, leads.id))
        .limit(1);

      if (!emailWithLead || !emailWithLead[0]) {
        throw new Error(`No email found with id ${emailId}`);
      }

      targetSalesforceId = emailWithLead[0].lead.salesforceId || '';
      subject = emailWithLead[0].email.subject;
      body = emailWithLead[0].email.content;
      recipientEmail = emailWithLead[0].lead.email || '';
    } else if (salesforceId) {
      const emailData = await this.generatePersonalizedEmail(salesforceId);
      emailId = emailData.email_id;
      targetSalesforceId = emailData.lead_id;
      subject = emailData.subject;
      body = emailData.body;
      tokenUsage = emailData.token_usage;

      const lead = await salesforce.getLeadById(salesforceId);
      recipientEmail = lead.Email || '';
    } else {
      // Get email data directly from the database
      const emailData = await db
        .select({
          email: emails,
          lead: leads,
        })
        .from(emails)
        .where(eq(emails.id, emailId!))
        .innerJoin(leads, eq(emails.leadId, leads.id))
        .limit(1);

      if (!emailData || !emailData[0]) {
        throw new Error(`No email found with id ${emailId}`);
      }

      targetSalesforceId = emailData[0].lead.salesforceId || '';
      subject = emailData[0].email.subject;
      body = emailData[0].email.content;
      recipientEmail = emailData[0].lead.email || '';
    }

    try {
      if (emailConfigId) {
        // Get email configuration
        const config = await db
          .select()
          .from(emailConfigs)
          .where(eq(emailConfigs.id, emailConfigId))
          .limit(1);

        if (!config || !config[0]) {
          throw new Error(`No email configuration found with id ${emailConfigId}`);
        }

        const emailConfig = config[0];

        // Validate required SMTP configuration
        if (!emailConfig.smtp || !emailConfig.email || !emailConfig.password) {
          throw new Error("Invalid SMTP configuration: missing required fields");
        }

        // Parse port number and set secure option
        const port = parseInt(emailConfig.port.toString(), 10);
        if (isNaN(port)) {
          throw new Error("Invalid SMTP port number");
        }

        // Create nodemailer transporter with proper configuration
        const transporter = nodemailer.createTransport({
          host: emailConfig.smtp,
          port: port,
          secure: port === 465, // true for 465, false for other ports
          auth: {
            user: emailConfig.email,
            pass: emailConfig.password,
          },
          tls: {
            // Do not fail on invalid certificates
            rejectUnauthorized: false
          }
        });

        // Verify SMTP connection configuration
        await transporter.verify();

        // Обработка нескольких email адресов, если они указаны через запятую
        const emailRecipients = recipientEmail.split(',').map(email => email.trim()).filter(Boolean);

        // Send email using nodemailer
        const info = await transporter.sendMail({
          from: emailConfig.email,
          to: emailRecipients.join(', '),
          subject: subject,
          text: body,
        });

        if (!info.messageId) {
          throw new Error("Failed to send email: no message ID received");
        }

        // Update email status in database
        await db
          .update(emails)
          .set({
            sent: true,
          })
          .where(eq(emails.id, emailId!));

        await db
          .update(emailHistory)
          .set({
            sentAt: new Date(),
          })
          .where(eq(emailHistory.emailId, emailId!));

        return [
          `Email was successfully sent using custom email configuration (Message ID: ${info.messageId})`,
          tokenUsage,
        ];
      } else {
        // Fallback to Salesforce email sending
        const result = await salesforce.sendEmail({
          targetLeadId: targetSalesforceId,
          subject,
          body,
        });

        if (result.success) {
          await db
            .update(emails)
            .set({
              sent: true,
            })
            .where(eq(emails.id, emailId!));

          await db
            .update(emailHistory)
            .set({
              sentAt: new Date(),
            })
            .where(eq(emailHistory.emailId, emailId!));

          return [
            `Email was successfully sent and logged as a task. Task ID: ${result.taskId}`,
            tokenUsage,
          ];
        } else {
          return ["Failed to send email.", tokenUsage];
        }
      }
    } catch (error) {
      console.error("Error sending email:", error);
      throw error;
    }
  }
}

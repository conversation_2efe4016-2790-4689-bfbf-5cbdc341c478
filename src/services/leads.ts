import { leads } from "@/db/schema";

export type Lead = typeof leads.$inferSelect;
export type NewLead = typeof leads.$inferInsert;

export const LeadsService = {
    async getLeads(params?: {
        page?: number;
        limit?: number;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
        search?: string;
        ids?: number[];
    }) {
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.set('page', params.page.toString());
        if (params?.limit) searchParams.set('limit', params.limit.toString());
        if (params?.sortBy) searchParams.set('sortBy', params.sortBy);
        if (params?.sortOrder) searchParams.set('sortOrder', params.sortOrder);
        if (params?.search) searchParams.set('search', params.search);
        if (params?.ids?.length) searchParams.set('ids', params.ids.join(','));

        const response = await fetch(`/api/leads?${searchParams.toString()}`);
        if (!response.ok) {
            throw new Error('Failed to fetch leads');
        }
        const data = await response.json();

        // Проверяем структуру данных
        if (!Array.isArray(data?.leads)) {
            throw new Error('Invalid leads data format');
        }

        return {
            leads: data.leads,
            pagination: data.pagination || {
                total: data.leads.length,
                page: 1,
                limit: data.leads.length,
                totalPages: 1
            }
        };
    },

    async getLead(id: number) {
        const response = await fetch(`/api/leads/${id}`);
        if (!response.ok) {
            throw new Error('Failed to fetch lead');
        }
        return await response.json();
    },

    async createLead(data: NewLead) {
        const response = await fetch('/api/leads', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        });
        if (!response.ok) {
            throw new Error('Failed to create lead');
        }
        return await response.json();
    },

    async updateLead(id: number, data: Partial<NewLead>) {
        const response = await fetch(`/api/leads/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        });
        if (!response.ok) {
            throw new Error('Failed to update lead');
        }
        return await response.json();
    },

    async deleteLead(id: number) {
        const response = await fetch(`/api/leads/${id}`, {
            method: 'DELETE',
        });
        if (!response.ok) {
            throw new Error('Failed to delete lead');
        }
        return await response.json();
    },
}

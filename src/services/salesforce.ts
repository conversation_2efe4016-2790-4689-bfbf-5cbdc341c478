import * as jsforce from "jsforce";
import { SalesforceTokens } from "@/types";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

// Define Lead interface to match Salesforce Lead object structure
interface SalesforceLead {
    Id: string;
    FirstName?: string;
    LastName?: string;
    Email?: string;
    Company?: string;
    Title?: string;
    Phone?: string;
    Status?: string;
    LastModifiedDate?: string;
    CreatedDate?: string;
    Rating?: string;
    Industry?: string;
    LeadSource?: string;
    Description?: string;
    Website?: string;
    NumberOfEmployees?: number;
    Street?: string;
    City?: string;
    State?: string;
    PostalCode?: string;
    Country?: string;
}

interface SendEmailParams {
    targetLeadId: string;
    subject: string;
    body: string;
}

interface SendEmailResult {
    success: boolean;
    taskId?: string;
    error?: string;
}

interface EmailSimpleResponse {
    isSuccess: boolean;
    errors?: Array<{ message: string }>;
}

interface WithCreatedDate {
    CreatedDate: string;
}

class SalesforceService {
    private conn: jsforce.Connection;
    private oauth2: jsforce.OAuth2;
    private isAuthenticated: boolean = false;

    constructor() {
        // Initialize OAuth2 configuration
        this.oauth2 = new jsforce.OAuth2({
            clientId: process.env.NEXT_SALESFORCE_CLIENT_ID!,
            clientSecret: process.env.NEXT_SALESFORCE_CLIENT_SECRET!,
            redirectUri: `${process.env.NEXTAUTH_URL}/api/v1/salesforce/auth/callback`,
        });

        // Initialize connection
        this.conn = new jsforce.Connection({
            oauth2: this.oauth2,
        });
    }

    private async getSession() {
        try {
            const session = await getServerSession(authOptions);
            if (!session?.accessToken || !session?.instanceUrl) {
                const error = new Error("Not authenticated with Salesforce");
                (error as any).redirectTo = "/api/auth/signin";
                throw error;
            }
            return session;
        } catch (error) {
            console.error("Error getting session:", error);
            throw error;
        }
    }

    private async ensureAuth(): Promise<void> {
        try {
            const session = await this.getSession();

            if (!session.accessToken || !session.instanceUrl) {
                throw new Error("Missing Salesforce tokens");
            }

            // Set up connection with session tokens
            this.conn = new jsforce.Connection({
                oauth2: this.oauth2,
                accessToken: session.accessToken,
                instanceUrl: session.instanceUrl,
                refreshToken: session.refreshToken,
            });

            // Verify the connection
            await this.conn.identity();
            this.isAuthenticated = true;
        } catch (error) {
            console.error("Error ensuring auth:", error);
            this.isAuthenticated = false;
            throw error;
        }
    }

    async getCurrentUserId(): Promise<string> {
        await this.ensureAuth();
        const userInfo = await this.conn.identity();
        return userInfo.user_id;
    }

    async getLeads(
        lastSyncDate?: Date
    ): Promise<(jsforce.Lead & { displayName: string })[]> {
        try {
            await this.ensureAuth();

            let query = `
        SELECT Id, FirstName, LastName, Email, Company, Title, Phone, Status,
               LastModifiedDate, CreatedDate, Rating, Industry, LeadSource,
               Description, Website, NumberOfEmployees
        FROM Lead
        WHERE IsDeleted = false
      `;

            if (lastSyncDate) {
                query += ` AND LastModifiedDate > ${lastSyncDate.toISOString()}`;
            }

            query += " ORDER BY LastModifiedDate DESC LIMIT 1000";

            const result = await this.conn.query<SalesforceLead>(query);

            // Transform the records to include a display name
            return result.records.map((lead) => ({
                ...lead,
                displayName: `${lead.FirstName || ""} ${lead.LastName || ""} - ${lead.Company || "No Company"
                    }`.trim(),
            }));
        } catch (error) {
            console.error("Error fetching leads:", error);
            throw error;
        }
    }

    async getLeadById(id: string): Promise<jsforce.Lead> {
        try {
            await this.ensureAuth();

            const result = await this.conn.query<jsforce.Lead>(`
        SELECT Id, FirstName, LastName, Description, Email, Company, Title, Phone, Status,
               LastModifiedDate, CreatedDate, Rating, Industry, LeadSource
        FROM Lead
        WHERE Id = '${id}'
        AND IsDeleted = false
      `);

            if (!result.records.length) {
                throw new Error("Lead not found");
            }

            return result.records[0] as jsforce.Lead;
        } catch (error) {
            console.error("Error fetching lead:", error);
            throw error;
        }
    }

    async updateLead(
        id: string,
        data: Partial<jsforce.Lead>
    ): Promise<jsforce.SaveResult> {
        try {
            await this.ensureAuth();

            const result = (await this.conn.sobject("Lead").update({
                Id: id,
                ...data,
            })) as jsforce.SaveResult;

            if (!result.success) {
                throw new Error("Failed to update lead");
            }

            return result;
        } catch (error) {
            console.error("Error updating lead:", error);
            throw error;
        }
    }

    async searchLeads(searchTerm: string): Promise<jsforce.SearchRecord[]> {
        try {
            await this.ensureAuth();

            const sosl = `
        FIND {${searchTerm}}
        IN ALL FIELDS
        RETURNING Lead(
          Id, FirstName, LastName, Email, Company, Title, Phone, Status,
          LastModifiedDate, CreatedDate, Rating, Industry, LeadSource
          WHERE IsDeleted = false
        )
        LIMIT 1000
      `;

            const result = await this.conn.search<jsforce.SearchRecord>(sosl);
            return result.searchRecords;
        } catch (error) {
            console.error("Error searching leads:", error);
            throw error;
        }
    }

    async describeLead(): Promise<jsforce.DescribeSObjectResult> {
        try {
            await this.ensureAuth();
            return await this.conn.describe();
        } catch (error) {
            console.error("Error describing Lead object:", error);
            throw error;
        }
    }

    async getLeadComprehensiveHistory(leadId: string) {
        try {
            await this.ensureAuth();

            // GET TASKS
            const tasks = await this.conn.query<WithCreatedDate>(
                `SELECT Id, Subject, Description, ActivityDate, Status, WhoId, CreatedDate 
       FROM Task 
       WHERE WhoId = '${leadId}' 
       ORDER BY CreatedDate ASC`
            );

            // GET EVENTS
            const events = await this.conn.query<WithCreatedDate>(
                `SELECT Id, Subject, Description, StartDateTime, EndDateTime, ActivityDate, WhoId, CreatedDate 
       FROM Event 
       WHERE WhoId = '${leadId}' 
       ORDER BY CreatedDate ASC`
            );

            // GET FIELD HISTORY
            const fieldHistory = await this.conn.query<WithCreatedDate>(
                `SELECT Field, OldValue, NewValue, CreatedDate 
       FROM LeadHistory 
       WHERE LeadId = '${leadId}' 
       ORDER BY CreatedDate ASC`
            );

            const allHistory = [
                ...tasks.records,
                ...events.records,
                ...fieldHistory.records,
            ].sort(
                (a, b) =>
                    new Date(a.CreatedDate).getTime() - new Date(b.CreatedDate).getTime()
            );

            return allHistory;
        } catch (error) {
            console.error("Failed to retrieve lead history", error);
            throw error;
        }
    }

    getAuthorizationUrl() {
        // Генерируем code verifier для PKCE
        const codeVerifier = [...Array(96)]
            .map(() => Math.random().toString(36)[2])
            .join('');

        // Создаем code challenge из code verifier
        const codeChallenge = codeVerifier; // В реальном приложении здесь должен быть SHA256

        // Получаем URL авторизации с PKCE
        const url = this.oauth2.getAuthorizationUrl({
            scope: 'api refresh_token',
            code_challenge: codeChallenge,
            code_challenge_method: 'plain', // В реальном приложении должно быть 'S256'
        });

        return { url, codeVerifier };
    }

    async getAccessToken(code: string, codeVerifier: string): Promise<SalesforceTokens> {
        try {
            const userInfo = await this.oauth2.requestToken(code, { code_verifier: codeVerifier });
            return {
                access_token: userInfo.access_token,
                refresh_token: userInfo.refresh_token,
                instance_url: userInfo.instance_url,
            };
        } catch (error) {
            console.error('Error getting access token:', error);
            throw error;
        }
    }

    setTokens(accessToken: string, refreshToken?: string, instanceUrl?: string) {
        this.conn = new jsforce.Connection({
            oauth2: this.oauth2,
            accessToken,
            refreshToken,
            instanceUrl,
        });
    }

    async sendEmail(params: SendEmailParams): Promise<SendEmailResult> {
        try {
            await this.ensureAuth();

            const lead = await this.getLeadById(params.targetLeadId);
            const leadEmail = lead.Email;

            if (!leadEmail) {
                throw new Error("No email was found");
            }

            // Send email using Salesforce API
            const response = (await this.conn.request({
                method: "POST",
                url: "/actions/standard/emailSimple",
                headers: {
                    Authorization: "Bearer " + this.conn.accessToken,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    inputs: [
                        {
                            emailSubject: params.subject,
                            emailBody: params.body,
                            emailAddresses: leadEmail,
                        },
                    ],
                }),
            })) as EmailSimpleResponse[];

            if (!response[0]?.isSuccess) {
                return {
                    success: false,
                    error: response[0]?.errors?.[0]?.message || "Failed to send email",
                };
            }

            // Log email as a task
            const taskResult = await this.conn.sobject("Task").create({
                WhoId: params.targetLeadId,
                Subject: `Email: ${params.subject}`,
                Description: params.body,
                ActivityDate: new Date().toISOString(),
                Status: "Completed",
                Priority: "Normal",
                TaskSubtype: "Email",
            });

            return {
                success: true,
                taskId: taskResult.id,
            };
        } catch (error) {
            console.error("Error sending email:", error);
            return {
                success: false,
                error:
                    error instanceof Error ? error.message : "Unknown error occurred",
            };
        }
    }
}

// Export a singleton instance
export const salesforce = new SalesforceService();

import { db } from "@/db";
import { leads, whatsappMessages, whatsappHistory, whatsappConfigs, agents } from "@/db/schema";
import { eq } from "drizzle-orm";
import { validatePhoneNumber } from "@/utils/phone-validation";
import {
  parseWhatsAppPlaceholders,
  normalizeWhatsAppPromptVariables,
  WhatsAppContext
} from "@/utils/whatsapp-placeholder-parser";
import { WhatsAppLogger, LogContext } from "@/utils/whatsapp-logger";

// Re-export for backward compatibility
export { validatePhoneNumber } from "@/utils/phone-validation";

// OpenRouter API configuration
const OPENROUTER_API = {
  URL: 'https://openrouter.ai/api/v1/chat/completions',
  MODELS: {
    HAIKU: 'anthropic/claude-3.5-haiku-20241022',
    SONNET: 'anthropic/claude-3-sonnet-20240229',
    SONNET_35: 'anthropic/claude-3.5-sonnet-20241022',
    OPUS: 'anthropic/claude-3-opus-20240229',
  } as const,
} as const;

// OpenRouter response interface
interface OpenRouterResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage?: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
  };
}

// WhatsApp message generation prompt
const WHATSAPP_PROMPT = `
Ты - профессиональный менеджер по продажам, который пишет WhatsApp сообщение потенциальному клиенту.

ВАЖНО: Ты должен создать сообщение ОТ ИМЕНИ отправителя ДЛЯ получателя, а НЕ ответ от получателя!

Информация о лиде (получателе):
- Имя: {{leadFirstName}} {{leadLastName}}
- Компания: {{leadCompany}}
- Должность: {{leadTitle}}
- Отрасль: {{leadIndustry}}
- Город: {{details_city}}
- LinkedIn: {{communications_linkedin}}

Информация об отправителе (тебе):
- Имя: {{senderName}}
- Компания: {{senderCompany}}
- Должность: {{senderPosition}}

Инструкции:
1. Создай персонализированное WhatsApp сообщение (максимум 300 символов)
2. Пиши от первого лица (от имени {{senderName}})
3. Будь дружелюбным и разговорным
4. Включи четкий призыв к действию
5. Используй эмодзи уместно
6. Сохраняй профессионализм, но будь неформальным
7. Упоминай конкретные детали о лиде, если они доступны
8. НЕ упоминай детали, которые отсутствуют (пустые поля)

Формат ответа:
Message: [Твое WhatsApp сообщение здесь]
`;

// Function to make OpenRouter API request
async function makeOpenRouterRequest(
  prompt: string,
  model: string,
  maxTokens: number,
  temperature: number
): Promise<OpenRouterResponse> {
  const apiKey = process.env.NEXT_OPENROUTER_API_KEY;

  if (!apiKey) {
    throw new Error('NEXT_OPENROUTER_API_KEY environment variable is not set');
  }

  const response = await fetch(OPENROUTER_API.URL, {
    method: "POST",
    headers: {
      "Authorization": `Bearer ${apiKey}`,
      "HTTP-Referer": process.env.NEXTAUTH_URL || 'http://localhost:3000',
      "X-Title": 'SalesFlow WhatsApp Generator',
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: maxTokens,
      temperature: temperature
    })
  });

  if (!response.ok) {
    const errorData = await response.text();
    throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${errorData}`);
  }

  return response.json();
}





export class WhatsAppService {
  static async generatePersonalizedMessage(
    leadId: number,
    customPrompt?: string,
    agentId?: number
  ): Promise<{
    lead_id: number;
    message_id: number;
    content: string;
    phone_number: string;
    token_usage: any;
  }> {
    const sessionStartTime = Date.now();

    try {
      // Get lead data by internal ID
      const lead = await db
        .select()
        .from(leads)
        .where(eq(leads.id, leadId))
        .limit(1);

      if (!lead || !lead[0]) {
        throw new Error(`No lead found with ID: ${leadId}`);
      }

      const leadData = lead[0];

      // Create logging context
      const logContext: LogContext = {
        leadId,
        leadName: `${leadData.firstName || ''} ${leadData.lastName || ''}`.trim(),
        leadCompany: leadData.company || undefined,
        agentId,
        sessionId: `whatsapp_${leadId}_${Date.now()}`
      };

      // Start logging session
      WhatsAppLogger.logSessionStart(logContext, "MESSAGE_GENERATION");

      // Validate phone number
      if (!leadData.phone) {
        throw new Error("Lead does not have a phone number");
      }

      const phoneValidation = validatePhoneNumber(leadData.phone);

      // Log phone validation
      WhatsAppLogger.logPhoneValidation(logContext, leadData.phone || '', phoneValidation);

      if (!phoneValidation.isValid) {
        WhatsAppLogger.logGenerationError(logContext, new Error(phoneValidation.error || "Invalid phone number"), "phone_validation");
        throw new Error(phoneValidation.error || "Invalid phone number");
      }

      // Get agent if specified
      let agentPrompt = customPrompt;
      let agentData = null;

      if (agentId && !customPrompt) {
        const agent = await db
          .select()
          .from(agents)
          .where(eq(agents.id, agentId))
          .limit(1);

        const agentFound = agent && agent[0];
        agentData = agentFound ? agent[0] : null;

        // Log agent retrieval
        WhatsAppLogger.logAgentRetrieval(logContext, agentId, !!agentFound, agentData);

        if (agentFound) {
          agentPrompt = agent[0].prompt;
          // Update log context with agent name
          logContext.agentName = agent[0].name;
        }
      }

      // Prepare WhatsApp context for placeholder parsing
      const whatsappContext: WhatsAppContext = {
        lead: leadData,
        senderName: "SalesFlow Team", // TODO: Get from user settings
        senderCompany: "SalesFlow",
        senderPosition: "Sales Manager"
      };

      // Process prompt with placeholder parsing
      const basePrompt = agentPrompt || WHATSAPP_PROMPT;

      // First normalize the prompt variables to standard format
      const normalizedPrompt = normalizeWhatsAppPromptVariables(basePrompt);

      // Then parse placeholders with actual lead data
      const prompt = parseWhatsAppPlaceholders(normalizedPrompt, whatsappContext);

      // Detect placeholders for logging
      const placeholderRegex = /{{([^}]+)}}/g;
      const placeholdersFound = [...basePrompt.matchAll(placeholderRegex)].map(match => match[1]);

      // Log prompt processing
      WhatsAppLogger.logPromptProcessing(logContext, basePrompt, prompt, placeholdersFound);

      // Prepare LLM request parameters from agent settings or defaults
      const agentSettings = agentData?.settings as {
        model?: string;
        maxTokens?: number;
        temperature?: number;
        llmProvider?: string;
      } || {};

      // Map model names to OpenRouter format if needed
      let openRouterModel = agentSettings.model || OPENROUTER_API.MODELS.SONNET;

      // If the model doesn't start with a provider prefix, assume it's an Anthropic model
      if (!openRouterModel.includes('/')) {
        // Map common model names to OpenRouter format
        const modelMapping: Record<string, string> = {
          'claude-3-haiku-20240307': OPENROUTER_API.MODELS.HAIKU,
          'claude-3-sonnet-20240229': OPENROUTER_API.MODELS.SONNET,
          'claude-3.5-sonnet-20241022': OPENROUTER_API.MODELS.SONNET_35,
          'claude-3-opus-20240229': OPENROUTER_API.MODELS.OPUS,
        };
        openRouterModel = modelMapping[openRouterModel] || OPENROUTER_API.MODELS.SONNET;
      }

      const llmParams = {
        model: openRouterModel,
        maxTokens: agentSettings.maxTokens || 1000,
        temperature: agentSettings.temperature || 0.2,
        promptLength: prompt.length
      };

      // Log LLM request
      WhatsAppLogger.logLLMRequest(logContext, llmParams);

      const llmStartTime = Date.now();
      const openRouterResponse = await makeOpenRouterRequest(
        prompt,
        llmParams.model,
        llmParams.maxTokens,
        llmParams.temperature
      );
      const llmEndTime = Date.now();
      const llmExecutionTime = llmEndTime - llmStartTime;

      const generatedMessage = openRouterResponse.choices[0]?.message?.content || "Hello! I'd like to discuss SalesFlow.Team with you. Are you available for a quick chat? 📱";

      // Log LLM response
      const llmResponse = {
        content: generatedMessage,
        usage: openRouterResponse.usage ? {
          input_tokens: openRouterResponse.usage.prompt_tokens,
          output_tokens: openRouterResponse.usage.completion_tokens,
          total_tokens: openRouterResponse.usage.total_tokens
        } : undefined,
        executionTimeMs: llmExecutionTime
      };
      WhatsAppLogger.logLLMResponse(logContext, llmResponse);

      // Extract message content (remove "Message:" prefix if present)
      let processedContent = generatedMessage.replace(/^Message:\s*/i, "").trim();

      // Parse placeholders in the generated message content
      const originalPlaceholderCount = (processedContent.match(/{{[^}]+}}/g) || []).length;
      let messageContent = parseWhatsAppPlaceholders(processedContent, whatsappContext);
      const finalPlaceholderCount = (messageContent.match(/{{[^}]+}}/g) || []).length;
      const placeholdersReplaced = originalPlaceholderCount - finalPlaceholderCount;

      // Log message processing
      const messageProcessing = {
        rawResponse: generatedMessage,
        processedContent,
        finalMessage: messageContent,
        placeholdersReplaced
      };
      WhatsAppLogger.logMessageProcessing(logContext, messageProcessing);

      const leadInternalId = leadData.id;

      const [savedMessage] = await db
        .insert(whatsappMessages)
        .values({
          leadId: Number(leadInternalId),
          content: messageContent,
          prompt: agentPrompt || messageContent,
          phoneNumber: phoneValidation.formatted,
          sent: false,
        })
        .returning();

      // Log database operation
      WhatsAppLogger.logDatabaseOperation(logContext, "INSERT", "whatsappMessages", true, {
        messageId: savedMessage.id,
        contentLength: messageContent.length,
        phoneNumber: phoneValidation.formatted
      });

      const totalExecutionTime = Date.now() - sessionStartTime;

      // Log successful generation
      WhatsAppLogger.logGenerationSuccess(logContext, savedMessage.id, phoneValidation.formatted, totalExecutionTime);

      // Log session end
      WhatsAppLogger.logSessionEnd(logContext, "MESSAGE_GENERATION", true, totalExecutionTime);

      const tokenUsage = {
        input_tokens: openRouterResponse.usage?.prompt_tokens || 0,
        output_tokens: openRouterResponse.usage?.completion_tokens || 0,
        total_tokens: openRouterResponse.usage?.total_tokens || 0,
        input_cost: 0,
        output_cost: 0,
        total_cost: 0,
      };

      return {
        lead_id: leadId,
        message_id: savedMessage.id,
        content: messageContent,
        phone_number: phoneValidation.formatted,
        token_usage: tokenUsage,
      };
    } catch (error) {
      const totalExecutionTime = Date.now() - sessionStartTime;

      // Create minimal log context if not available
      const errorLogContext: LogContext = {
        leadId,
        agentId,
        sessionId: `whatsapp_${leadId}_${Date.now()}`
      };

      // Log generation error
      WhatsAppLogger.logGenerationError(
        errorLogContext,
        error instanceof Error ? error : new Error(String(error)),
        "message_generation"
      );

      // Log session end with failure
      WhatsAppLogger.logSessionEnd(errorLogContext, "MESSAGE_GENERATION", false, totalExecutionTime);

      console.error("Error generating personalized WhatsApp message:", error);
      throw error;
    }
  }

  // Backward compatibility method for Salesforce ID
  static async generatePersonalizedMessageBySalesforceId(
    salesforceId: string,
    customPrompt?: string
  ): Promise<{
    lead_id: string;
    message_id: number;
    content: string;
    phone_number: string;
    token_usage: any;
  }> {
    try {
      // Get lead data by Salesforce ID
      const lead = await db
        .select()
        .from(leads)
        .where(eq(leads.salesforceId, salesforceId))
        .limit(1);

      if (!lead || !lead[0]) {
        throw new Error(`No lead found with Salesforce ID: ${salesforceId}`);
      }

      // Use the main method with internal ID
      const result = await this.generatePersonalizedMessage(lead[0].id, customPrompt, undefined);

      // Convert lead_id back to salesforceId for backward compatibility
      return {
        ...result,
        lead_id: salesforceId,
      };
    } catch (error) {
      console.error("Error generating personalized WhatsApp message by Salesforce ID:", error);
      throw error;
    }
  }

  static async sendMessage(
    messageId: number,
    whatsappConfigId?: string
  ): Promise<[string, any]> {
    const sessionStartTime = Date.now();

    try {
      const messageWithLead = await db
        .select({
          message: whatsappMessages,
          lead: leads,
        })
        .from(whatsappMessages)
        .where(eq(whatsappMessages.id, messageId))
        .innerJoin(leads, eq(whatsappMessages.leadId, leads.id))
        .limit(1);

      if (!messageWithLead || !messageWithLead[0]) {
        throw new Error(`No WhatsApp message found with id ${messageId}`);
      }

      const { message: whatsappMessage, lead } = messageWithLead[0];

      // Create logging context
      const logContext: LogContext = {
        leadId: lead.id,
        leadName: `${lead.firstName || ''} ${lead.lastName || ''}`.trim(),
        leadCompany: lead.company || undefined,
        sessionId: `whatsapp_send_${messageId}_${Date.now()}`
      };

      // Start logging session
      WhatsAppLogger.logSessionStart(logContext, "MESSAGE_SENDING");

      // Get WhatsApp configuration
      let whatsappConfig = null;
      if (whatsappConfigId) {
        const configs = await db
          .select()
          .from(whatsappConfigs)
          .where(eq(whatsappConfigs.id, whatsappConfigId))
          .limit(1);
        
        if (configs.length > 0) {
          whatsappConfig = configs[0];
        }
      }

      // Use environment variables as fallback
      const apiUrl = whatsappConfig?.apiUrl || process.env.WHATSAPP_API_URL;
      const profileId = whatsappConfig?.profileId || process.env.WHATSAPP_PROFILE_ID;
      const token = whatsappConfig?.token || process.env.WHATSAPP_TOKEN;

      if (!apiUrl || !profileId || !token) {
        WhatsAppLogger.logSendingError(logContext, messageId, whatsappMessage.phoneNumber, new Error("WhatsApp API configuration is missing"));
        throw new Error("WhatsApp API configuration is missing");
      }

      // Log sending attempt
      WhatsAppLogger.logSendingAttempt(logContext, messageId, whatsappMessage.phoneNumber, whatsappConfigId);

      // Send WhatsApp message via external API
      const response = await fetch(`${apiUrl}/api/sync/message/send?profile_id=${profileId}`, {
        method: "POST",
        headers: {
          "Authorization": token,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          body: whatsappMessage.content,
          recipient: whatsappMessage.phoneNumber,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`WhatsApp API error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();

      // Log successful sending
      WhatsAppLogger.logSendingSuccess(logContext, messageId, whatsappMessage.phoneNumber, result);

      // Update message status in database
      await db
        .update(whatsappMessages)
        .set({
          sent: true,
          sentAt: new Date(),
        })
        .where(eq(whatsappMessages.id, messageId));

      // Log database operation
      WhatsAppLogger.logDatabaseOperation(logContext, "UPDATE", "whatsappMessages", true, {
        messageId,
        status: "sent"
      });

      // Log to history
      await db
        .insert(whatsappHistory)
        .values({
          messageId: messageId,
          status: "sent",
          sentAt: new Date(),
        });

      // Log database operation
      WhatsAppLogger.logDatabaseOperation(logContext, "INSERT", "whatsappHistory", true, {
        messageId,
        status: "sent"
      });

      const totalExecutionTime = Date.now() - sessionStartTime;

      // Log session end
      WhatsAppLogger.logSessionEnd(logContext, "MESSAGE_SENDING", true, totalExecutionTime);

      return [
        `WhatsApp message was successfully sent to ${whatsappMessage.phoneNumber}`,
        null,
      ];
    } catch (error) {
      const totalExecutionTime = Date.now() - sessionStartTime;

      // Create minimal log context if not available
      const errorLogContext: LogContext = {
        leadId: 0, // Will be updated if we have message data
        sessionId: `whatsapp_send_error_${messageId}_${Date.now()}`
      };

      // Try to get phone number for logging
      let phoneNumber = "unknown";
      try {
        const messageData = await db
          .select({ phoneNumber: whatsappMessages.phoneNumber })
          .from(whatsappMessages)
          .where(eq(whatsappMessages.id, messageId))
          .limit(1);
        if (messageData[0]) {
          phoneNumber = messageData[0].phoneNumber;
        }
      } catch (_dbError) {
        // Ignore database errors when trying to get phone number for logging
      }

      // Log sending error
      WhatsAppLogger.logSendingError(
        errorLogContext,
        messageId,
        phoneNumber,
        error instanceof Error ? error : new Error(String(error))
      );

      // Log session end with failure
      WhatsAppLogger.logSessionEnd(errorLogContext, "MESSAGE_SENDING", false, totalExecutionTime);

      console.error("Error sending WhatsApp message:", error);

      // Log error to history
      try {
        await db
          .insert(whatsappHistory)
          .values({
            messageId: messageId,
            status: "failed",
            error: error instanceof Error ? error.message : "Unknown error",
          });

        WhatsAppLogger.logDatabaseOperation(errorLogContext, "INSERT", "whatsappHistory", true, {
          messageId,
          status: "failed",
          error: error instanceof Error ? error.message : "Unknown error"
        });
      } catch (dbError) {
        WhatsAppLogger.logDatabaseOperation(errorLogContext, "INSERT", "whatsappHistory", false, {
          messageId,
          error: dbError instanceof Error ? dbError.message : "Unknown database error"
        });
      }

      throw error;
    }
  }
}

import { withAuth } from "next-auth/middleware";

export default withAuth({
    pages: {
        signIn: "/login",
    },
    callbacks: {
        authorized({ token }) {
            return !!token;
        },
    },
});

export const config = {
    matcher: [
        // Защищенные API-маршруты (все, кроме публичных)
        "/api/auth/:path*",
        "/api/emails/:path*",
        "/api/leads/:path*",
        "/api/prompts/:path*",
        "/api/salesforce/:path*",
        "/api/settings/:path*",
        "/api/test/:path*",
        "/api/trigger/:path*",

        // Защищенные API v1 маршруты (все, кроме публичных)
        "/api/v1/settings/:path*",
        "/api/v1/agents/:path*",
        "/api/v1/generate/:path*",

        // Защищаем только определенные маршруты в api/v1/prompts
        "/api/v1/prompts",
        "/api/v1/prompts/create",
        "/api/v1/prompts/update",
        "/api/v1/prompts/delete",
        "/api/v1/prompts/test", // Основной тест, но не test-direct и не test-callback

        "/api/v1/salesforce/:path*",

        // Защищенные страницы
        "/",
        "/agents/:path*",
        "/prompts/:path*",
        "/settings/:path*",
        "/leads/:path*",
        "/test-functions/:path*",

        // Исключаем страницу документации API из защищенных маршрутов
        // "/docs/api",
    ]
};

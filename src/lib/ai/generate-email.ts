import { LeadData, LeadHistoryItem, ManagerInfo, ProductInfo } from "../schemas/agent-api-schemas";
import { createChatCompletion } from "../anthropic";
import { normalizePromptVariables } from "@/utils/prompt-helpers";

// const SYSTEM_PROMPT = `You are an AI co-pilot for SalesFlow.Team, a cutting-edge service that automates sales emails and follow-ups using AI. Your primary function is to craft highly personalized, effective cold emails for high-value prospects for any cmr users.
// Your goal is to generate emails that stand out in crowded inboxes, demonstrate deep understanding of the prospect's needs, and compel them to take action.

// Always start your response with "Subject: " followed by a compelling subject line.
// After the subject line, add a blank line and then write the email body.`;

const SYSTEM_PROMPT = 'You are an AI assistant, which helps to user awesome and perfect things which user want. Make it step by step, with all details and explanations.'
type EmailGenerationParams = {
    agentPrompt: string;
    leadData: LeadData;
    leadHistory: LeadHistoryItem[];
    managerInfo: ManagerInfo;
    productInfo: ProductInfo;
    emailType: "initial_outreach" | "follow_up" | "meeting_request" | "custom";
    customInstructions?: string;
    tone: "formal" | "friendly" | "persuasive" | "professional";
    language: "ru" | "en";
    settings: {
        model: string;
        maxTokens: number;
        temperature: number;
        llmProvider: string;
    };
    userId?: string;
};

/**
 * Заменяет переменные в шаблоне промпта агента
 */
function replaceTemplateVariables(template: string, params: EmailGenerationParams): string {
    const {
        leadData,
        leadHistory,
        managerInfo,
        productInfo,
        emailType,
        customInstructions,
        tone,
        language,
    } = params;

    // Форматирование истории взаимодействий для промпта
    const formattedHistory = leadHistory.map((item) => {
        return `Дата: ${new Date(item.date).toLocaleDateString()}
Тип: ${item.type}
Описание: ${item.description}
Результат: ${item.result}
Содержание: ${item.content}
`;
    }).join("\n---\n");

    // Форматирование преимуществ продукта
    const formattedBenefits = productInfo.benefits 
        ? productInfo.benefits.map(benefit => `- ${benefit}`).join("\n")
        : "";

    // Форматирование примеров использования
    const formattedUseCases = productInfo.useCases
        ? productInfo.useCases.map(useCase => `- ${useCase}`).join("\n")
        : "";
    
    // Базовый набор переменных для замены
    const variables: Record<string, string> = {
        // Данные о лиде
        "{{lead.firstName}}": leadData.firstName,
        "{{lead.lastName}}": leadData.lastName,
        "{{lead.fullName}}": `${leadData.firstName} ${leadData.lastName}`,
        "{{lead.company}}": leadData.company,
        "{{lead.companyDesc}}": leadData.companyDesc || "",
        "{{lead.position}}": leadData.position || "",
        "{{lead.email}}": leadData.email,
        "{{lead.industry}}": leadData.industry || "",
        "{{lead.companySize}}": leadData.companySize || "",
        
        // Данные о менеджере
        "{{manager.name}}": managerInfo.name,
        "{{manager.position}}": managerInfo.position,
        "{{manager.company}}": managerInfo.company,
        "{{manager.email}}": managerInfo.email,
        "{{manager.phone}}": managerInfo.phone || "",
        
        // Данные о продукте
        "{{product.name}}": productInfo.name,
        "{{product.description}}": productInfo.description,
        "{{product.benefits}}": formattedBenefits,
        "{{product.useCases}}": formattedUseCases,
        
        // Прочие параметры
        "{{emailType}}": emailType,
        "{{tone}}": tone,
        "{{language}}": language === 'ru' ? 'русский' : 'английский',
        "{{customInstructions}}": customInstructions || "",
        "{{leadHistory}}": formattedHistory,
    };
    
    // Заменяем все переменные в шаблоне
    let result = template;
    for (const [key, value] of Object.entries(variables)) {
        result = result.replace(new RegExp(key, 'g'), value);
    }
    
    return result;
}

// Создаем новый интерфейс для возвращаемого значения
interface EmailContent {
  subject: string;
  body: string;
  systemPrompt: string;
}

// Изменяем сигнатуру функции, чтобы она возвращала объект EmailContent
export async function createEmailWithAI(params: EmailGenerationParams): Promise<EmailContent> {
    try {
        const {
            agentPrompt,
            language,
            emailType,
        } = params;

        let processedPrompt = agentPrompt;
        
        // Подготовка детальной информации о лиде
        const leadDetails = [];
        if (params.leadData.position) leadDetails.push(`Должность: ${params.leadData.position}`);
        if (params.leadData.industry) leadDetails.push(`Отрасль: ${params.leadData.industry}`);
        if (params.leadData.companySize) leadDetails.push(`Размер компании: ${params.leadData.companySize}`);

        // Замена переменных в промпте
        processedPrompt = processedPrompt
            .replace(/{{lead\.firstName}}/g, params.leadData.firstName)
            .replace(/{{lead\.lastName}}/g, params.leadData.lastName)
            .replace(/{{lead\.fullName}}/g, `${params.leadData.firstName} ${params.leadData.lastName}`)
            .replace(/{{lead\.company}}/g, params.leadData.company)
            .replace(/{{lead\.email}}/g, params.leadData.email)
            // Другие базовые переменные...
            
            // Обработка составных переменных
            .replace(/{{lead\.details}}/g, leadDetails.length > 0 ? leadDetails.join('\n') : 'Информация отсутствует')
            .replace(/{{lead\.companyDesc}}/g, params.leadData.companyDesc || 'Информация о компании отсутствует')
            .replace(/{{leadHistory}}/g, formatLeadHistory(params.leadHistory));

        // Вспомогательная функция для форматирования истории лида
        function formatLeadHistory(history: {
            from?: string;
            to?: string;
            subject?: string; 
            type: "email" | "call" | "meeting" | "other"; date: string; description: string; result: string; content: string; 
}[] | undefined | null) {
            if (!history || history.length === 0) {
                return 'История взаимодействий отсутствует';
            }
            
            return history.map(item => {
                let historyEntry = `${new Date(item.date).toLocaleDateString()} ${new Date(item.date).toLocaleTimeString()}: ${item.type} - ${item.description}. ${item.result}`;
                
                // Добавляем информацию о from, to, subject для email-сообщений
                if (item.type === "email" && item.from && item.to) {
                    historyEntry += `\nОт: ${item.from}\nКому: ${item.to}`;
                    if (item.subject) {
                        historyEntry += `\nТема: ${item.subject}`;
                    }
                    if (item.content) {
                        historyEntry += `\nСодержание: ${item.content}`;
                    }
                }
                
                return historyEntry;
            }).join('\n\n');
        }
        
        

        // Сначала нормализуем переменные в промпте агента
        const normalizedPrompt = normalizePromptVariables(processedPrompt);
        
        // Заменяем переменные в промпте агента
        let systemPrompt = replaceTemplateVariables(normalizedPrompt, params);
        // console.log('systemPrompt', systemPrompt)
        
        // Добавляем базовую инструкцию генерировать email, если в промпте не указана явно задача
        if (!systemPrompt.includes("email") && !systemPrompt.includes("письм")) {
            systemPrompt = `${systemPrompt}\n\nТы - опытный копирайтер, специализирующийся на создании эффективных email-писем. Твоя задача - составить профессиональное письмо на основе информации выше.`;
        }
        
        // console.log('systemPrompt', systemPrompt)

        // Добавляем инструкции по формату, если они отсутствуют
        // if (!systemPrompt.includes("тем") && !systemPrompt.includes("subject")) {
        systemPrompt += `\n\nПисьмо должно включать тему (subject line), основной текст и подпись.`;
        // }
        
        // Добавляем заключительные инструкции по возвращаемому формату
        if (!systemPrompt.includes("предоставь только текст") && 
            !systemPrompt.includes("без комментариев") && 
            !systemPrompt.includes("without comments")) {
            systemPrompt += `\n\nПредоставь только текст готового письма без дополнительных комментариев, пояснений или разметки.`;
        }

        // Запрос к API для генерации текста
        const userPrompt = emailType === 'initial_outreach' 
            ? 'Создай первое вступительное письмо для этого клиента.' 
            : emailType === 'follow_up' 
            ? 'Создай письмо для продолжения общения с этим клиентом.' 
            : emailType === 'meeting_request' 
            ? 'Создай письмо с запросом на встречу с этим клиентом.' 
            : 'Создай персонализированное письмо для этого клиента.';
        
        // Добавляем информацию о языке в пользовательский запрос
        const localizedUserPrompt = language === 'ru' 
            ? `${userPrompt} Письмо должно быть на русском языке.` 
            : `${userPrompt} The email should be in English.`;

        // console.log('localizedUserPrompt', localizedUserPrompt)

        const result = await createChatCompletion({
            system: SYSTEM_PROMPT,
            messages: [
                {
                    role: "user",
                    content: systemPrompt + '\n\n' + localizedUserPrompt
                }
            ],
            max_tokens: params.settings.maxTokens,
            model: params.settings.model,
            temperature: params.settings.temperature,
        });

        // console.log('result', result)

        // Предположим, что сейчас функция возвращает строку с полным текстом email
        // Нужно изменить логику, чтобы разделить результат на тему и тело
        
        // Пример реализации:
        const generatedText = result || "Не удалось сгенерировать письмо";

        // console.log('generatedText', generatedText)
        
        // Разделение на тему и тело
        // Предполагаем, что первая строка - это тема письма
        const lines = generatedText.trim().split('\n');
        const subject = lines[0].replace(/^(Subject:|Тема:)\s*/i, '').trim();
        const body = lines.slice(1).join('\n').trim();
        
        return {
            subject,
            body,
            systemPrompt,
        };
    } catch (error) {
        console.error("Ошибка при генерации email с помощью ИИ:", error);
        throw new Error("Не удалось сгенерировать email с помощью ИИ");
    }
} 
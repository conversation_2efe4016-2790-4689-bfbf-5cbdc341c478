// Используем OpenRouter для доступа к различным языковым моделям
// Получаем API-ключ из переменных окружения
const apiKey = process.env.NEXT_OPENROUTER_API_KEY;
const baseUrl = process.env.NEXT_API_BASE_URL || 'https://openrouter.ai/api/v1';

if (!apiKey) {
  console.warn('NEXT_OPENROUTER_API_KEY не установлен. Генерация сообщений не будет работать.');
}

// Тип для настроек запроса к API
interface ChatCompletionOptions {
  system?: string;
  messages: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
  model?: string;
  max_tokens?: number;
  temperature?: number;
}

// Тип для сообщений OpenRouter API
type ApiMessage = {
  role: 'user' | 'assistant' | 'system';
  content: string;
};

/**
 * Создает завершение чата с помощью OpenRouter API
 * @param options Настройки запроса
 * @returns Сгенерированный текст
 */
export async function createChatCompletion(options: ChatCompletionOptions): Promise<string> {
  // В тестовом режиме возвращаем тестовые данные, не делая реальных API запросов
  if (process.env.DEBUG_PROMPT_TEST === 'true' && process.env.NODE_ENV === 'development') {
    console.log('[ANTHROPIC] Using DEBUG mode for generating email');
    
    // Получаем пример запроса для логов
    const userMessage = options.messages.find(m => m.role === 'user')?.content || '';
    console.log(`[ANTHROPIC] DEBUG prompt (truncated): ${userMessage.substring(0, 100)}...`);
    
    // Возвращаем тестовый ответ
    return `Subject: [TEST] Personalized Follow-up Regarding Your Marketing Strategy

Hi there,

I'm reaching out because I noticed your company has been making significant strides in the market recently. I thought you might be interested in our new solution that could potentially help optimize your customer acquisition strategy.

Our platform has helped similar companies in your industry increase conversion rates by an average of 27% while reducing customer acquisition costs.

Would you be open to a quick 15-minute call next week to discuss how we might be able to help?

Best regards,
Test Sender`;
  }

  if (!apiKey) {
    console.error('Не удалось выполнить запрос: отсутствует NEXT_OPENROUTER_API_KEY');
    return 'Ошибка: API-ключ не настроен. Пожалуйста, настройте NEXT_OPENROUTER_API_KEY.';
  }

  try {
    const { system, messages, model = 'anthropic/claude-3.5-haiku-20241022', max_tokens = 4000, temperature = 0.7 } = options;

    // Подготовка сообщений для API
    let apiMessages: ApiMessage[] = messages as unknown as ApiMessage[];
    
    // Если присутствует system prompt, добавляем его как сообщение от системы
    if (system) {
      apiMessages = [{ role: 'system', content: system } as ApiMessage, ...apiMessages];
    }

    // Создаем запрос к API
    const response = await fetch(`${baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': process.env.NEXTAUTH_URL || 'http://localhost:3000',
        'X-Title': 'SalesFlow Email Generator',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        messages: apiMessages,
        max_tokens,
        temperature,
      })
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Ошибка API: ${response.status} ${response.statusText} - ${errorData}`);
    }

    const data = await response.json();

    console.log('data', JSON.stringify(data, null, 2))
    
    // Получаем текст из ответа
    if (data.choices && data.choices.length > 0 && data.choices[0].message && data.choices[0].message.content) {
      return data.choices[0].message.content;
    }
    
    // Проверяем наличие ошибки в ответе
    if (data.error) {
      console.error('Ошибка в ответе API:', data.error);
      const errorMessage = data.error.message || 'Неизвестная ошибка';
      const errorCode = data.error.code || 'unknown';
      return `Ошибка API (${errorCode}): ${errorMessage}`;
    }
    
    return "Ошибка: ответ не содержит текста или имеет неподдерживаемый формат";
  } catch (error) {
    console.error('Ошибка при запросе к API:', error);
    throw new Error(`Не удалось получить ответ от API: ${error instanceof Error ? error.message : 'Неизвестная ошибка'}`);
  }
} 
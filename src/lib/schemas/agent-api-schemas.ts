import { z } from "zod";

// Базовая информация о лиде
export const leadDataSchema = z.object({
    firstName: z.string().min(1, "Имя обязательно"),
    lastName: z.string().min(1, "Фамилия обязательна"),
    company: z.string().min(1, "Название компании обязательно"),
    companyDesc: z.string().optional(),
    position: z.string().optional(),
    email: z.string().email("Некорректный формат email"),
    industry: z.string().optional(),
    companySize: z.string().optional(),
});

// История взаимодействий с лидом
export const leadHistoryItemSchema = z.object({
    date: z.string().datetime({ offset: true }),
    type: z.enum(["email", "call", "meeting", "other"]),
    from: z.string().optional(),
    to: z.string().optional(),
    subject: z.string().optional(),
    description: z.string(),
    result: z.string(),
    content: z.string(),
});

// Информация о менеджере
export const managerInfoSchema = z.object({
    name: z.string().min(1, "Имя менеджера обязательно"),
    position: z.string().min(1, "Должность менеджера обязательна"),
    company: z.string().min(1, "Компания менеджера обязательна"),
    email: z.string().email("Некорректный формат email"),
    phone: z.string().optional(),
});

// Информация о продукте
export const productInfoSchema = z.object({
    name: z.string().min(1, "Название продукта обязательно"),
    description: z.string().min(1, "Описание продукта обязательно"),
    benefits: z.array(z.string()).optional(),
    useCases: z.array(z.string()).optional(),
});

// Общая схема запроса
export const agentRequestSchema = z.object({
    agentId: z.string().uuid("Некорректный формат UUID агента"),
    leadData: leadDataSchema,
    leadHistory: z.array(leadHistoryItemSchema).optional(),
    managerInfo: managerInfoSchema,
    productInfo: productInfoSchema,
    emailType: z.enum(["initial_outreach", "follow_up", "meeting_request", "custom"]),
    customInstructions: z.string().optional(),
    tone: z.enum(["formal", "friendly", "persuasive", "professional"]).default("professional"),
    language: z.enum(["ru", "en"]).default("ru"),
    promptId: z.number().optional(),
    settings: z.object({
        model: z.string().default('anthropic/claude-3.5-haiku-20241022'),
        maxTokens: z.number().default(4000),
        temperature: z.number().default(0.7),
        llmProvider: z.string().default('anthropic')
    }).optional()
});

export type LeadData = z.infer<typeof leadDataSchema>;
export type LeadHistoryItem = z.infer<typeof leadHistoryItemSchema>;
export type ManagerInfo = z.infer<typeof managerInfoSchema>;
export type ProductInfo = z.infer<typeof productInfoSchema>;
export type AgentRequest = z.infer<typeof agentRequestSchema>; 
import { z } from "zod";

// Schema for communications object
export const communicationsSchema = z.object({
  linkedin: z.string().url("Invalid LinkedIn URL").optional(),
  facebook: z.string().url("Invalid Facebook URL").optional(),
  telegram: z.string().optional(),
  vkontakte: z.string().url("Invalid VK URL").optional(),
}).optional();

// Schema for LinkedIn info object
export const linkedinInfoSchema = z.object({
  url: z.string().url("Invalid LinkedIn URL"),
  content: z.string().optional(),
}).optional();

// Schema for details object
export const detailsSchema = z.object({
  linkedin_info: linkedinInfoSchema,
}).optional();

// Schema for lead enrichment request
export const leadEnrichmentRequestSchema = z.object({
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  email: z.string().email("Invalid email format").optional(),
  company: z.string().optional(),
  communications: communicationsSchema,
  description: z.string().optional(),
  details: detailsSchema,
  webhook_url: z.string().url("Invalid webhook URL").optional(),
  lang: z.enum(["ru", "en"]).default("ru").optional(),
});

// Schema for creating an operation
export const createOperationSchema = z.object({
  type: z.enum(["LEAD_ENRICHMENT", "EMAIL_GENERATION", "DATA_IMPORT", "DATA_EXPORT"]),
  details: z.string().optional(),
});

// Types
export type LeadEnrichmentRequest = z.infer<typeof leadEnrichmentRequestSchema>;
export type CreateOperationRequest = z.infer<typeof createOperationSchema>;

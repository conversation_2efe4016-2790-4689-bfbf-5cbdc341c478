import { db } from "@/db";
import { users } from "@/db/schema";
import { compare } from "bcryptjs";
import { eq } from "drizzle-orm";
import { AuthOptions } from "next-auth";
import Credentials from "next-auth/providers/credentials";
import Salesforce from "next-auth/providers/salesforce";
import { getServerSession } from "next-auth";

export const authOptions: AuthOptions = {
    providers: [
        Salesforce({
            clientId: process.env.NEXT_SALESFORCE_CLIENT_ID!,
            clientSecret: process.env.NEXT_SALESFORCE_CLIENT_SECRET!,
            authorization: { params: { scope: "api refresh_token web" } },
        }),
        Credentials({
            credentials: {
                email: {},
                password: {},
            },
            async authorize(_credentials, _req) {
                console.log("NextAuth authorize: Starting authorization", {
                    hasCredentials: !!_credentials,
                    email: _credentials?.email,
                    hasPassword: !!_credentials?.password
                });

                try {
                    const response = await db
                        .select()
                        .from(users)
                        .where(eq(users.email, _credentials?.email || ""))
                        .limit(1);

                    console.log("NextAuth authorize: Database query result", {
                        userFound: !!response[0],
                        email: _credentials?.email
                    });

                    const user = response[0];

                    if (!user) {
                        console.log("NextAuth authorize: User not found");
                        return null;
                    }

                    const passwordCorrect = await compare(
                        _credentials?.password || "",
                        user.password
                    );

                    console.log("NextAuth authorize: Password check", {
                        passwordCorrect,
                        userId: user.id
                    });

                    if (!passwordCorrect) {
                        console.log("NextAuth authorize: Password incorrect");
                        return null;
                    }

                    const result = {
                        id: user.id.toString(),
                        email: user.email,
                        name: user.name,
                    };

                    console.log("NextAuth authorize: Success", result);
                    return result;
                } catch (error) {
                    console.error("NextAuth authorize: Error", error);
                    return null;
                }
            },
        }),

    ],
    pages: {
        signIn: "/login",
    },
    session: {
        strategy: "jwt",
        maxAge: 24 * 60 * 60, // 24 hours
    },
    cookies: {
        sessionToken: {
            name: process.env.NODE_ENV === 'production'
                ? '__Secure-next-auth.session-token'
                : 'next-auth.session-token',
            options: {
                httpOnly: true,
                sameSite: 'lax',
                path: '/',
                secure: process.env.NODE_ENV === 'production',
                // Не устанавливаем domain для production, чтобы cookie работал только для текущего домена
            },
        },
    },
    debug: process.env.NODE_ENV === 'development',
    callbacks: {
        async session({ session, token }) {
            console.log("NextAuth session callback:", {
                hasSession: !!session,
                hasUser: !!session?.user,
                hasToken: !!token,
                tokenSub: token?.sub,
                tokenEmail: token?.email,
                sessionUserEmail: session?.user?.email
            });

            if (session?.user && token?.sub) {
                session.user.id = token.sub;
            }

            // Ensure email is passed from token to session
            if (session?.user && token?.email) {
                session.user.email = token.email;
            }

            if (token) {
                session.accessToken = token.accessToken as string;
                session.refreshToken = token.refreshToken as string;
                session.instanceUrl = token.instanceUrl as string;
            }

            console.log("NextAuth session callback result:", {
                hasSession: !!session,
                hasUser: !!session?.user,
                userEmail: session?.user?.email,
                userId: session?.user?.id
            });

            return session;
        },
        async jwt({ token, account, user }) {
            console.log("NextAuth JWT callback:", {
                hasToken: !!token,
                hasAccount: !!account,
                hasUser: !!user,
                tokenEmail: token?.email,
                userEmail: user?.email,
                accountProvider: account?.provider
            });

            // On first sign in, user object is available
            if (user) {
                token.id = user.id;
                token.email = user.email;
                token.name = user.name;
            }

            if (account) {
                token.accessToken = account.access_token as string;
                token.refreshToken = account.refresh_token as string;
                token.instanceUrl = account.instance_url as string;
            }

            console.log("NextAuth JWT callback result:", {
                tokenId: token?.id,
                tokenEmail: token?.email,
                tokenName: token?.name
            });

            return token;
        },
        async signIn({ account, user }) {
            console.log("NextAuth signIn callback:", {
                hasAccount: !!account,
                hasUser: !!user,
                accountProvider: account?.provider,
                userEmail: user?.email
            });

            if (account?.provider === "salesforce") {
                return true;
            }
            return true;
        },
    },
};

export const auth = () => getServerSession(authOptions);
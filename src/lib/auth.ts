import { db } from "@/db";
import { users } from "@/db/schema";
import { compare } from "bcryptjs";
import { eq } from "drizzle-orm";
import { AuthOptions } from "next-auth";
import Credentials from "next-auth/providers/credentials";
import Salesforce from "next-auth/providers/salesforce";
import { getServerSession } from "next-auth";

export const authOptions: AuthOptions = {
    providers: [
        Salesforce({
            clientId: process.env.NEXT_SALESFORCE_CLIENT_ID!,
            clientSecret: process.env.NEXT_SALESFORCE_CLIENT_SECRET!,
            authorization: { params: { scope: "api refresh_token web" } },
        }),
        Credentials({
            credentials: {
                email: {},
                password: {},
            },
            async authorize(_credentials, _req) {
                const response = await db
                    .select()
                    .from(users)
                    .where(eq(users.email, _credentials?.email || ""))
                    .limit(1);
                const user = response[0];
                const passwordCorrect = await compare(
                    _credentials?.password || "",
                    user.password
                );

                if (!passwordCorrect) {
                    return null;
                }

                return {
                    id: user.id.toString(),
                    email: user.email,
                    name: user.name,
                };
            },
        }),

    ],
    pages: {
        signIn: "/login",
    },
    session: {
        strategy: "jwt",
    },
    callbacks: {
        async session({ session, token }) {
            if (session?.user && token?.sub) {
                session.user.id = token.sub;
            }
            if (token) {
                session.accessToken = token.accessToken as string;
                session.refreshToken = token.refreshToken as string;
                session.instanceUrl = token.instanceUrl as string;
            }
            return session;
        },
        async jwt({ token, account }) {
            if (account) {
                token.accessToken = account.access_token as string;
                token.refreshToken = account.refresh_token as string;
                token.instanceUrl = account.instance_url as string;
            }
            return token;
        },
        async signIn({ account }) {
            if (account?.provider === "salesforce") {
                return true;
            }
            return true;
        },
    },
};

export const auth = () => getServerSession(authOptions);
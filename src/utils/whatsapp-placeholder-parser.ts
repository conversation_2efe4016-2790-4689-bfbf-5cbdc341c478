import { Lead } from "@/db/schema/leads";

/**
 * Interface for WhatsApp message generation context
 */
export interface WhatsAppContext {
  lead: Lead;
  senderName?: string;
  senderCompany?: string;
  senderPosition?: string;
}

/**
 * Extracts value from nested JSON object using dot notation
 * Example: getNestedValue(obj, "details.city") returns obj.details?.city
 */
function getNestedValue(obj: any, path: string): string {
  if (!obj || !path) return "";
  
  const keys = path.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return "";
    }
  }
  
  return current ? String(current) : "";
}

/**
 * Parses and replaces placeholders in WhatsApp message template
 */
export function parseWhatsAppPlaceholders(template: string, context: WhatsAppContext): string {
  const { lead, senderName = "SalesFlow Team", senderCompany = "SalesFlow", senderPosition = "Sales Manager" } = context;

  // Define placeholder mappings
  const placeholders: Record<string, string> = {
    // Basic lead information
    "{{leadFirstName}}": lead.firstName || "",
    "{{leadLastName}}": lead.lastName || "",
    "{{leadFullName}}": `${lead.firstName || ""} ${lead.lastName || ""}`.trim(),
    "{{leadCompany}}": lead.company || "",
    "{{leadTitle}}": lead.title || "",
    "{{leadEmail}}": lead.email || "",
    "{{leadPhone}}": lead.phone || "",
    "{{leadIndustry}}": lead.industry || "",
    "{{leadWebsite}}": lead.website || "",
    "{{leadStatus}}": lead.status || "",
    "{{leadDescription}}": lead.description || "",
    
    // Sender information
    "{{senderName}}": senderName,
    "{{senderCompany}}": senderCompany,
    "{{senderPosition}}": senderPosition,
    
    // JSON field extractions - details
    "{{details_city}}": getNestedValue(lead.details, "city"),
    "{{details_country}}": getNestedValue(lead.details, "country"),
    "{{details_region}}": getNestedValue(lead.details, "region"),
    "{{details_address}}": getNestedValue(lead.details, "address"),
    "{{details_linkedin}}": getNestedValue(lead.details, "linkedin"),
    "{{details_facebook}}": getNestedValue(lead.details, "facebook"),
    "{{details_twitter}}": getNestedValue(lead.details, "twitter"),
    
    // JSON field extractions - communications
    "{{communications_linkedin}}": getNestedValue(lead.communications, "linkedin"),
    "{{communications_email}}": getNestedValue(lead.communications, "email"),
    "{{communications_phone}}": getNestedValue(lead.communications, "phone"),
    "{{communications_website}}": getNestedValue(lead.communications, "website"),
    "{{communications_vkontakte}}": getNestedValue(lead.communications, "vkontakte"),
    "{{communications_telegram}}": getNestedValue(lead.communications, "telegram"),
    "{{communications_whatsapp}}": getNestedValue(lead.communications, "whatsapp"),
    
    // JSON field extractions - company_info
    "{{company_info_name}}": getNestedValue(lead.company_info, "name"),
    "{{company_info_description}}": getNestedValue(lead.company_info, "description"),
    "{{company_info_industry}}": getNestedValue(lead.company_info, "industry"),
    "{{company_info_size}}": getNestedValue(lead.company_info, "size"),
    "{{company_info_website}}": getNestedValue(lead.company_info, "website"),
    "{{company_info_location}}": getNestedValue(lead.company_info, "location"),
    
    // Legacy placeholders for backward compatibility
    "{{lead_details}}": JSON.stringify(lead.details || {}, null, 2),
    "{{lead_history}}": JSON.stringify(lead.history || {}, null, 2),
    "{{company_info}}": JSON.stringify(lead.company_info || {}, null, 2),
  };
  
  // Replace all placeholders in the template
  let result = template;

  for (const [placeholder, value] of Object.entries(placeholders)) {
    // Use global regex to replace all occurrences
    const regex = new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g');
    result = result.replace(regex, value);
  }

  // Clean up sentences with empty placeholders
  result = cleanEmptyPlaceholderSentences(result);

  return result;
}

/**
 * Removes sentences or phrases that contain only empty placeholder values
 */
function cleanEmptyPlaceholderSentences(text: string): string {
  // Patterns to clean up when placeholders are empty
  const cleanupPatterns = [
    // "и находитесь в городе ." -> remove entire phrase
    /и находитесь в городе\s*\.\s*/g,
    /и находитесь в\s*\.\s*/g,
    /в городе\s*\.\s*/g,

    // "Мы нашли ваш профиль в  и" -> "Мы нашли ваш профиль и"
    /нашли ваш профиль в\s+и/g,
    /профиль в\s+и/g,

    // "LinkedIn:  " -> remove if empty
    /LinkedIn:\s*\.\s*/g,
    /LinkedIn:\s+и/g,

    // Multiple spaces and dots
    /\s{2,}/g, // Multiple spaces -> single space
    /\.\s*\.\s*/g, // Multiple dots -> single dot
    /\s+\./g, // Space before dot -> just dot
    /\.\s*,/g, // Dot comma -> just comma
    /,\s*\./g, // Comma dot -> just dot
  ];

  let cleaned = text;

  // Apply cleanup patterns
  cleanupPatterns.forEach((pattern, index) => {
    if (index === cleanupPatterns.length - 6) { // Multiple spaces pattern
      cleaned = cleaned.replace(pattern, ' ');
    } else if (index === cleanupPatterns.length - 5) { // Multiple dots pattern
      cleaned = cleaned.replace(pattern, '.');
    } else if (index === cleanupPatterns.length - 4) { // Space before dot
      cleaned = cleaned.replace(pattern, '.');
    } else if (index === cleanupPatterns.length - 3) { // Dot comma
      cleaned = cleaned.replace(pattern, ',');
    } else if (index === cleanupPatterns.length - 2) { // Comma dot
      cleaned = cleaned.replace(pattern, '.');
    } else if (index < 3) { // Remove entire phrases
      cleaned = cleaned.replace(pattern, '');
    } else { // Replace with "и"
      cleaned = cleaned.replace(pattern, 'и');
    }
  });

  // Final cleanup: remove extra whitespace and normalize punctuation
  cleaned = cleaned
    .replace(/\s+/g, ' ') // Normalize spaces
    .replace(/\s+\./g, '.') // Remove space before dots
    .replace(/\s+,/g, ',') // Remove space before commas
    .replace(/\.\s*\n\s*\n/g, '.\n\n') // Normalize paragraph breaks
    .trim();

  return cleaned;
}

/**
 * Normalizes WhatsApp prompt variables to standard format
 * Similar to normalizePromptVariables but for WhatsApp specific placeholders
 */
export function normalizeWhatsAppPromptVariables(prompt: string): string {
  const variableMappings = [
    // Lead name variations
    { pattern: /{{lead\.firstName}}/g, replacement: "{{leadFirstName}}" },
    { pattern: /{{lead\.lastName}}/g, replacement: "{{leadLastName}}" },
    { pattern: /{{lead\.fullName}}/g, replacement: "{{leadFullName}}" },
    { pattern: /{{leadName}}/g, replacement: "{{leadFirstName}}" },
    { pattern: /{{leadSurname}}/g, replacement: "{{leadLastName}}" },
    
    // Lead company variations
    { pattern: /{{lead\.company}}/g, replacement: "{{leadCompany}}" },
    { pattern: /{{companyName}}/g, replacement: "{{leadCompany}}" },
    
    // Lead contact variations
    { pattern: /{{lead\.email}}/g, replacement: "{{leadEmail}}" },
    { pattern: /{{lead\.phone}}/g, replacement: "{{leadPhone}}" },
    { pattern: /{{lead\.title}}/g, replacement: "{{leadTitle}}" },
    { pattern: /{{leadPosition}}/g, replacement: "{{leadTitle}}" },
    
    // Sender variations
    { pattern: /{{manager\.name}}/g, replacement: "{{senderName}}" },
    { pattern: /{{managerName}}/g, replacement: "{{senderName}}" },
    { pattern: /{{manager\.company}}/g, replacement: "{{senderCompany}}" },
    { pattern: /{{manager\.position}}/g, replacement: "{{senderPosition}}" },
  ];
  
  let normalizedPrompt = prompt;
  variableMappings.forEach(mapping => {
    normalizedPrompt = normalizedPrompt.replace(mapping.pattern, mapping.replacement);
  });
  
  return normalizedPrompt;
}

/**
 * Detects unknown placeholders in WhatsApp template
 */
export function detectUnknownWhatsAppPlaceholders(template: string): string[] {
  const placeholderRegex = /{{([^}]+)}}/g;
  const knownPlaceholders = [
    "leadFirstName", "leadLastName", "leadFullName", "leadCompany", "leadTitle",
    "leadEmail", "leadPhone", "leadIndustry", "leadWebsite", "leadStatus", "leadDescription",
    "senderName", "senderCompany", "senderPosition",
    "details_city", "details_country", "details_region", "details_address",
    "details_linkedin", "details_facebook", "details_twitter",
    "communications_linkedin", "communications_email", "communications_phone",
    "communications_website", "communications_vkontakte", "communications_telegram", "communications_whatsapp",
    "company_info_name", "company_info_description", "company_info_industry",
    "company_info_size", "company_info_website", "company_info_location",
    "lead_details", "lead_history", "company_info"
  ];
  
  const matches = [...template.matchAll(placeholderRegex)];
  const unknownPlaceholders = matches
    .map(match => match[1])
    .filter(placeholder => !knownPlaceholders.includes(placeholder));
  
  return [...new Set(unknownPlaceholders)]; // Remove duplicates
}

/**
 * Validates WhatsApp template and returns validation result
 */
export function validateWhatsAppTemplate(template: string): {
  isValid: boolean;
  unknownPlaceholders: string[];
  warnings: string[];
} {
  const unknownPlaceholders = detectUnknownWhatsAppPlaceholders(template);
  const warnings: string[] = [];
  
  // Check for common issues
  if (template.length > 1000) {
    warnings.push("Template is quite long for WhatsApp message");
  }
  
  if (!template.includes("{{leadFirstName}}") && !template.includes("{{leadFullName}}")) {
    warnings.push("Template doesn't include lead name personalization");
  }
  
  return {
    isValid: unknownPlaceholders.length === 0,
    unknownPlaceholders,
    warnings
  };
}

/**
 * WhatsApp Service Logger
 * Provides structured logging for WhatsApp message generation and sending
 */

export interface LogContext {
  leadId: number;
  leadName?: string;
  leadCompany?: string;
  agentId?: number;
  agentName?: string;
  sessionId?: string;
}

export interface LLMRequestParams {
  model: string;
  maxTokens: number;
  temperature: number;
  promptLength: number;
}

export interface LLMResponse {
  content: string;
  usage?: {
    input_tokens?: number;
    output_tokens?: number;
    total_tokens?: number;
  };
  executionTimeMs: number;
}

export interface MessageProcessing {
  rawResponse: string;
  processedContent: string;
  finalMessage: string;
  placeholdersReplaced: number;
}

export class WhatsAppLogger {
  private static formatTimestamp(): string {
    return new Date().toISOString();
  }

  private static formatContext(context: LogContext): string {
    const parts = [
      `Lead ID: ${context.leadId}`,
      context.leadName ? `Name: ${context.leadName}` : null,
      context.leadCompany ? `Company: ${context.leadCompany}` : null,
      context.agentId ? `Agent ID: ${context.agentId}` : null,
      context.agentName ? `Agent: ${context.agentName}` : null,
    ].filter(Boolean);
    
    return `[${parts.join(' | ')}]`;
  }

  private static logWithPrefix(prefix: string, context: LogContext, message: string, data?: any): void {
    const timestamp = this.formatTimestamp();
    const contextStr = this.formatContext(context);
    
    console.log(`\n🤖 [WhatsApp] ${prefix} - ${timestamp}`);
    console.log(`📋 Context: ${contextStr}`);
    console.log(`📝 ${message}`);
    
    if (data) {
      console.log(`📊 Data:`, data);
    }
  }

  /**
   * Log prompt processing before LLM request
   */
  static logPromptProcessing(
    context: LogContext,
    originalPrompt: string,
    finalPrompt: string,
    placeholdersFound: string[]
  ): void {
    this.logWithPrefix("PROMPT_PROCESSING", context, "Processing prompt for LLM request", {
      originalPromptLength: originalPrompt.length,
      finalPromptLength: finalPrompt.length,
      placeholdersFound: placeholdersFound.length,
      placeholders: placeholdersFound,
    });

    // Log prompts separately for better readability
    console.log(`📄 Original Prompt:\n${this.truncateText(originalPrompt, 500)}`);
    console.log(`📄 Final Prompt:\n${this.truncateText(finalPrompt, 500)}`);
  }

  /**
   * Log LLM request parameters
   */
  static logLLMRequest(context: LogContext, params: LLMRequestParams): void {
    this.logWithPrefix("LLM_REQUEST", context, "Sending request to Anthropic Claude", {
      model: params.model,
      maxTokens: params.maxTokens,
      temperature: params.temperature,
      promptLength: params.promptLength,
      estimatedTokens: Math.ceil(params.promptLength / 4), // Rough estimation
    });
  }

  /**
   * Log LLM response
   */
  static logLLMResponse(context: LogContext, response: LLMResponse): void {
    this.logWithPrefix("LLM_RESPONSE", context, "Received response from Anthropic Claude", {
      executionTimeMs: response.executionTimeMs,
      contentLength: response.content.length,
      usage: response.usage,
      tokensPerSecond: response.usage?.total_tokens 
        ? Math.round((response.usage.total_tokens / response.executionTimeMs) * 1000)
        : null,
    });

    console.log(`📄 Raw Response:\n${this.truncateText(response.content, 300)}`);
  }

  /**
   * Log message processing steps
   */
  static logMessageProcessing(context: LogContext, processing: MessageProcessing): void {
    this.logWithPrefix("MESSAGE_PROCESSING", context, "Processing LLM response", {
      rawResponseLength: processing.rawResponse.length,
      processedContentLength: processing.processedContent.length,
      finalMessageLength: processing.finalMessage.length,
      placeholdersReplaced: processing.placeholdersReplaced,
    });

    console.log(`📄 Processed Content:\n${this.truncateText(processing.processedContent, 200)}`);
    console.log(`📄 Final Message:\n${this.truncateText(processing.finalMessage, 200)}`);
  }

  /**
   * Log successful message generation
   */
  static logGenerationSuccess(
    context: LogContext,
    messageId: number,
    phoneNumber: string,
    totalExecutionTimeMs: number
  ): void {
    this.logWithPrefix("GENERATION_SUCCESS", context, "WhatsApp message generated successfully", {
      messageId,
      phoneNumber: this.maskPhoneNumber(phoneNumber),
      totalExecutionTimeMs,
      avgTimePerStep: Math.round(totalExecutionTimeMs / 4), // Rough estimation
    });
  }

  /**
   * Log generation errors
   */
  static logGenerationError(context: LogContext, error: Error, step: string): void {
    this.logWithPrefix("GENERATION_ERROR", context, `Error during ${step}`, {
      errorName: error.name,
      errorMessage: error.message,
      step,
      stack: error.stack?.split('\n').slice(0, 3).join('\n'), // First 3 lines of stack
    });
  }

  /**
   * Log message sending attempt
   */
  static logSendingAttempt(
    context: LogContext,
    messageId: number,
    phoneNumber: string,
    configId?: string
  ): void {
    this.logWithPrefix("SENDING_ATTEMPT", context, "Attempting to send WhatsApp message", {
      messageId,
      phoneNumber: this.maskPhoneNumber(phoneNumber),
      configId: configId || "default",
    });
  }

  /**
   * Log successful message sending
   */
  static logSendingSuccess(
    context: LogContext,
    messageId: number,
    phoneNumber: string,
    apiResponse: any
  ): void {
    this.logWithPrefix("SENDING_SUCCESS", context, "WhatsApp message sent successfully", {
      messageId,
      phoneNumber: this.maskPhoneNumber(phoneNumber),
      apiResponseStatus: apiResponse?.status || "unknown",
      apiResponseSize: JSON.stringify(apiResponse || {}).length,
    });
  }

  /**
   * Log sending errors
   */
  static logSendingError(
    context: LogContext,
    messageId: number,
    phoneNumber: string,
    error: Error,
    apiResponse?: any
  ): void {
    this.logWithPrefix("SENDING_ERROR", context, "Failed to send WhatsApp message", {
      messageId,
      phoneNumber: this.maskPhoneNumber(phoneNumber),
      errorName: error.name,
      errorMessage: error.message,
      apiResponseStatus: apiResponse?.status,
      apiResponseError: apiResponse?.error,
    });
  }

  /**
   * Log phone number validation
   */
  static logPhoneValidation(
    context: LogContext,
    originalPhone: string,
    validationResult: { isValid: boolean; formatted: string; error?: string }
  ): void {
    this.logWithPrefix("PHONE_VALIDATION", context, "Validating phone number", {
      originalPhone: this.maskPhoneNumber(originalPhone),
      isValid: validationResult.isValid,
      formattedPhone: validationResult.isValid 
        ? this.maskPhoneNumber(validationResult.formatted) 
        : null,
      error: validationResult.error,
    });
  }

  /**
   * Log agent retrieval
   */
  static logAgentRetrieval(
    context: LogContext,
    agentId: number,
    agentFound: boolean,
    agentData?: any
  ): void {
    this.logWithPrefix("AGENT_RETRIEVAL", context, "Retrieving agent data", {
      agentId,
      agentFound,
      agentName: agentData?.name,
      agentPromptLength: agentData?.prompt?.length,
      agentSettings: agentData?.settings,
    });
  }

  /**
   * Log database operations
   */
  static logDatabaseOperation(
    context: LogContext,
    operation: string,
    table: string,
    success: boolean,
    details?: any
  ): void {
    this.logWithPrefix("DATABASE_OPERATION", context, `Database ${operation} on ${table}`, {
      operation,
      table,
      success,
      details,
    });
  }

  /**
   * Utility: Truncate text for logging
   */
  private static truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + `... [truncated, total length: ${text.length}]`;
  }

  /**
   * Utility: Mask phone number for privacy
   */
  private static maskPhoneNumber(phone: string): string {
    if (!phone || phone.length < 4) return phone;
    const start = phone.substring(0, 2);
    const end = phone.substring(phone.length - 2);
    const middle = '*'.repeat(phone.length - 4);
    return `${start}${middle}${end}`;
  }

  /**
   * Log session start
   */
  static logSessionStart(context: LogContext, operation: string): void {
    console.log(`\n${'='.repeat(80)}`);
    console.log(`🚀 WhatsApp Service - ${operation.toUpperCase()} SESSION STARTED`);
    console.log(`⏰ Timestamp: ${this.formatTimestamp()}`);
    console.log(`📋 Context: ${this.formatContext(context)}`);
    console.log(`${'='.repeat(80)}`);
  }

  /**
   * Log session end
   */
  static logSessionEnd(context: LogContext, operation: string, success: boolean, totalTimeMs: number): void {
    console.log(`\n${'='.repeat(80)}`);
    console.log(`🏁 WhatsApp Service - ${operation.toUpperCase()} SESSION ${success ? 'COMPLETED' : 'FAILED'}`);
    console.log(`⏰ Timestamp: ${this.formatTimestamp()}`);
    console.log(`📋 Context: ${this.formatContext(context)}`);
    console.log(`⏱️  Total Execution Time: ${totalTimeMs}ms`);
    console.log(`${success ? '✅' : '❌'} Status: ${success ? 'SUCCESS' : 'FAILURE'}`);
    console.log(`${'='.repeat(80)}\n`);
  }
}

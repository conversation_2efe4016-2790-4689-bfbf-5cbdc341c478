import crypto from "crypto";
import { db } from "@/db";
import { apiTokens } from "@/db/schema/api-tokens";
import { eq } from "drizzle-orm";

/**
 * Генерирует новый уникальный API-токен
 */
export function generateApiToken(): string {
    return `sf_${crypto.randomBytes(32).toString('hex')}`;
}

/**
 * Проверяет действительность API-токена
 * @param token - API-токен для проверки
 * @returns Объект с флагом valid и ID пользователя, если токен действителен
 */
export async function verifyApiToken(token: string): Promise<{ valid: boolean; userId?: number }> {
    try {
        const result = await db.query.apiTokens.findFirst({
            where: eq(apiTokens.token, token),
            with: { user: true },
        });
        
        if (!result || !result.isActive) {
            return { valid: false };
        }
        
        // Проверка срока действия токена
        if (result.expiresAt && new Date(result.expiresAt) < new Date()) {
            return { valid: false };
        }
        
        // Обновление времени последнего использования
        await db
            .update(apiTokens)
            .set({ lastUsedAt: new Date() })
            .where(eq(apiTokens.id, result.id));
        
        return { valid: true, userId: result.userId };
    } catch (error) {
        console.error("Ошибка проверки API-токена:", error);
        return { valid: false };
    }
} 
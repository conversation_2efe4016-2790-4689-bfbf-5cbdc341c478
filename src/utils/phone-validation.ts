// Phone number validation utility - client-safe
// This file can be imported in both client and server components

export interface PhoneValidationResult {
  isValid: boolean;
  formatted: string;
  error?: string;
}

/**
 * Validates and formats Russian mobile phone numbers
 * Supports various input formats and normalizes to international format
 */
export function validatePhoneNumber(phone: string): PhoneValidationResult {
  if (!phone) {
    return { isValid: false, formatted: "", error: "Phone number is required" };
  }

  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, "");

  // Check if it's a valid Russian mobile number (starts with 7 or 8, followed by 9 digits)
  if (cleaned.length === 11 && (cleaned.startsWith("7") || cleaned.startsWith("8"))) {
    // Convert to international format (7XXXXXXXXXX)
    const formatted = cleaned.startsWith("8") ? "7" + cleaned.slice(1) : cleaned;
    return { isValid: true, formatted };
  }

  // Check if it's already in international format without country code
  if (cleaned.length === 10 && cleaned.startsWith("9")) {
    return { isValid: true, formatted: "7" + cleaned };
  }

  return { 
    isValid: false, 
    formatted: "", 
    error: "Invalid phone number format. Please use Russian mobile number format." 
  };
}

/**
 * Formats phone number for display
 * Example: 79115576367 -> +7 (911) 557-63-67
 */
export function formatPhoneForDisplay(phone: string): string {
  if (!phone) return "";
  
  const validation = validatePhoneNumber(phone);
  if (!validation.isValid) return phone;
  
  const formatted = validation.formatted;
  if (formatted.length === 11 && formatted.startsWith("7")) {
    return `+7 (${formatted.slice(1, 4)}) ${formatted.slice(4, 7)}-${formatted.slice(7, 9)}-${formatted.slice(9)}`;
  }
  
  return phone;
}

/**
 * Checks if phone number is valid for WhatsApp
 */
export function isValidForWhatsApp(phone: string): boolean {
  return validatePhoneNumber(phone).isValid;
}

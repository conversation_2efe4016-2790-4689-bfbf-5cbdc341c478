/**
 * Функция для автоматического преобразования переменных в промпте к системному формату
 * @param prompt Исходный промпт с произвольными переменными
 * @returns Преобразованный промпт с системными переменными
 */
export function normalizePromptVariables(prompt: string): string {
  // Маппинг распространенных форматов переменных к системному формату
  const variableMappings = [
    // Переменные лида
    { pattern: /{{leadFirstName}}/g, replacement: "{{lead.firstName}}" },
    { pattern: /{{leadLastName}}/g, replacement: "{{lead.lastName}}" },
    { pattern: /{{leadName}}/g, replacement: "{{lead.firstName}}" },
    { pattern: /{{leadSurname}}/g, replacement: "{{lead.lastName}}" },
    { pattern: /{{leadFullName}}/g, replacement: "{{lead.fullName}}" },
    { pattern: /{{leadCompany}}/g, replacement: "{{lead.company}}" },
    { pattern: /{{leadPosition}}/g, replacement: "{{lead.position}}" },
    { pattern: /{{leadEmail}}/g, replacement: "{{lead.email}}" },
    { pattern: /{{leadIndustry}}/g, replacement: "{{lead.industry}}" },
    { pattern: /{{leadCompanySize}}/g, replacement: "{{lead.companySize}}" },
    { pattern: /{{companyName}}/g, replacement: "{{lead.company}}" },
    { pattern: /{{companyDescription}}/g, replacement: "{{lead.companyDesc}}" },
    { pattern: /{{details_city}}/g, replacement: "{{lead.industry}}" }, // Ближайший аналог

    // Переменные менеджера
    { pattern: /{{senderName}}/g, replacement: "{{manager.name}}" },
    { pattern: /{{senderFirstName}}/g, replacement: "{{manager.name}}" },
    { pattern: /{{senderLastName}}/g, replacement: "{{manager.name}}" },
    { pattern: /{{senderPosition}}/g, replacement: "{{manager.position}}" },
    { pattern: /{{senderCompany}}/g, replacement: "{{manager.company}}" },
    { pattern: /{{senderEmail}}/g, replacement: "{{manager.email}}" },
    { pattern: /{{senderPhone}}/g, replacement: "{{manager.phone}}" },
    { pattern: /{{managerName}}/g, replacement: "{{manager.name}}" },

    // Переменные продукта
    { pattern: /{{productName}}/g, replacement: "{{product.name}}" },
    { pattern: /{{productDescription}}/g, replacement: "{{product.description}}" },
    { pattern: /{{productBenefits}}/g, replacement: "{{product.benefits}}" },
    { pattern: /{{productUseCases}}/g, replacement: "{{product.useCases}}" },

    // Прочие переменные
    { pattern: /{{tone}}/g, replacement: "{{tone}}" },
    { pattern: /{{language}}/g, replacement: "{{language}}" },
    { pattern: /{{emailType}}/g, replacement: "{{emailType}}" },
    { pattern: /{{customInstructions}}/g, replacement: "{{customInstructions}}" },
    { pattern: /{{leadHistory}}/g, replacement: "{{leadHistory}}" },

    // Добавляем поддержку нестандартных форматов из вашего примера
    { pattern: /{{lead_details}}/g, replacement: "{{lead.details}}" },
    { pattern: /{{lead_history}}/g, replacement: "{{leadHistory}}" },
    { pattern: /{{company_info}}/g, replacement: "{{lead.companyDesc}}" },
    
    // Дополнительные нестандартные форматы, которые могут встречаться
    { pattern: /{{company_description}}/g, replacement: "{{lead.companyDesc}}" },
    { pattern: /{{lead_info}}/g, replacement: "{{lead.details}}" },
    { pattern: /{{history}}/g, replacement: "{{leadHistory}}" },
  ];

  // Применяем все маппинги к промпту
  let normalizedPrompt = prompt;
  variableMappings.forEach(mapping => {
    normalizedPrompt = normalizedPrompt.replace(mapping.pattern, mapping.replacement);
  });

  return normalizedPrompt;
}

/**
 * Обнаруживает неизвестные переменные в промпте, которые не соответствуют системным
 * @param prompt Промпт для проверки
 * @returns Массив неизвестных переменных
 */
export function detectUnknownVariables(prompt: string): string[] {
  const variableRegex = /{{([^}]+)}}/g;
  const systemVariables = [
    "lead.firstName", "lead.lastName", "lead.fullName", "lead.company", 
    "lead.companyDesc", "lead.position", "lead.email", "lead.industry", 
    "lead.companySize", "manager.name", "manager.position", "manager.company", 
    "manager.email", "manager.phone", "product.name", "product.description", 
    "product.benefits", "product.useCases", "tone", "language", "emailType", 
    "customInstructions", "leadHistory"
  ];
  
  const matches = [...prompt.matchAll(variableRegex)];
  const unknownVariables = matches
    .map(match => match[1])
    .filter(variable => !systemVariables.includes(variable));
  
  return [...new Set(unknownVariables)]; // Удаляем дубликаты
}

/**
 * Нормализует переменные в промпте и заменяет их значениями
 * @param template Шаблон промпта с переменными
 * @param variables Объект с переменными для замены
 * @returns Промпт с нормализованными и замененными переменными
 */
export function normalizeAndReplaceVariables(template: string, variables: Record<string, string>): string {
  // Сначала нормализуем переменные в шаблоне
  const normalizedTemplate = normalizePromptVariables(template);
  
  // Затем заменяем переменные их значениями
  let result = normalizedTemplate;
  Object.entries(variables).forEach(([key, value]) => {
    if (value) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value);
    }
  });
  
  return result;
} 
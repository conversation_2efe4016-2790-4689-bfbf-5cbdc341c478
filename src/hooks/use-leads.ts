import { Lead } from '@/db/schema/leads';
import { useQuery } from '@tanstack/react-query';

// interface Lead {
//   id: number;
//   firstName: string;
//   lastName: string;
//   company: string;
//   email: string;
//   status?: string;
//   role?: string;
//   source?: string;
//   lastContactDate?: string;
//   interactionCount?: number;
//   lastAction?: string;
//   industry?: string;
//   companySize?: string;
//   website?: string;
//   details: any;
//   history: any;
//   company_info: any;
//   createdBy: string;
//   createdAt: Date;
//   updatedAt: Date;
// }

interface LeadsResponse {
    leads: Lead[];
    pagination?: {
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    };
}

async function fetchLeads(): Promise<Lead[]> {
    // Не передаем параметры пагинации, чтобы получить все лиды
    const response = await fetch('/api/leads');
    if (!response.ok) {
        throw new Error('Failed to fetch leads');
    }
    const data: LeadsResponse = await response.json();
    return data.leads;
}

export function useLeads() {
    return useQuery<Lead[]>({
        queryKey: ['leads'],
        queryFn: fetchLeads,
        staleTime: 30 * 1000, // Кэшируем на 30 секунд
    });
}

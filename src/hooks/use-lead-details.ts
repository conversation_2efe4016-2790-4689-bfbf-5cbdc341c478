import { useQuery } from "@tanstack/react-query";
import { Lead } from "@/db/schema/leads";

export interface LeadDetails {
    lead: Lead;
    history: {
        type: string;
        date: string;
        description: string;
    }[];
    opportunities: {
        id: string;
        name: string;
        stage: string;
        amount: number;
        closeDate: string;
    }[];
    activities: {
        id: string;
        type: string;
        subject: string;
        date: string;
        description: string;
    }[];
}

export function useLeadDetails(leadId: string | null) {
    return useQuery<LeadDetails>({
        queryKey: ["lead-details", leadId],
        enabled: !!leadId,
        queryFn: async () => {
            const response = await fetch(`/api/leads/${leadId}/details`);
            if (!response.ok) {
                const error = await response.text();
                console.error("Failed to fetch lead details:", error);
                throw new Error("Failed to fetch lead details");
            }
            const data = await response.json();
            return data;
        },
    });
}

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { PromptSettings, PromptMetadata, PromptType } from '@/db/schema/prompts';

interface Prompt {
  id: number;
  title: string;
  type: PromptType;
  content: string;
  description: string | null;
  isActive: boolean;
  version: number;
  settings: PromptSettings;
  metadata: PromptMetadata;
  createdAt: Date;
  updatedAt: Date;
}

interface CreatePromptData {
  title: string;
  type: PromptType;
  content: string;
  description: string | null;
  settings?: PromptSettings;
  metadata?: PromptMetadata;
}

interface UpdatePromptData extends CreatePromptData {
  id: number;
  isActive?: boolean;
}

interface TestPromptData {
  content: string;
  settings: {
    model: string;
    temperature: number;
    maxTokens: number;
  };
  variables?: Record<string, string>;
}

export function usePrompts() {
  return useQuery<Prompt[]>({
    queryKey: ['prompts'],
    queryFn: async () => {
      const response = await fetch('/api/prompts');
      if (!response.ok) {
        throw new Error('Failed to fetch prompts');
      }
      return response.json();
    },
  });
}

export function useCreatePrompt() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreatePromptData) => {
      const response = await fetch('/api/prompts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to create prompt');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['prompts'] });
    },
  });
}

export function useUpdatePrompt() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdatePromptData) => {
      const response = await fetch(`/api/prompts/${data.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to update prompt');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['prompts'] });
    },
  });
}

export function useDeletePrompt() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number) => {
      const response = await fetch(`/api/prompts/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete prompt');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['prompts'] });
    },
  });
}

export function useTestPrompt() {
  return useMutation({
    mutationFn: async (data: TestPromptData) => {
      // Отправляем запрос на тестирование промпта
      const response = await fetch('/api/v1/prompts/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to start prompt test');
      }

      // Получаем ответ от API
      const responseData = await response.json();
      
      // Проверяем статус ответа
      if (!responseData.success) {
        throw new Error(responseData.error || 'Failed to process prompt test');
      }
      
      // Если статус "processing", начинаем опрашивать результаты
      if (responseData.status === 'processing') {
        console.log(`[CLIENT] Test started in background with ID: ${responseData.testId}`);
        return await pollForResults(responseData.testId);
      } 
      // Если статус "completed", возвращаем результат напрямую
      else if (responseData.status === 'completed') {
        console.log('[CLIENT] Direct result received');
        return responseData.result;
      }
      // Если неизвестный статус, выбрасываем ошибку
      else {
        throw new Error(`Unknown status: ${responseData.status}`);
      }
    },
  });
}

// Функция для опроса результатов теста
async function pollForResults(testId: string, maxAttempts = 300, interval = 1000) {
  let attempts = 0;
  let currentInterval = interval;
  
  while (attempts < maxAttempts) {
    try {
      // Логируем каждые 10 секунд для отслеживания прогресса
      if (attempts % 10 === 0 && attempts > 0) {
        console.log(`[CLIENT] Still waiting for results... (${attempts} seconds passed)`);
      }
      
      // После определенного времени показываем дополнительную информацию
      if (attempts === 60) {
        console.log(`[CLIENT] Still waiting after 1 minute. This might take longer for complex prompts.`);
      }
      
      if (attempts === 180) {
        console.log(`[CLIENT] Still waiting after 3 minutes. The process is still running...`);
      }
      
      if (attempts === 240) {
        console.log(`[CLIENT] Still waiting after 4 minutes. This is taking longer than expected.`);
      }
      
      const response = await fetch(`/api/v1/prompts/test-callback?id=${testId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      // Обработка случая превышения количества запросов
      if (response.status === 429) {
        console.warn(`[CLIENT] Rate limiting detected, increasing polling interval`);
        currentInterval = Math.min(currentInterval * 1.5, 5000); // Увеличиваем интервал, но не более 5 секунд
        await new Promise(resolve => setTimeout(resolve, currentInterval * 2));
        attempts++;
        continue;
      }
      
      if (!response.ok) {
        // Если ошибка на стороне сервера, пробуем еще раз
        if (response.status >= 500) {
          console.warn(`[CLIENT] Server error (${response.status}), retrying...`);
          await new Promise(resolve => setTimeout(resolve, currentInterval));
          attempts++;
          continue;
        }
        
        throw new Error(`Failed to get test results: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Если результат еще не готов, ждем и пробуем снова
      if (data.status === 'pending') {
        await new Promise(resolve => setTimeout(resolve, currentInterval));
        attempts++;
        continue;
      }
      
      // Если результат готов, возвращаем его
      if (data.success && data.result) {
        console.log(`[CLIENT] Received results after ${attempts} seconds`);
        return data.result;
      } else {
        throw new Error(data.error || 'Failed to generate email');
      }
    } catch (error) {
      console.error('Error polling for results:', error);
      
      // Проверяем, является ли ошибка ошибкой сети, и в этом случае продолжаем попытки
      if (error instanceof TypeError || 
          (error instanceof Error && 
           (error.message.includes('NetworkError') || 
            error.message.includes('network') || 
            error.message.includes('CORS')))) {
        console.warn(`[CLIENT] Network error, retrying in ${currentInterval}ms...`);
        await new Promise(resolve => setTimeout(resolve, currentInterval));
        attempts++;
        continue;
      }
      
      throw error;
    }
  }
  
  throw new Error(`Timeout waiting for test results after ${maxAttempts} seconds. The operation might still be processing in the background. You can try refreshing the page later to see if results are available.`);
}

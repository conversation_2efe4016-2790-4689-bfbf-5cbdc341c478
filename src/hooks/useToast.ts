import { useToast as useToastUI } from "@/components/ui/use-toast";

export interface ToastProps {
  title?: string;
  description: string;
  variant?: 'default' | 'destructive';
}

export const useToast = () => {
  const { toast: toastUI } = useToastUI();

  const toast = ({ title, description, variant = 'default' }: ToastProps) => {
    toastUI({
      title,
      description,
      variant,
    });
  };

  return { toast };
};

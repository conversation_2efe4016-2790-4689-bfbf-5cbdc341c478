/* eslint-disable import/no-anonymous-default-export */
import { logger, task, wait } from "@trigger.dev/sdk/v3";
import { db } from "@/db";
import { leads } from "@/db/schema/leads";
import { eq, isNull, isNotNull, lt, desc, asc, and, or, sql } from "drizzle-orm";
import { extractDataFromUrl } from '@/services/tavily';

// Упрощенные параметры задачи
interface EnrichLeadsPayload {
  batchSize?: number;
  mode?: 'new' | 'outdated' | 'all' | 'missing_fields'; // Режим работы
  minAgeHours?: number; // Для устаревших данных - минимальный возраст в часах
}

// Интерфейс для детальной информации о лиде
interface LeadDetails {
  linkedin_info?: {
    url: string;
    extractedAt: string;
    content?: string;
    title?: string;
    summary?: string;
    error?: string;
  };
  facebook_info?: {
    url: string;
    extractedAt: string;
    content?: string;
    title?: string;
    summary?: string;
    error?: string;
  };
  company_info?: {
    url: string;
    extractedAt: string;
    content?: string;
    title?: string;
    summary?: string;
  };
  lastUpdated: string;
}

export const enrichLeadsTask = task({
  id: "enrich-leads-data",
  maxDuration: 600, // 10 минут
  run: async (payload: EnrichLeadsPayload = {}, { ctx }) => {
    logger.log("Начало обогащения данных лидов");
    
    const { 
      batchSize = 20, 
      mode = 'new',
      minAgeHours = 24 // По умолчанию считаем устаревшими данные старше 24 часов
    } = payload;
    
    const startTime = Date.now();
    const results = {
      total: 0,
      enriched: 0,
      failed: 0,
      skipped: 0,
      details: [] as Array<{ id: string | number; status: string; message?: string }>,
    };

    try {
      // Получаем лидов для обработки в зависимости от режима
      let leadsToProcess = [];
      
      switch (mode) {
        case 'new':
          // Лиды без данных
          leadsToProcess = await db.select()
            .from(leads)
            .where(isNull(leads.details))
            .limit(batchSize);
          break;
          
        case 'outdated':
          // Лиды с устаревшими данными
          const cutoffDate = new Date();
          cutoffDate.setHours(cutoffDate.getHours() - minAgeHours);
          
          leadsToProcess = await db.select()
            .from(leads)
            .where(
              and(
                isNotNull(leads.details),
                lt(leads.updatedAt, cutoffDate)
              )
            )
            .orderBy(asc(leads.updatedAt)) // Сначала самые старые
            .limit(batchSize);
          break;
          
        case 'all':
        default:
          // Сначала берем лидов без данных
          const leadsWithoutDetails = await db.select()
            .from(leads)
            .where(isNull(leads.details))
            .limit(batchSize);
            
          // Если есть место в партии, добавляем лидов с устаревшими данными
          if (leadsWithoutDetails.length < batchSize) {
            const cutoffDate = new Date();
            cutoffDate.setHours(cutoffDate.getHours() - minAgeHours);
            
            const remainingCount = batchSize - leadsWithoutDetails.length;
            const leadsWithOldDetails = await db.select()
              .from(leads)
              .where(
                and(
                  isNotNull(leads.details),
                  lt(leads.updatedAt, cutoffDate)
                )
              )
              .orderBy(asc(leads.updatedAt))
              .limit(remainingCount);
              
            leadsToProcess = [...leadsWithoutDetails, ...leadsWithOldDetails];
          } else {
            leadsToProcess = leadsWithoutDetails;
          }
          break;
          
        case 'missing_fields':
          // Выбираем лидов, у которых отсутствуют нужные поля в details
          leadsToProcess = await db.select()
            .from(leads)
            .where(
              and(
                isNotNull(leads.details),
                or(
                  // Есть LinkedIn в communications, но нет linkedin_info в details
                  and(
                    sql`${leads.communications}->>'linkedin' IS NOT NULL`,
                    sql`${leads.details}->>'linkedin_info' IS NULL`
                  ),
                  // Есть Facebook в communications, но нет facebook_info в details
                  and(
                    sql`${leads.communications}->>'facebook' IS NOT NULL`,
                    sql`${leads.details}->>'facebook_info' IS NULL`
                  ),
                  // Есть Website в communications, но нет company_info в details
                  and(
                    sql`${leads.communications}->>'website' IS NOT NULL`,
                    sql`${leads.details}->>'company_info' IS NULL`
                  )
                )
              )
            )
            .limit(batchSize);
          break;
      }
      
      results.total = leadsToProcess.length;
      logger.log(`Найдено ${leadsToProcess.length} лидов для обработки (режим: ${mode})`);
      
      if (leadsToProcess.length === 0) {
        logger.log("Нет лидов для обработки");
        return {
          success: true,
          results
        };
      }
      
      // Обрабатываем каждого лида
      for (const lead of leadsToProcess) {
        try {
          logger.log(`Обработка лида: ${lead.id}`);
          logger.log('lead', { communications: JSON.stringify(lead.communications, null, 2) });
          const existingDetails = (lead.details as LeadDetails) || {};
          const communications = lead.communications || {};
          
          const enrichedData: Partial<LeadDetails> = {
            lastUpdated: new Date().toISOString(),
          };
          
          // LinkedIn обработка
          if (communications.linkedin) {
            logger.log(`Извлечение данных LinkedIn для лида: ${lead.id}`);
            logger.log('communications.linkedin', { url: communications.linkedin });
            const extractResult = await extractDataFromUrl(communications.linkedin);
            logger.log('extractResult', extractResult);
            if (extractResult.success) {
              enrichedData.linkedin_info = {
                url: communications.linkedin,
                extractedAt: new Date().toISOString(),
                content: extractResult.data.results[0]?.raw_content,
                title: extractResult.data.results[0]?.title || '',
                summary: extractResult.data.results[0]?.content || '',
              };
              logger.log(`Данные LinkedIn успешно извлечены для лида: ${lead.id}`);
            } else {
              logger.error(`Не удалось извлечь данные LinkedIn для лида: ${lead.id}`, {
                error: extractResult.error
              });
              enrichedData.linkedin_info = {
                url: communications.linkedin,
                extractedAt: new Date().toISOString(),
                error: extractResult.error,
              };
              logger.log('enrichedData.linkedin_info', enrichedData.linkedin_info);
            }
          }
          
          // Facebook обработка
          if (communications.facebook) {
            logger.log(`Извлечение данных Facebook для лида: ${lead.id}`);
            logger.log('communications.facebook', { url: communications.facebook });
            const extractResult = await extractDataFromUrl(communications.facebook);
            logger.log('extractResult', extractResult);
            if (extractResult.success) {
              enrichedData.facebook_info = {
                url: communications.facebook,
                extractedAt: new Date().toISOString(),
                content: extractResult.data.results[0]?.raw_content,
                title: extractResult.data.results[0]?.title || '',
                summary: extractResult.data.results[0]?.content || '',
              };
              logger.log(`Данные Facebook успешно извлечены для лида: ${lead.id}`);
            } else {
              logger.error(`Не удалось извлечь данные Facebook для лида: ${lead.id}`, {
                error: extractResult.error
              });
              enrichedData.facebook_info = {
                url: communications.facebook,
                extractedAt: new Date().toISOString(),
                error: extractResult.error,
              };
              logger.log('enrichedData.facebook_info', enrichedData.facebook_info);
            }
          }
          
          // Обработка сайта компании
          if (communications.website) {
            logger.log(`Извлечение данных о компании для лида: ${lead.id}`);
            logger.log('communications.website', { url: communications.website });
            const extractResult = await extractDataFromUrl(communications.website);
            
            if (extractResult.success) {
              enrichedData.company_info = {
                url: communications.website,
                extractedAt: new Date().toISOString(),
                content: extractResult.data.results[0]?.raw_content,
                title: extractResult.data.results[0]?.title || '',
                summary: extractResult.data.results[0]?.content || '',
              };
              logger.log(`Данные о компании успешно извлечены для лида: ${lead.id}`);
            }
          }
          
          // Сохраняем обогащенные данные
          const updatedDetails = {
            ...existingDetails,
            ...enrichedData,
          };
          
          const updateResult = await db.update(leads)
            .set({
              details: updatedDetails as any, // Явное приведение типа для jsonb поля
              updatedAt: new Date(),
            })
            .where(eq(leads.id, lead.id));

          logger.log('updateResult', { result: JSON.stringify(updateResult, null, 2) });
          
          if (!updateResult || updateResult.rowCount === 0) {
            logger.warn(`Обновление данных для лида ${lead.id} не выполнено. Проверка не прошла.`);
            throw new Error(`Не удалось обновить данные для лида ${lead.id}`);
          }
          
          // Добавляем проверку после обновления
          const updatedLead = await db.select()
            .from(leads)
            .where(eq(leads.id, lead.id))
            .limit(1);

          if (updatedLead.length > 0) {
            logger.log('ПРОВЕРКА ОБНОВЛЕНИЯ:', {
              id: lead.id,
              details: updatedLead[0].details,
              linkedin_info: updatedLead[0].details?.linkedin_info,
              facebook_info: updatedLead[0].details?.facebook_info,
              company_info: updatedLead[0].details?.company_info
            });
            
            // Проверяем, что данные были сохранены правильно
            const savedDetails = updatedLead[0].details || {};
            
            if (communications.linkedin && !savedDetails.linkedin_info) {
              logger.error(`Ошибка: linkedin_info не был сохранен для лида ${lead.id}`);
            }
            
            if (communications.facebook && !savedDetails.facebook_info) {
              logger.error(`Ошибка: facebook_info не был сохранен для лида ${lead.id}`);
            }
            
            if (communications.website && !savedDetails.company_info) {
              logger.error(`Ошибка: company_info не был сохранен для лида ${lead.id}`);
            }
          }

          logger.log(`Данные лида ${lead.id} успешно обновлены в базе данных`);
          logger.log('updatedDetails', updatedDetails);
            
          results.enriched++;
          results.details.push({
            id: lead.id,
            status: "success",
          });
          
          logger.log(`Лид ${lead.id} успешно обогащен данными`);
          
          // Небольшая пауза между запросами, чтобы не перегружать API
          await wait.for({ seconds: 1 });
          
        } catch (error: any) {
          logger.error(`Ошибка при обработке лида ${lead.id}:`, {
            error: error.message || 'Неизвестная ошибка'
          });
          
          results.failed++;
          results.details.push({
            id: lead.id,
            status: "error",
            message: error.message || 'Неизвестная ошибка',
          });
        }
      }
      
      const executionTime = Date.now() - startTime;
      logger.log(`Обогащение данных завершено. Время выполнения: ${executionTime}мс`);
      
      return {
        success: true,
        executionTime,
        results,
      };
      
    } catch (error: any) {
      logger.error("Ошибка при обогащении данных лидов:", {
        error: error.message || 'Неизвестная ошибка'
      });
      
      return {
        success: false,
        error: error.message || 'Неизвестная ошибка',
        results,
      };
    }
  },
});

// Для запуска обогащения по расписанию
export const scheduledEnrichLeadsTask = task({
  id: "scheduled-enrich-leads",
  maxDuration: 600,
  run: async () => {
    logger.log("Запуск запланированного обогащения данных лидов");
    
    // Запускаем задачу обогащения в режиме 'all' с размером партии 50
    const enrichTask = enrichLeadsTask as any;
    const result = await enrichTask.run({
      batchSize: 50,
      mode: 'all',
      minAgeHours: 48 // Обновляем данные, которым больше 48 часов
    });
    
    logger.log(`Запланированное обогащение данных завершено: обработано ${result.results.total} лидов`);
    return result;
  },
}); 
import { logger, task, wait } from "@trigger.dev/sdk/v3";
import { tavily } from "@tavily/core";


export const helloWorldTask = task({
  id: "hello-world",
  // Set an optional maxDuration to prevent tasks from running indefinitely
  maxDuration: 300, // Stop executing after 300 secs (5 mins) of compute
  run: async (payload: any, { ctx }) => {
    logger.log("Hello, world!", { payload, ctx });
    const tvly = tavily({ apiKey: process.env.NEXT_TAVILY_API_KEY });

    const urls = ['https://linkedin.com/in/phil-churilov']
    try {
      const options = {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${process.env.NEXT_TAVILY_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          urls: urls[0],
          include_images: false,
          extract_depth: 'basic'
        })
      };
      
      const response = await fetch('https://api.tavily.com/extract', options);
      const extractResults = await response.json();
      
      console.log('extractResults', extractResults);
    } catch (error) {
      console.error('Error extracting content:', error);
    }

    return {
      message: "Hello, world!",
    }
  },
});


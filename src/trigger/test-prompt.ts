/* eslint-disable import/no-anonymous-default-export */
import { logger, task, wait } from "@trigger.dev/sdk/v3";
import { anthropic } from "@/services/anthropic";
import { formatPrompt } from "@/services/prompts";
import { db } from "@/db";
import { promptTests } from "@/db/schema/prompt-tests";
import { eq } from "drizzle-orm";
import { extractDataFromUrl } from '@/services/tavily';


// Тип для параметров задачи
interface TestPromptPayload {
  content: string;
  variables?: Record<string, any>;
  settings: {
    model: string;
    temperature: number;
    maxTokens: number;
  };
  testId: string;
}

// Задача для тестирования промптов
export const testPromptTask = task({
  id: "test-prompt",
  // Установка максимальной продолжительности выполнения (5 минут)
  maxDuration: 300,
  run: async (payload: TestPromptPayload, { ctx }) => {
    logger.log("Начало тестирования промпта", { testId: payload.testId });
    logger.log(`Длина контента: ${payload.content?.length || 0} символов`);

    const { content, variables = {}, settings, testId } = payload;

    if (!content) {
      logger.error(`Отсутствует контент шаблона для теста с ID: ${testId}`);
      throw new Error("Требуется шаблон электронного письма");
    }

    // Обновляем статус теста на "PROCESSING"
    await db.insert(promptTests)
      .values({
        id: testId,
        prompt: content,
        settings: settings,
        status: 'PROCESSING',
        startedAt: new Date(),
      })
      .onConflictDoUpdate({
        target: promptTests.id,
        set: {
          status: 'PROCESSING',
          startedAt: new Date(),
          updatedAt: new Date(),
        }
      });

    try {
      // 1. Форматируем промпт с переменными
      const startTime = Date.now();
      const formattedPrompt = formatPrompt(content, variables);
      logger.log(`Промпт отформатирован, длина: ${formattedPrompt.length} символов`);
      let fullPrompt = formattedPrompt;
      // console.log('variables.communications_linkedin', variables.communications_linkedin)
      // if (variables.communications_linkedin) {
      //   const extractResult = await extractDataFromUrl(
      //     variables.communications_linkedin
      //   );
      
      //   console.log('extractResult', extractResult)
      //   if (extractResult.success) {
      //     fullPrompt = fullPrompt.replace(
      //     variables.communications_linkedin,
      //     `\n\nСодержимое с linkedin:\n${extractResult.data.results[0]?.raw_content}\n\n`
      //    );
      //   } else {
      //     console.error(extractResult.error);
      //     // Обработка ошибки
      //   }
      // }
      
      // console.log('variables.communications_facebook', variables.communications_facebook)
      // if (variables.communications_facebook) {
      //   const extractResult = await extractDataFromUrl(
      //     variables.communications_facebook
      //   );

      //   if (extractResult.success) {
      //     // Используем данные
      //     const extractData = extractResult.data;
      //     fullPrompt = fullPrompt.replace(
      //       variables.communications_facebook,
      //       `\n\nСодержимое с facebook:\n${extractData.results[0]?.raw_content}\n\n`
      //     );
      //   } else {
      //     console.error(extractResult.error);
      //     // Обработка ошибки
      //   }
      // }
      console.log('fullPrompt', fullPrompt)
      // 2. Генерируем email используя Anthropic
      logger.log(`Запуск API-вызова Anthropic для теста с ID: ${testId}`);
      const response = await anthropic.generateEmail(fullPrompt, {
        model: settings.model,
        max_tokens: settings.maxTokens,
        temperature: settings.temperature,
      });
      logger.log(`API-вызов Anthropic успешно выполнен для теста с ID: ${testId}`);

      // 3. Форматируем результат
      const formattedResponse = {
        ...response,
        subject: formatPrompt(response.subject, variables),
        body: formatPrompt(response.body, variables),
        formattedPrompt: fullPrompt
      };
      logger.log(`Ответ отформатирован для теста с ID: ${testId}, тема: "${formattedResponse.subject.substring(0, 30)}..."`);

      const executionTime = Date.now() - startTime;
      logger.log(`Время выполнения: ${executionTime}мс`);

      // 4. Сохраняем результат в базу данных
      logger.log(`Сохранение результата в БД для теста с ID: ${testId}`);
      await db.update(promptTests)
        .set({
          // Устанавливает новые значения для полей в записи БД:
          result: formattedResponse, // Сохраняет результат генерации
          status: 'SUCCESS', // Обновляет статус на успешный
          completedAt: new Date(), // Устанавливает время завершения
          updatedAt: new Date(), // Обновляет время последнего изменения
        })
        .where(eq(promptTests.id, testId));
      
      logger.log(`Результат успешно сохранен в БД для теста с ID: ${testId}`);

      // Возвращаем результат
      return {
        success: true,
        testId,
        result: formattedResponse,
        executionTime
      };
    } catch (error: any) {
      // Обработка ошибок
      logger.error(`Ошибка при генерации для теста с ID: ${testId}:`, { 
        error: error.message || 'Неизвестная ошибка' 
      });
      
      // Сохраняем информацию об ошибке в БД
      try {
        await db.update(promptTests)
          .set({
            status: 'ERROR',
            error: error instanceof Error ? error.message : "Неизвестная ошибка",
            completedAt: new Date(),
            updatedAt: new Date(),
          })
          .where(eq(promptTests.id, testId));
        
        logger.log(`Детали ошибки сохранены в БД для теста с ID: ${testId}`);
      } catch (saveError: any) {
        logger.error(`Не удалось сохранить детали ошибки в БД:`, {
          error: saveError.message || 'Неизвестная ошибка'
        });
      }
      
      // Пробрасываем ошибку дальше
      throw error;
    }
  },
});

// Задача для отложенного тестирования промптов (с отсрочкой)
export const scheduledTestPromptTask = task({
  id: "scheduled-test-prompt",
  // Установка максимальной продолжительности выполнения (5 минут)
  maxDuration: 300,
  run: async (payload: TestPromptPayload & { delaySeconds?: number }, { ctx }) => {
    const { delaySeconds = 0, ...testPromptPayload } = payload;
    
    if (delaySeconds > 0) {
      logger.log(`Ожидание ${delaySeconds} секунд перед запуском теста с ID: ${testPromptPayload.testId}`);
      await wait.for({ seconds: delaySeconds });
    }
    
    // Вызываем тестирование промпта непосредственно
    const { content, variables, settings, testId } = testPromptPayload;
    
    // Выполняем те же шаги, что и в testPromptTask
    logger.log("Начало тестирования промпта", { testId });
    logger.log(`Длина контента: ${content?.length || 0} символов`);

    if (!content) {
      logger.error(`Отсутствует контент шаблона для теста с ID: ${testId}`);
      throw new Error("Требуется шаблон электронного письма");
    }

    // Остальной код аналогичен testPromptTask...
    // (Для краткости не дублирую весь код)
    
    // Обновляем статус теста на "PROCESSING"
    await db.insert(promptTests)
      .values({
        id: testId,
        prompt: content,
        settings: settings,
        status: 'PROCESSING',
        startedAt: new Date(),
      })
      .onConflictDoUpdate({
        target: promptTests.id,
        set: {
          status: 'PROCESSING',
          startedAt: new Date(),
          updatedAt: new Date(),
        }
      });

    try {
      // 1. Форматируем промпт с переменными
      const startTime = Date.now();
      const formattedPrompt = formatPrompt(content, variables || {});
      
      // 2. Генерируем email используя Anthropic
      const response = await anthropic.generateEmail(formattedPrompt, {
        model: settings.model,
        max_tokens: settings.maxTokens,
        temperature: settings.temperature,
      });

      // 3. Форматируем результат
      const formattedResponse = {
        ...response,
        subject: formatPrompt(response.subject, variables || {}),
        body: formatPrompt(response.body, variables || {}),
        formattedPrompt
      };
      
      const executionTime = Date.now() - startTime;

      // 4. Сохраняем результат в базу данных
      await db.update(promptTests)
        .set({
          result: formattedResponse,
          status: 'SUCCESS',
          completedAt: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(promptTests.id, testId));

      // Возвращаем результат
      return {
        success: true,
        testId,
        result: formattedResponse,
        executionTime
      };
    } catch (error: any) {
      // Обработка ошибок по аналогии с testPromptTask
      throw error;
    }
  },
}); 
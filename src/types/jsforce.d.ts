declare module 'jsforce' {
    export interface ConnectionOptions {
        oauth2?: OAuth2;
        accessToken?: string;
        refreshToken?: string;
        instanceUrl?: string;
    }

    export interface TokenResponse {
        access_token: string;
        instance_url: string;
        refresh_token?: string;
    }

    export interface SaveResult {
        success: boolean;
        id: string;
        errors: string[];
    }

    export interface SearchRecord {
        Id: string;
        [key: string]: any;
    }

    export interface DescribeSObjectResult {
        name: string;
        label: string;
        fields: any[];
    }

    export class Connection {
        constructor(options?: ConnectionOptions);
        oauth2?: OAuth2;
        
        // Identity methods
        identity(callback?: (err: Error, res: any) => void): Promise<any>;
        
        // Query methods
        query<T>(soql: string): Promise<{ records: T[] }>;
        search<T = SearchRecord>(sosl: string): Promise<{ searchRecords: T[] }>;
        
        // SObject methods
        sobject(type: string): any;
        describe(): Promise<DescribeSObjectResult>;

        // Request methods
        request(options: any): Promise<any>;
        instanceUrl: string;
        accessToken: string;
    }

    export class OAuth2 {
        constructor(options: {
            clientId: string;
            clientSecret: string;
            redirectUri: string;
        });

        getAuthorizationUrl(params?: { 
            scope?: string; 
            state?: string;
            prompt?: 'login' | 'consent' | 'select_account' | 'none';
            code_challenge?: string;
            code_challenge_method?: 'S256' | 'plain';
        }): string;
        
        requestToken(code: string, params?: {
            code_verifier?: string;
        }): Promise<TokenResponse>;
        
        refreshToken(refreshToken: string): Promise<TokenResponse>;
    }

    export interface Lead {
        Id: string;
        FirstName?: string;
        LastName?: string;
        Email?: string;
        Company?: string;
        Title?: string;
        Phone?: string;
        Status?: string;
        [key: string]: any;
    }
}
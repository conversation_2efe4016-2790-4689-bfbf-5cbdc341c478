export interface Lead {
  id?: number;
  salesforceId: string;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  company?: string | null;
  title?: string | null;
  phone?: string | null;
  status?: string | null;
  industry?: string | null;
  rating?: string | null;
  leadSource?: string | null;
  description?: string | null;
  website?: string | null;
  numberOfEmployees?: number | null;
  lastModifiedDate?: Date | null;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface GeneratedEmail {
  subject: string;
  content: string;
  prompt: string;
}

export interface SalesforceTokens {
  access_token: string;
  refresh_token?: string;
  instance_url: string;
}

export interface EmailTemplate {
  id: number;
  title: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface EmailConfig {
  id: string;
  smtp: string;
  port: number;
  email: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export type LLMProvider = 'openai' | 'anthropic';

export type OpenAIModel = 
  | 'gpt-4'
  | 'gpt-4-turbo'
  | 'gpt-3.5-turbo'
  | 'chatgpt-4o-latest'
  | 'gpt-4o-mini'
  | 'o1-mini'
  | 'o1-preview';

export type AnthropicModel = 
  | 'claude-3-5-sonnet-20241022'
  | 'claude-3-5-haiku-20241022';

export type LLMModel = OpenAIModel | AnthropicModel;

export interface AgentSettings {
  llmProvider: LLMProvider;
  model: LLMModel;
  temperature: number;
  maxTokens: number;
  [key: string]: any;
}

export interface AgentMetadata {
  lastUpdate?: {
    timestamp: string;
    user: string;
  };
  lastTest?: {
    timestamp: string;
    result: string;
  };
  [key: string]: any;
}

export interface Agent {
  id: number;
  uuid?: string;
  name: string;
  description: string;
  prompt: string;
  promptId?: number;
  settings: AgentSettings;
  metadata: AgentMetadata;
  isActive: boolean;
  version: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export type CreateAgentInput = Omit<
  Agent,
  'id' | 'createdBy' | 'createdAt' | 'updatedAt' | 'version' | 'metadata'
>;

export type UpdateAgentInput = Partial<CreateAgentInput>;

import { DefaultSession } from "next-auth";

declare module "next-auth" {
  // Extend session to hold the access_token
  interface Session {
    user: {
      id: string;
      email: string;
      name?: string | null;
    } & DefaultSession["user"];
    accessToken?: string;
    refreshToken?: string;
    instanceUrl?: string;
  }

  interface User {
    id: string;
    email: string;
    name?: string | null;
  }
}

declare module "next-auth/jwt" {
  // Extend token to hold the access_token before it gets put into session
  interface JWT {
    id: string;
    email: string;
    name?: string | null;
    accessToken?: string;
    refreshToken?: string;
    instanceUrl?: string;
  }
}

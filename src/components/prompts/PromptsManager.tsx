import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { type Prompt, type PromptMetadata } from "@/db/schema/prompts";
import { PromptEditor } from "./PromptEditor";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface EditPromptData {
  title: string;
  type: string;
  content: string;
  description: string;
  isActive: boolean;
  settings: {};
  metadata: PromptMetadata;
}

interface PromptData {
  id: string;
  name: string;
  content: string;
  createdAt: string;
  updatedAt: string;
}

export function PromptsManager() {
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const fetchPrompts = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/prompts");
      if (!response.ok) {
        throw new Error("Failed to fetch prompts");
      }
      const data = await response.json();
      setPrompts(data);
    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch prompts',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchPrompts();
  }, [fetchPrompts]);

  const handleDeletePrompt = async (promptId: number) => {
    try {
      const response = await fetch(`/api/prompts/${promptId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete prompt");

      await fetchPrompts();
      toast({
        title: "Success",
        description: "Prompt deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete prompt",
        variant: "destructive",
      });
    }
  };

  const handleSavePrompt = async (promptData: Partial<Prompt>) => {
    try {
      setIsLoading(true);
      const url = selectedPrompt 
        ? `/api/prompts/${selectedPrompt.id}` 
        : "/api/prompts";
      
      const response = await fetch(url, {
        method: selectedPrompt ? "PUT" : "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(promptData),
      });

      if (!response.ok) {
        throw new Error("Failed to save prompt");
      }

      await fetchPrompts();
      setIsEditing(false);
      setSelectedPrompt(null);
      toast({
        title: "Success",
        description: `Prompt ${selectedPrompt ? "updated" : "created"} successfully`,
      });
    } catch (error) {
      console.error("Error saving prompt:", error);
      toast({
        title: "Error",
        description: "Failed to save prompt",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Prompts</h2>
        <Button onClick={() => setIsEditing(true)}>
          Create New Prompt
        </Button>
      </div>

      <Dialog open={isEditing} onOpenChange={(open) => !open && setIsEditing(false)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedPrompt ? "Edit Prompt" : "Create New Prompt"}</DialogTitle>
          </DialogHeader>
          <PromptEditor
            prompt={selectedPrompt || undefined}
            mode={selectedPrompt ? "edit" : "create"}
            onSave={handleSavePrompt}
            onCancel={() => {
              setIsEditing(false);
              setSelectedPrompt(null);
            }}
          />
        </DialogContent>
      </Dialog>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {prompts.map((prompt) => (
              <TableRow key={prompt.id}>
                <TableCell>{prompt.title}</TableCell>
                <TableCell>{prompt.type}</TableCell>
                <TableCell>{prompt.description}</TableCell>
                <TableCell>{prompt.isActive ? "Active" : "Inactive"}</TableCell>
                <TableCell className="text-right space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedPrompt(prompt);
                      setIsEditing(true);
                    }}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDeletePrompt(prompt.id)}
                  >
                    Delete
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useLeads } from "@/hooks/use-leads";
import type { Lead } from "@/db/schema/leads";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";
import { 
  AlertCircle, 
  Bug, 
  Check, 
  Clock, 
  Info, 
  RefreshCw, 
  Loader2, 
  Save
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

export interface PromptTesterProps {
  content: string;
  onTest: (
    content: string, 
    variables: Record<string, string>,
    settings: {
      model: string;
      temperature: number;
      maxTokens: number;
    }
  ) => Promise<any>;
}

export function PromptTester({ content, onTest }: PromptTesterProps) {
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingStatus, setLoadingStatus] = useState<string>("");
  const [debugMode, setDebugMode] = useState(false);
  const [testId, setTestId] = useState<string>("");
  const [requestStatus, setRequestStatus] = useState<{
    step: string;
    message: string;
    timestamp: Date;
    success?: boolean;
    error?: string;
  } | null>(null);
  const [statusLogs, setStatusLogs] = useState<{
    step: string;
    message: string;
    timestamp: Date;
    success?: boolean;
    error?: string;
  }[]>([]);
  const [processingTime, setProcessingTime] = useState<number>(0);
  const [processingTimer, setProcessingTimer] = useState<NodeJS.Timeout | null>(null);
  const [result, setResult] = useState<{
    subject: string;
    body: string;
    formattedPrompt: string;
  } | null>(null);
  const [settings, setSettings] = useState({
    model: "anthropic/claude-3-5-haiku",
    temperature: 0.2,
    maxTokens: 4096
  });

  const { data: leads = [], isLoading: isLeadsLoading } = useLeads();
  const { toast } = useToast();

  // Добавляем логирование статуса
  const addStatusLog = (step: string, message: string, success?: boolean, error?: string) => {
    const logEntry = {
      step,
      message,
      timestamp: new Date(),
      success,
      error
    };
    
    console.log(`[UI] ${step}: ${message}${error ? ` (Error: ${error})` : ''}`);
    setRequestStatus(logEntry);
    setStatusLogs(prev => [logEntry, ...prev]);
  };

  useEffect(() => {
    if (leads.length > 0 && !selectedLead) {
      setSelectedLead(leads[0]);
    }
  }, [leads, selectedLead]);

  useEffect(() => {
    // Очистка таймера при размонтировании компонента
    return () => {
      if (processingTimer) {
        clearInterval(processingTimer);
      }
    };
  }, [processingTimer]);

  const handleTestPrompt = async () => {
    if (!selectedLead) return;
    
    // Сбрасываем состояние
    setResult(null);
    setStatusLogs([]);
    setProcessingTime(0);
    if (processingTimer) {
      clearInterval(processingTimer);
    }
    
    // Запускаем таймер для отслеживания времени выполнения
    const timer = setInterval(() => {
      setProcessingTime(prev => prev + 1);
    }, 1000);
    setProcessingTimer(timer);
    
    setIsLoading(true);
    setLoadingStatus("Запуск теста промпта...");
    addStatusLog("init", "Подготовка к тестированию промпта");
    
    // Используем тестовый режим, если он включен
    const testParams = debugMode ? "?test=true" : "";
    
    try {
      // Convert null values to empty strings to satisfy Record<string, string>
      const variables: Record<string, string> = {
        // Основные поля
        leadId: selectedLead.id?.toString() || '',
        leadFirstName: selectedLead.firstName || '',
        leadLastName: selectedLead.lastName || '',
        leadEmail: selectedLead.email || '',
        leadCompany: selectedLead.company || '',
        leadTitle: selectedLead.title || '',
        leadPhone: selectedLead.phone || '',
        leadIndustry: selectedLead.industry || '',
        leadRating: selectedLead.rating || '',
        leadSource: selectedLead.leadSource || '',
        leadDescription: selectedLead.description || '',
        leadWebsite: selectedLead.website || '',
        leadNumberOfEmployees: selectedLead.numberOfEmployees?.toString() || '',
        leadStatus: selectedLead.status || '',
        leadSalesforceId: selectedLead.salesforceId || '',
        leadCreatedAt: selectedLead.createdAt instanceof Date ? selectedLead.createdAt.toISOString() : selectedLead.createdAt || '',
        leadUpdatedAt: selectedLead.updatedAt instanceof Date ? selectedLead.updatedAt.toISOString() : selectedLead.updatedAt || '',
        leadCreatedBy: selectedLead.createdBy || '',
        leadLastModifiedDate: selectedLead.lastModifiedDate instanceof Date ? selectedLead.lastModifiedDate.toISOString() : selectedLead.lastModifiedDate || '',

        // Поля из details
        details_city: selectedLead.details?.city || '',
        details_role: selectedLead.details?.role || '',
        details_tags: selectedLead.details?.tags || '',
        details_export: selectedLead.details?.export || '',
        details_mailing: selectedLead.details?.mailing || '',
        details_language: selectedLead.details?.language || '',
        details_products: selectedLead.details?.products || '',
        details_projects: selectedLead.details?.projects || '',
        details_bitrix_id: selectedLead.details?.bitrix_id || '',
        details_commented: selectedLead.details?.commented || '',
        details_birth_date: selectedLead.details?.birth_date || '',
        details_tech_field: selectedLead.details?.tech_field || '',
        details_middle_name: selectedLead.details?.middle_name || '',
        details_contact_type: selectedLead.details?.contact_type || '',
        details_linkedin_info: selectedLead.details?.linkedin_info?.content || '',
        details_facebook_info: selectedLead.details?.facebook_info?.content || '',
        details_company_info: selectedLead.details?.company_info?.content || '',

        // Поля из communications
        communications_facebook: selectedLead.communications?.facebook || '',
        communications_linkedin: selectedLead.communications?.linkedin || '',
        communications_telegram: selectedLead.communications?.telegram || '',
        communications_vkontakte: selectedLead.communications?.vkontakte || '',
        communications_last_activity: selectedLead.communications?.last_activity || '',
        communications_other_contact: selectedLead.communications?.other_contact || '',
        communications_other_website: selectedLead.communications?.other_website || '',
        communications_contact_history: selectedLead.communications?.contact_history || '',

        // Поля из company_info
        company_name: selectedLead.company_info?.name || '',
        company_website: selectedLead.company_info?.website || '',
        company_industry: selectedLead.company_info?.industry || '',

        // Сохраняем существующие форматированные поля для обратной совместимости
        lead_details: `Role: ${selectedLead.title || 'N/A'}\nStatus: ${selectedLead.status || 'N/A'}\nSource: ${selectedLead.leadSource || 'N/A'}`,
        lead_history: selectedLead.history ? JSON.stringify(selectedLead.history) : 'No history available',
        company_info: `Industry: ${selectedLead.industry || 'N/A'}\nSize: ${selectedLead.numberOfEmployees || 'N/A'}\nWebsite: ${selectedLead.website || 'N/A'}`
      };

      console.log(`[UI] Testing prompt with model: ${settings.model}`);
      addStatusLog("request", `Отправка запроса на генерацию с моделью ${settings.model}`);
      setLoadingStatus("Генерация email с помощью AI...");
      
      // Добавляем обработку ответа для извлечения testId
      const response = await fetch('/api/v1/prompts/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content,
          variables,
          settings
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        addStatusLog("error", "Ошибка при запуске теста", false, errorData.error || "Неизвестная ошибка");
        throw new Error(errorData.error || "Failed to start prompt test");
      }
      
      const responseData = await response.json();
      
      // Обрабатываем случай с прямым ответом или фоновой обработкой
      if (responseData.status === 'processing') {
        // Получен testId для последующего опроса
        const newTestId = responseData.testId;
        addStatusLog("processing", `Тест запущен в фоновом режиме с ID: ${newTestId}`, true);
        setTestId(newTestId);
        setLoadingStatus(`Обработка в фоновом режиме... (ID: ${newTestId.substring(0, 8)})`);
        
        // Опрашиваем статус результата, пока он не будет готов
        let attempts = 0;
        const maxAttempts = 30; // Максимальное количество попыток
        const pollInterval = 2000; // Интервал между запросами в мс
        
        while (attempts < maxAttempts) {
          attempts++;
          try {
            const pollResponse = await fetch(`/api/v1/prompts/test-callback?id=${newTestId}${debugMode ? '&test=true' : ''}`);
            if (!pollResponse.ok) continue;
            
            const pollData = await pollResponse.json();
            if (pollData.status === 'completed' && pollData.result) {
              addStatusLog("success", "Получен результат из фоновой обработки", true);
              setResult(pollData.result);
              break;
            }
            
            if (pollData.status === 'error') {
              throw new Error(pollData.error || "Error in background processing");
            }
            
            // Ждем перед следующей попыткой
            await new Promise(resolve => setTimeout(resolve, pollInterval));
            
          } catch (error) {
            console.error("[UI] Error polling for results:", error);
            // Продолжаем попытки опроса, даже если произошла ошибка
          }
        }
        
        if (attempts >= maxAttempts) {
          addStatusLog("timeout", "Превышено время ожидания результата", false);
          throw new Error("Timeout waiting for test results");
        }
      } else if (responseData.status === 'completed') {
        // Получен прямой результат
        addStatusLog("success", "Получен прямой результат", true);
        setResult(responseData.result);
      } else {
        // Неизвестный статус
        addStatusLog("error", `Неизвестный статус: ${responseData.status}`, false);
        throw new Error(`Unknown status: ${responseData.status}`);
      }
      
      console.log('[UI] Prompt test completed successfully');
    } catch (error) {
      console.error("[UI] Error testing prompt:", error);
      
      // Особая обработка для ошибок таймаута
      if (error instanceof Error && error.message.includes('Timeout waiting for test results')) {
        addStatusLog("timeout", "Превышено время ожидания", false, error.message);
        toast({
          variant: "destructive",
          title: "Превышено время ожидания",
          description: "Генерация заняла слишком много времени. Результат может быть доступен позже - попробуйте повторить тестирование через некоторое время. Также можно использовать более короткий промпт или другую модель."
        });
      } else {
        addStatusLog("error", "Ошибка при тестировании", false, error instanceof Error ? error.message : "Неизвестная ошибка");
        toast({
          variant: "destructive",
          title: "Error testing prompt",
          description: error instanceof Error ? error.message : "An unexpected error occurred"
        });
      }
    } finally {
      if (processingTimer) {
        clearInterval(processingTimer);
        setProcessingTimer(null);
      }
      setIsLoading(false);
      setLoadingStatus("");
    }
  };

  // Форматирование времени выполнения
  const formatProcessingTime = () => {
    if (processingTime < 60) {
      return `${processingTime} секунд`;
    } else {
      const minutes = Math.floor(processingTime / 60);
      const seconds = processingTime % 60;
      return `${minutes} мин ${seconds} сек`;
    }
  };

  // Функция для ручной проверки результата по ID
  const handleCheckResultByTestId = async () => {
    if (!testId) return;
    
    try {
      setIsLoading(true);
      addStatusLog("manual_check", `Ручная проверка результата по ID: ${testId}`);
      
      const response = await fetch(`/api/v1/prompts/test-callback?id=${testId}${debugMode ? '&test=true' : ''}`);
      
      if (!response.ok) {
        addStatusLog("error", `Ошибка получения результата: ${response.status}`, false);
        throw new Error(`Failed to get result: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.status === 'pending') {
        addStatusLog("pending", "Результат еще не готов, ожидайте", true);
        toast({
          title: "Результат не найден",
          description: "Тест ещё обрабатывается. Пожалуйста, подождите и попробуйте проверить позже."
        });
      } else if (data.success && data.result) {
        addStatusLog("success", "Результат успешно получен", true);
        setResult(data.result);
        toast({
          title: "Результат найден",
          description: "Результат теста успешно загружен!",
          variant: "default"
        });
      } else {
        addStatusLog("error", "Некорректный формат результата", false);
        toast({
          variant: "destructive",
          title: "Ошибка",
          description: data.error || "Некорректный формат результата"
        });
      }
    } catch (error) {
      console.error("[UI] Error checking result:", error);
      addStatusLog("error", "Ошибка при проверке результата", false, error instanceof Error ? error.message : "Неизвестная ошибка");
      toast({
        variant: "destructive",
        title: "Ошибка при проверке результата",
        description: error instanceof Error ? error.message : "Неизвестная ошибка"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <h3 className="text-lg font-semibold mb-2">Тестирование промпта</h3>
        </div>
        <div className="flex items-center space-x-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="debug-mode"
                    checked={debugMode}
                    onCheckedChange={setDebugMode}
                  />
                  <Label htmlFor="debug-mode" className="cursor-pointer">
                    <Bug size={16} className="inline mr-1" />
                    Режим отладки
                  </Label>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>В режиме отладки будет использоваться тестовый результат без вызова AI</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <div className="flex flex-col space-y-2">
        <label className="text-sm font-medium">Содержание промпта</label>
        <Textarea
          value={content}
          readOnly
          className="h-[200px] min-h-[200px] bg-muted"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="flex flex-col space-y-2">
          <label className="text-sm font-medium">Модель</label>
          <Select
            value={settings.model}
            onValueChange={(value) => setSettings(prev => ({ ...prev, model: value }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="anthropic/claude-3.5-sonnet">Claude 3.5 Sonnet</SelectItem>
              <SelectItem value="anthropic/claude-3-5-haiku">Claude 3.5 Haiku</SelectItem>
              <SelectItem value="anthropic/claude-3.7-sonnet">Claude 3.7 Sonnet</SelectItem>
              <SelectItem value="anthropic/claude-3.7-sonnet:online">Claude 3.7 Sonnet (Online)</SelectItem>
              <SelectItem value="anthropic/claude-3.7-sonnet:thinking">Claude 3.7 Sonnet (Thinking)</SelectItem>
              <SelectItem value="anthropic/claude-3.7-sonnet:online:thinking">Claude 3.7 Sonnet (Online Thinking)</SelectItem>
              <SelectItem disabled value="divider-deepsearch">---------------</SelectItem>

              <SelectItem value="perplexity/sonar-deep-research">Sonar Deep Research</SelectItem>
              <SelectItem disabled value="divider-openai">---------------</SelectItem>
              
              <SelectItem value="openai/gpt-4o-search-preview">GPT-4o Search Preview</SelectItem>
              <SelectItem value="openai/gpt-4o-mini-search-preview">GPT-4o Mini Search Preview</SelectItem>
              <SelectItem value="openai/gpt-4o-2024-11-20:online">GPT-4o (2024-11-20 Online)</SelectItem>
              <SelectItem value="openai/gpt-4o-2024-11-20">GPT-4o (2024-11-20)</SelectItem>
              <SelectItem value="openai/gpt-4o-2024-08-06">GPT-4o (2024-08-06)</SelectItem>
              <SelectItem value="openai/gpt-4o-2024-05-13">GPT-4o (2024-05-13)</SelectItem>
              <SelectItem value="openai/gpt-4o-mini">GPT-4o Mini</SelectItem>
              <SelectItem value="openai/gpt-4-turbo">GPT-4 Turbo</SelectItem>
              <SelectItem value="openai/gpt-4">GPT-4</SelectItem>
              <SelectItem value="openai/gpt-4.1">GPT-4.1</SelectItem>
              <SelectItem value="openai/gpt-4.1-mini">GPT-4.1 Mini</SelectItem>
              <SelectItem value="openai/gpt-4.1-nano">GPT-4.1 Nano</SelectItem>
              <SelectItem value="openai/gpt-4.5-preview">GPT-4.5 Preview</SelectItem>
              <SelectItem value="openai/gpt-4.5-preview:online">GPT-4.5 Preview (Online)</SelectItem>
              <SelectItem value="openai/gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
              <SelectItem value="openai/o1">o1</SelectItem>
              <SelectItem value="openai/o1:online">o1 (Online)</SelectItem>
              <SelectItem value="openai/o1-mini">o1 Mini</SelectItem>
              <SelectItem value="openai/o3-mini-high">o3 Mini High</SelectItem>
              <SelectItem value="openai/o3-mini">o3 Mini</SelectItem>
              <SelectItem value="openai/o1-preview">o1 Preview</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex flex-col space-y-2">
          <label className="text-sm font-medium">Temperature ({settings.temperature})</label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={settings.temperature}
            onChange={(e) => setSettings(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
            className="w-full"
          />
        </div>

        <div className="flex flex-col space-y-2">
          <label className="text-sm font-medium">Max Tokens</label>
          <input
            type="number"
            value={settings.maxTokens}
            onChange={(e) => setSettings(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
            min="1"
            max="4096"
            className="w-full p-2 border rounded"
          />
        </div>
      </div>

      <div className="flex flex-col space-y-2">
        <label className="text-sm font-medium">Выбор лида</label>
        <Select
          value={selectedLead?.id.toString()}
          onValueChange={(value) => {
            const lead = leads.find((l) => l.id.toString() === value);
            if (lead) {
              setSelectedLead(lead);
            }
          }}
          disabled={isLeadsLoading}
        >
          <SelectTrigger>
            <SelectValue placeholder={isLeadsLoading ? "Loading leads..." : "Select a lead"}>
              {selectedLead ? `${selectedLead.firstName || ''} ${selectedLead.lastName || ''} - ${selectedLead.company}` : 
               isLeadsLoading ? "Loading leads..." : "Select a lead"}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {isLeadsLoading ? (
              <SelectItem value="loading" disabled>Loading leads...</SelectItem>
            ) : leads.map((lead) => (
              <SelectItem key={lead.id} value={lead.id.toString()}>
                {`${lead.firstName || ''} ${lead.lastName || ''} - ${lead.company}`}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div className="grid grid-cols-1 gap-2">
        <Button 
          onClick={handleTestPrompt} 
          disabled={!selectedLead || isLoading || isLeadsLoading}
          className="w-full"
        >
          {isLoading ? (
            <span className="flex items-center">
              <Loader2 size={16} className="mr-2 animate-spin" />
              {loadingStatus || "Тестирование..."}
            </span>
          ) : (
            <span className="flex items-center">
              <RefreshCw size={16} className="mr-2" />
              Тестировать промпт
            </span>
          )}
        </Button>
        
        {debugMode && (
          <div className="flex space-x-2 mt-2">
            <div className="flex-1">
              <Input
                placeholder="Введите ID теста для проверки"
                value={testId}
                onChange={e => setTestId(e.target.value)}
                disabled={isLoading}
              />
            </div>
            <Button 
              onClick={handleCheckResultByTestId} 
              disabled={!testId || isLoading}
              variant="outline"
            >
              <Clock size={16} className="mr-2" />
              Проверить
            </Button>
          </div>
        )}
      </div>

      {isLoading && (
        <div className="flex flex-col items-center justify-center p-4 space-y-2 border rounded-md bg-muted/20">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="text-sm text-muted-foreground">{loadingStatus}</p>
          <p className="text-xs text-muted-foreground">Время выполнения: {formatProcessingTime()}</p>
          <p className="text-xs text-muted-foreground">
            {processingTime > 60 
              ? "Генерация может занять до 2 минут для больших промптов..." 
              : "Это может занять некоторое время..."}
          </p>
        </div>
      )}
      
      {statusLogs.length > 0 && (
        <div className="space-y-2 mt-2 border p-3 rounded-md bg-muted/10">
          <div className="flex justify-between items-center">
            <h4 className="text-sm font-medium">Лог операций</h4>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setStatusLogs([])}
            >
              Очистить
            </Button>
          </div>
          <div className="space-y-1 max-h-40 overflow-y-auto text-xs">
            {statusLogs.map((log, index) => (
              <div key={index} className="flex items-start space-x-2 py-1 border-b border-dashed border-muted last:border-0">
                <span className="mt-0.5">
                  {log.success === true ? (
                    <Check size={14} className="text-green-500" />
                  ) : log.success === false ? (
                    <AlertCircle size={14} className="text-red-500" />
                  ) : (
                    <Info size={14} className="text-blue-500" />
                  )}
                </span>
                <div className="flex-1">
                  <div className="flex justify-between">
                    <span className="font-medium">{log.step}</span>
                    <span className="text-muted-foreground text-[10px]">
                      {log.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                  <p>{log.message}</p>
                  {log.error && <p className="text-red-500">{log.error}</p>}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {testId && !isLoading && (
        <div className="flex items-center space-x-2 text-sm">
          <span className="font-medium">ID теста:</span>
          <Badge variant="outline" className="font-mono">
            {testId}
          </Badge>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => {
              navigator.clipboard.writeText(testId);
              toast({
                title: "ID скопирован",
                description: "ID теста скопирован в буфер обмена"
              });
            }}
          >
            <Save size={14} className="mr-1" />
            Копировать
          </Button>
        </div>
      )}

      {result && (
        <div className="space-y-4 mt-4">
           <Separator />
            <div className="space-y-2">
            <label className="text-sm font-medium">Formatted Prompt (Полный промпт с подставленными переменными)</label>
            <Textarea
              value={result.formattedPrompt}
              readOnly
              className="h-[300px] min-h-[300px] bg-muted whitespace-pre-wrap font-mono text-sm"
            />
          </div>
          <Separator />
          <h3 className="text-lg font-semibold">Generated Email</h3>
          <div className="space-y-2">
            <label className="text-sm font-medium">Subject</label>
            <Textarea
              value={result.subject}
              readOnly
              className="h-[40px] min-h-[40px] bg-muted"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Body</label>
            <Textarea
              value={result.body}
              readOnly
              className="h-[400px] min-h-[400px] bg-muted whitespace-pre-wrap"
            />
          </div>
        </div>
      )}
    </div>
  );
}

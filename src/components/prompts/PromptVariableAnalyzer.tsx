import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";

interface PromptVariableAnalyzerProps {
  content: string;
  onVariablesChange: (variables: string[]) => void;
}

export function PromptVariableAnalyzer({
  content,
  onVariablesChange,
}: PromptVariableAnalyzerProps) {
  const [variables, setVariables] = useState<string[]>([]);
  const [newVariable, setNewVariable] = useState("");

  useEffect(() => {
    // Анализируем промпт на наличие переменных в формате {{variable}}
    const matches = content.match(/{{([^}]+)}}/g);
    if (matches) {
      const extractedVariables = matches.map((match) =>
        match.replace(/[{}]/g, "")
      );
      setVariables(Array.from(new Set(extractedVariables)));
      onVariablesChange(Array.from(new Set(extractedVariables)));
    } else {
      setVariables([]);
      onVariablesChange([]);
    }
  }, [content, onVariablesChange]);

  const addVariable = () => {
    if (newVariable && !variables.includes(newVariable)) {
      const updatedVariables = [...variables, newVariable];
      setVariables(updatedVariables);
      onVariablesChange(updatedVariables);
      setNewVariable("");
    }
  };

  const removeVariable = (variable: string) => {
    const updatedVariables = variables.filter((v) => v !== variable);
    setVariables(updatedVariables);
    onVariablesChange(updatedVariables);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-end gap-2">
        <div className="flex-1">
          <Label>Добавить переменную</Label>
          <Input
            value={newVariable}
            onChange={(e) => setNewVariable(e.target.value)}
            placeholder="Имя переменной"
          />
        </div>
        <Button onClick={addVariable} disabled={!newVariable}>
          Добавить
        </Button>
      </div>

      <div>
        <Label>Найденные переменные</Label>
        <ScrollArea className="h-[100px] w-full border rounded-md p-2">
          <div className="flex flex-wrap gap-2">
            {variables.map((variable) => (
              <Badge
                key={variable}
                variant="secondary"
                className="cursor-pointer"
                onClick={() => removeVariable(variable)}
              >
                {variable} ×
              </Badge>
            ))}
            {variables.length === 0 && (
              <span className="text-sm text-gray-500">
                Нет найденных переменных
              </span>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}

"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { Switch } from "@/components/ui/switch";
import { promptTypes, type Prompt } from "@/db/schema/prompts";
import { PromptTester } from "./PromptTester";
import { useTestPrompt } from "@/hooks/use-prompts";
import { PromptVariableHighlighter } from "./PromptVariableHighlighter";

// Стили для предпросмотра
const previewStyles = `
  .hljs-preview {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, <PERSON><PERSON><PERSON>, "Liberation Mono", "Courier New", monospace;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-break: break-word;
    padding: 0.5rem;
    border-radius: 0.375rem;
    min-height: 200px;
    margin-top: 0.5rem;
    border: 1px solid #e5e7eb;
    overflow: auto;
    background-color: #ffffff;
  }
  
  .dark .hljs-preview {
    background-color: #1e293b;
    border-color: #334155;
  }
`;

interface PromptEditorProps {
  prompt?: Prompt;
  mode: "create" | "edit";
  onSave?: (prompt: Partial<Prompt>) => Promise<void>;
  onCancel?: () => void;
}

export function PromptEditor({ prompt, mode, onSave, onCancel }: PromptEditorProps) {
  const [editData, setEditData] = useState<Partial<Prompt>>({
    title: prompt?.title || "",
    type: prompt?.type || promptTypes.SYSTEM,
    content: prompt?.content || "",
    description: prompt?.description || "",
    isActive: prompt?.isActive ?? true,
    settings: prompt?.settings || {},
    metadata: prompt?.metadata || {}
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const { toast } = useToast();
  const testPrompt = useTestPrompt();

  const handleSave = async () => {
    try {
      setIsLoading(true);
      if (onSave) {
        await onSave(editData);
      }
      toast({
        title: "Success",
        description: "Prompt saved successfully",
      });
    } catch (error) {
      console.error("Error saving prompt:", error);
      toast({
        title: "Error",
        description: "Failed to save prompt",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTest = async (content: string, variables: Record<string, string>, settings: any) => {
    try {
      const result = await testPrompt.mutateAsync({
        content,
        settings: settings || {
          model: "claude-3-opus-20240229",
          temperature: 0.7,
          maxTokens: 2048
        },
        variables
      });
      
      toast({
        title: "Success",
        description: "Prompt tested successfully",
      });

      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to test prompt",
        variant: "destructive",
      });
      throw error;
    }
  };

  // Функция для обновления превью с подсветкой
  const updatePreview = (text: string) => {
    // Используем теперь не нужно вызывать highlight напрямую,
    // так как это делает компонент PromptVariableHighlighter
  };

  // Обрабатываем изменение текста
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setEditData({ ...editData, content: newValue });
  };

  // Инициализация подсветки при загрузке
  useEffect(() => {
    updatePreview(editData.content || "");
  }, []);

  // Функция для вставки переменной в текст
  const insertVariable = (variable: string) => {
    const textarea = document.getElementById("content") as HTMLTextAreaElement;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const currentContent = editData.content || "";
      const newContent = 
        currentContent.substring(0, start) + 
        `{{${variable}}}` + 
        currentContent.substring(end);
      
      setEditData({ ...editData, content: newContent });
      updatePreview(newContent);
      
      // Устанавливаем курсор после вставленной переменной
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + variable.length + 4, start + variable.length + 4);
      }, 0);
    }
  };

  return (
    <div className="space-y-4">
      <style dangerouslySetInnerHTML={{ __html: previewStyles }} />
      <Tabs defaultValue="edit" className="w-full">
        <TabsList className="w-full">
          <TabsTrigger value="edit" className="flex-1">
            {mode === "edit" ? "Edit Prompt" : "Create Prompt"}
          </TabsTrigger>
          <TabsTrigger value="test" className="flex-1">Test Prompt</TabsTrigger>
        </TabsList>

        <TabsContent value="edit" className="space-y-4 mt-4">
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={editData.title}
                onChange={(e) => setEditData({ ...editData, title: e.target.value })}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={editData.description!}
                onChange={(e) => setEditData({ ...editData, description: e.target.value })}
                className="h-20"
              />
            </div>

            <div className="grid gap-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="content">Prompt Content</Label>
                <div className="flex items-center">
                  <Switch 
                    id="preview" 
                    checked={showPreview} 
                    onCheckedChange={setShowPreview}
                    className="mr-2"
                  />
                  <Label htmlFor="preview" className="text-sm cursor-pointer">
                    {showPreview ? "Подсветка переменных" : "Редактирование"}
                  </Label>
                </div>
              </div>
              
              {!showPreview ? (
                <Textarea
                  id="content"
                  value={editData.content}
                  onChange={handleChange}
                  className="h-[300px] font-mono"
                />
              ) : (
                <div className="hljs-preview">
                  <PromptVariableHighlighter text={editData.content || ''} />
                </div>
              )}
              
              {/* Легенда с цветами переменных */}
              <div className="flex flex-wrap mt-2 text-xs text-muted-foreground">
                <div className="legend-item">
                  <span className="legend-swatch" style={{ backgroundColor: 'rgba(37, 99, 235, 0.1)', border: '1px solid rgba(37, 99, 235, 0.2)' }}></span>
                  <span>Основные</span>
                </div>
                <div className="legend-item">
                  <span className="legend-swatch" style={{ backgroundColor: 'rgba(8, 145, 178, 0.1)', border: '1px solid rgba(8, 145, 178, 0.2)' }}></span>
                  <span>details_*</span>
                </div>
                <div className="legend-item">
                  <span className="legend-swatch" style={{ backgroundColor: 'rgba(124, 58, 237, 0.1)', border: '1px solid rgba(124, 58, 237, 0.2)' }}></span>
                  <span>communications_*</span>
                </div>
                <div className="legend-item">
                  <span className="legend-swatch" style={{ backgroundColor: 'rgba(5, 150, 105, 0.1)', border: '1px solid rgba(5, 150, 105, 0.2)' }}></span>
                  <span>company_*</span>
                </div>
              </div>
              
              {/* Панель для вставки переменных */}
              <div className="variable-insertion py-3 border rounded-md mt-2">
                <h3 className="text-sm font-medium px-3 mb-2">Вставить переменную:</h3>
                <div className="px-3 flex flex-wrap gap-1">
                  <Button variant="outline" size="sm" onClick={() => insertVariable("leadFirstName")}>leadFirstName</Button>
                  <Button variant="outline" size="sm" onClick={() => insertVariable("leadLastName")}>leadLastName</Button>
                  <Button variant="outline" size="sm" onClick={() => insertVariable("leadEmail")}>leadEmail</Button>
                  <Button variant="outline" size="sm" onClick={() => insertVariable("leadCompany")}>leadCompany</Button>
                  <Button variant="outline" size="sm" onClick={() => insertVariable("senderName")}>senderName</Button>
                </div>
                <div className="border-t mt-2 pt-2 px-3">
                  <h4 className="text-sm font-medium mb-1">Поля details:</h4>
                  <div className="flex flex-wrap gap-1">
                    <Button variant="outline" size="sm" onClick={() => insertVariable("details_city")}>details_city</Button>
                    <Button variant="outline" size="sm" onClick={() => insertVariable("details_role")}>details_role</Button>
                    <Button variant="outline" size="sm" onClick={() => insertVariable("details_language")}>details_language</Button>
                  </div>
                </div>
                <div className="border-t mt-2 pt-2 px-3">
                  <h4 className="text-sm font-medium mb-1">Поля communications:</h4>
                  <div className="flex flex-wrap gap-1">
                    <Button variant="outline" size="sm" onClick={() => insertVariable("communications_linkedin")}>communications_linkedin</Button>
                    <Button variant="outline" size="sm" onClick={() => insertVariable("communications_telegram")}>communications_telegram</Button>
                  </div>
                </div>
                <div className="border-t mt-2 pt-2 px-3">
                  <h4 className="text-sm font-medium mb-1">Поля company:</h4>
                  <div className="flex flex-wrap gap-1">
                    <Button variant="outline" size="sm" onClick={() => insertVariable("company_name")}>company_name</Button>
                    <Button variant="outline" size="sm" onClick={() => insertVariable("company_website")}>company_website</Button>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={editData.isActive}
                  onCheckedChange={(checked) => setEditData({ ...editData, isActive: checked })}
                />
                <Label htmlFor="active">Active</Label>
              </div>
              {prompt && (
                <div className="text-sm text-muted-foreground">
                  Version: {prompt.version}
                </div>
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            {onCancel && (
              <Button variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button onClick={handleSave} disabled={isLoading}>
              {isLoading ? "Saving..." : mode === "edit" ? "Save Changes" : "Create Prompt"}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="test" className="space-y-4 mt-4">
          <PromptTester onTest={handleTest} content={editData.content || ""} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

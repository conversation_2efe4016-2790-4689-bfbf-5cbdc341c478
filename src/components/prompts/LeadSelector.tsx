import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Lead } from "@/db/schema/leads";

interface LeadSelectorProps {
  onLeadSelect: (lead: Lead) => void;
}

interface LeadsResponse {
  leads: Lead[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export function LeadSelector({ onLeadSelect }: LeadSelectorProps) {
  const [selectedLeadId, setSelectedLeadId] = useState<string>("");

  const { data, isLoading } = useQuery<LeadsResponse>({
    queryKey: ["leads"],
    queryFn: async () => {
      const response = await fetch("/api/leads");
      if (!response.ok) {
        throw new Error("Failed to fetch leads");
      }
      return response.json();
    },
  });

  const handleLeadChange = (leadId: string) => {
    setSelectedLeadId(leadId);
    const lead = data?.leads.find((l) => l.id === parseInt(leadId, 10));
    if (lead) {
      onLeadSelect(lead);
    }
  };

  if (isLoading) {
    return <div>Loading leads...</div>;
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-2">
          <Label>Select Lead</Label>
          <Select value={selectedLeadId} onValueChange={handleLeadChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select a lead" />
            </SelectTrigger>
            <SelectContent>
              {data?.leads.map((lead) => (
                <SelectItem key={lead.id} value={lead.id.toString()}>
                  {lead.firstName} {lead.lastName} - {lead.email}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  );
}

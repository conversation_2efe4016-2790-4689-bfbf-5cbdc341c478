"use client";

import React from 'react';

// Стили для подсветки
const highlightStyles = `
  .variable {
    border-radius: 3px;
    padding: 1px 2px;
    margin: 0 1px;
    display: inline-block;
  }
  
  .basic-var {
    color: #2563eb;
    background-color: rgba(37, 99, 235, 0.1);
    border: 1px solid rgba(37, 99, 235, 0.2);
  }
  
  .details-var {
    color: #0891b2;
    background-color: rgba(8, 145, 178, 0.1);
    border: 1px solid rgba(8, 145, 178, 0.2);
  }
  
  .communications-var {
    color: #7c3aed;
    background-color: rgba(124, 58, 237, 0.1);
    border: 1px solid rgba(124, 58, 237, 0.2);
  }
  
  .company-var {
    color: #059669;
    background-color: rgba(5, 150, 105, 0.1);
    border: 1px solid rgba(5, 150, 105, 0.2);
  }
  
  .other-var {
    color: #d97706;
    background-color: rgba(217, 119, 6, 0.1);
    border: 1px solid rgba(217, 119, 6, 0.2);
  }
`;

// Регулярные выражения для разных типов переменных
const basicVarRegex = /{{(leadFirstName|leadLastName|leadCompany|leadEmail|lead_details|lead_history|company_info|senderName)}}/g;
const detailsVarRegex = /{{(details_[a-zA-Z0-9_]+)}}/g;
const communicationsVarRegex = /{{(communications_[a-zA-Z0-9_]+)}}/g;
const companyVarRegex = /{{(company_[a-zA-Z0-9_]+)}}/g;
const otherVarRegex = /{{([a-zA-Z0-9_]+)}}/g;

// Функция для экранирования HTML
function escapeHtml(text: string) {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

// Функция для подсветки переменных в тексте
function highlightVariablesInText(text: string): string {
  // Сначала экранируем HTML
  let escapedText = escapeHtml(text);
  
  // Заменяем переменные на HTML с нужными классами
  escapedText = escapedText
    // Сначала заменяем основные переменные
    .replace(basicVarRegex, '<span class="variable basic-var">$&</span>')
    // Затем переменные details
    .replace(detailsVarRegex, '<span class="variable details-var">$&</span>')
    // Затем переменные communications
    .replace(communicationsVarRegex, '<span class="variable communications-var">$&</span>')
    // Затем переменные company
    .replace(companyVarRegex, '<span class="variable company-var">$&</span>')
    // И наконец, все остальные переменные
    .replace(otherVarRegex, (match) => {
      // Проверяем, была ли переменная уже обработана
      if (
        match.includes('class="variable')
      ) {
        return match;
      }
      return `<span class="variable other-var">${match}</span>`;
    });
  
  return escapedText;
}

interface PromptVariableHighlighterProps {
  text: string;
  className?: string;
}

export function PromptVariableHighlighter({ text, className = '' }: PromptVariableHighlighterProps) {
  // Подсвечиваем переменные
  const highlightedText = highlightVariablesInText(text);
  
  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: highlightStyles }} />
      <div
        className={`whitespace-pre-wrap ${className}`}
        dangerouslySetInnerHTML={{ __html: highlightedText }}
      />
    </>
  );
}

// Компонент-обертка для автоматического применения подсветки к строкам с переменными
export function HighlightVariables({ children }: { children: React.ReactNode }) {
  if (typeof children !== 'string') {
    return <>{children}</>;
  }

  // Проверяем, содержит ли текст переменные в формате {{variable}}
  if (!children.includes('{{')) {
    return <>{children}</>;
  }

  return <PromptVariableHighlighter text={children} />;
} 
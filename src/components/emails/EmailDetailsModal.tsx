"use client";

import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { useQuery } from "@tanstack/react-query";

interface EmailDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  emailId: number | null;
}

export function EmailDetailsModal({ isOpen, onClose, emailId }: EmailDetailsModalProps) {
  const { data: email, isLoading } = useQuery({
    queryKey: ["email", emailId],
    queryFn: async () => {
      if (!emailId) return null;
      const response = await fetch(`/api/emails/${emailId}`);
      if (!response.ok) throw new Error("Failed to fetch email");
      return response.json();
    },
    enabled: !!emailId,
  });

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Email Details</DialogTitle>
        </DialogHeader>

        <div className="max-h-[80vh] overflow-hidden flex flex-col">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
            </div>
          ) : email ? (
            <div className="space-y-6 overflow-y-auto pr-2">
              <div>
                <h3 className="text-lg font-medium sticky top-0 bg-white py-2">Generated Email</h3>
                <div className="mt-2 space-y-2">
                  <div className="font-medium">Subject:</div>
                  <div className="p-3 bg-gray-50 rounded-md">{email.subject}</div>
                  <div className="font-medium">Content:</div>
                  <div className="p-3 bg-gray-50 rounded-md whitespace-pre-wrap">
                    {email.content}
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium sticky top-0 bg-white py-2">Prompt Used</h3>
                <div className="mt-2 p-3 bg-gray-50 rounded-md whitespace-pre-wrap">
                  {email.prompt}
                </div>
              </div>
            </div>
          ) : null}
        </div>
      </DialogContent>
    </Dialog>
  );
}

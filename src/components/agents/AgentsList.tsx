'use client';

import { Agent } from '@/types/agent';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Edit2, Trash2, Co<PERSON>, Check } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useState } from 'react';
import { useToast } from '@/hooks/useToast';

interface AgentsListProps {
  agents: Agent[];
  onEdit: (agent: Agent) => void;
  onDelete: (agent: Agent) => void;
  isLoading?: boolean;
}

export function AgentsList({ agents, onEdit, onDelete, isLoading = false }: AgentsListProps) {
  const { toast } = useToast();
  const [copiedId, setCopiedId] = useState<string | null>(null);

  const handleCopyUuid = (uuid: string) => {
    if (!uuid) return;
    
    navigator.clipboard.writeText(uuid);
    setCopiedId(uuid);
    
    toast({
      title: "UUID скопирован",
      description: "UUID агента скопирован в буфер обмена",
    });
    
    setTimeout(() => {
      setCopiedId(null);
    }, 2000);
  };

  if (!agents?.length) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No agents found</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {agents.map((agent) => (
        <Card key={agent.id} className="flex flex-col">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl">{agent.name}</CardTitle>
              <Badge variant={agent.isActive ? 'default' : 'secondary'}>
                {agent.isActive ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            <CardDescription className="mt-2">
              {agent.description || 'No description'}
            </CardDescription>
          </CardHeader>
          <CardContent className="flex-grow">
            <div className="space-y-2">
              {agent.uuid && (
                <div className="border rounded-md p-2 bg-muted/30">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-muted-foreground">API ID:</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2"
                      onClick={() => handleCopyUuid(agent.uuid!)}
                    >
                      {copiedId === agent.uuid ? (
                        <Check className="h-3.5 w-3.5 text-green-500" />
                      ) : (
                        <Copy className="h-3.5 w-3.5" />
                      )}
                    </Button>
                  </div>
                  <code className="text-xs font-mono block truncate">
                    {agent.uuid}
                  </code>
                </div>
              )}
              <div>
                <span className="text-sm font-medium">Provider:</span>
                <span className="ml-2 text-sm text-gray-500">
                  {agent.settings.llmProvider}
                </span>
              </div>
              <div>
                <span className="text-sm font-medium">Model:</span>
                <span className="ml-2 text-sm text-gray-500">
                  {agent.settings.model}
                </span>
              </div>
              <div>
                <span className="text-sm font-medium">Version:</span>
                <span className="ml-2 text-sm text-gray-500">
                  {agent.version}
                </span>
              </div>
              <div>
                <span className="text-sm font-medium">Updated:</span>
                <span className="ml-2 text-sm text-gray-500">
                  {formatDistanceToNow(new Date(agent.updatedAt), { addSuffix: true })}
                </span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(agent)}
              disabled={isLoading}
            >
              <Edit2 className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => onDelete(agent)}
              disabled={isLoading}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}

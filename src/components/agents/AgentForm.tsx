'use client';

import { useState, useEffect } from 'react';
import { Agent, CreateAgentInput, LLMModel } from '@/types/agent';
import { Prompt } from '@/db/schema/prompts';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Copy, Check } from 'lucide-react';

interface AgentFormProps {
  initialData?: Partial<Agent>;
  onSubmit: (data: CreateAgentInput) => Promise<void>;
  onDelete?: () => Promise<void>;
  isLoading?: boolean;
}

export function AgentForm({ initialData, onSubmit, onDelete, isLoading = false }: AgentFormProps) {
  const { toast } = useToast();
  
  // Функция для преобразования сохраненной модели в новый формат с префиксом
  const normalizeModelId = (model: string, provider: string): LLMModel => {
    // Если модель уже содержит префикс, вернуть как есть
    if (model.includes('/')) {
      return model as LLMModel;
    }
    // Иначе добавить соответствующий префикс
    return `${provider}/${model}` as LLMModel;
  };
  
  // Обработка начальных данных
  const initialSettings = initialData?.settings || {
    llmProvider: 'anthropic',
    model: 'anthropic/claude-3-5-haiku-20241022' as LLMModel,
    temperature: 0.7,
    maxTokens: 2000,
  };
  
  // Нормализация модели, если необходимо
  if (initialData?.settings?.model) {
    initialSettings.model = normalizeModelId(
      initialData.settings.model,
      initialData.settings.llmProvider
    );
  }
  
  const [formData, setFormData] = useState<CreateAgentInput>({
    name: initialData?.name || '',
    description: initialData?.description || '',
    prompt: initialData?.prompt || '',
    promptId: initialData?.promptId,
    settings: initialSettings,
    isActive: initialData?.isActive ?? true,
  });

  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [copiedUuid, setCopiedUuid] = useState(false);

  // Добавляем отслеживание того, что maxTokens был изменен пользователем
  const [userModifiedMaxTokens, setUserModifiedMaxTokens] = useState(false);

  useEffect(() => {
    fetchPrompts();
  }, []);

  const fetchPrompts = async () => {
    try {
      const response = await fetch('/api/v1/prompts');
      if (!response.ok) throw new Error('Failed to fetch prompts');
      const data = await response.json();
      setPrompts(data.prompts || []);
    } catch (error) {
      console.error('Error fetching prompts:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch prompts',
        variant: 'destructive',
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await onSubmit(formData);
      toast({
        title: 'Success',
        description: `Agent ${initialData ? 'updated' : 'created'} successfully`,
      });
    } catch (error) {
      console.error('Form submission error:', error);
      toast({
        title: 'Error',
        description: 'Failed to save agent. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleDelete = async () => {
    if (!onDelete) return;
    try {
      await onDelete();
      toast({
        title: 'Success',
        description: 'Agent deleted successfully',
      });
    } catch (error) {
      console.error('Delete error:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete agent. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleCopyUuid = () => {
    if (!initialData?.uuid) return;
    
    navigator.clipboard.writeText(initialData.uuid);
    setCopiedUuid(true);
    
    toast({
      title: "UUID скопирован",
      description: "UUID агента скопирован в буфер обмена",
    });
    
    setTimeout(() => {
      setCopiedUuid(false);
    }, 2000);
  };

  // Объединенный список всех моделей
  const allModels = [
    // Anthropic models
    { value: 'anthropic/claude-3.7-sonnet', label: 'Claude 3.7 Sonnet', maxTokens: 8900 },
    { value: 'anthropic/claude-3.7-sonnet:online', label: 'Claude 3.7 Sonnet (Online)', maxTokens: 8900 },
    { value: 'anthropic/claude-3.7-sonnet:thinking', label: 'Claude 3.7 Sonnet (Thinking)', maxTokens: 8900 },
    { value: 'anthropic/claude-3.5-sonnet', label: 'Claude 3.5 Sonnet', maxTokens: 8900 },
    { value: 'anthropic/claude-3.5-haiku', label: 'Claude 3.5 Haiku', maxTokens: 8900 },
    { value: 'anthropic/claude-3-opus', label: 'Claude 3 Opus', maxTokens: 8900 },
    // OpenAI models
    { value: 'openai/gpt-4o-search-preview', label: 'GPT-4o Search Preview', maxTokens: 12800 },
    { value: 'openai/gpt-4o-mini-search-preview', label: 'GPT-4o Mini Search Preview', maxTokens: 12800 },
    { value: 'perplexity/sonar-deep-research', label: 'Sonar Deep Research', maxTokens: 12800 },
    { value: 'openai/gpt-4o-2024-11-20', label: 'GPT-4o (2024-11-20)', maxTokens: 12800 },
    { value: 'openai/gpt-4o-2024-11-20:online', label: 'GPT-4o (2024-11-20 Online)', maxTokens: 12800 },
    { value: 'openai/gpt-4o-2024-08-06', label: 'GPT-4o (2024-08-06)', maxTokens: 128000 },
    { value: 'openai/gpt-4o-2024-05-13', label: 'GPT-4o (2024-05-13)', maxTokens: 128000 },
    { value: 'openai/gpt-4o', label: 'GPT-4o', maxTokens: 128000 },
    { value: 'openai/gpt-4.1', label: 'GPT-4o', maxTokens: 128000 },
    { value: 'openai/gpt-4.1-mini', label: 'GPT-4o Mini', maxTokens: 128000 },
    { value: 'openai/gpt-4.1-nano', label: 'GPT-4o Nano', maxTokens: 128000 },
    { value: 'openai/chatgpt-4o-latest', label: 'ChatGPT-4o Latest', maxTokens: 128000 },
    { value: 'openai/gpt-4o-mini', label: 'GPT-4o Mini', maxTokens: 128000 },
    { value: 'openai/gpt-4-turbo', label: 'GPT-4 Turbo', maxTokens: 128000 },
    { value: 'openai/gpt-4', label: 'GPT-4', maxTokens: 8191 },
    { value: 'openai/gpt-4.5-preview', label: 'GPT-4.5 Preview', maxTokens: 8191 },
    { value: 'openai/gpt-4.5-preview:online', label: 'GPT-4.5 Preview (Online)', maxTokens: 8191 },
    { value: 'openai/gpt-3.5-turbo', label: 'GPT-3.5 Turbo', maxTokens: 16385 },
    { value: 'openai/o1', label: 'o1', maxTokens: 8000 },
    { value: 'openai/o1:online', label: 'o1 (Online)', maxTokens: 8000 },
    { value: 'openai/o1-mini', label: 'o1 Mini', maxTokens: 12800 },
    { value: 'openai/o3-mini-high', label: 'o3 Mini High', maxTokens: 12800 },
    { value: 'openai/o3-mini', label: 'o3 Mini', maxTokens: 12800 },
    { value: 'openai/o1-preview', label: 'o1 Preview', maxTokens: 12800 }
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {initialData?.id && initialData?.uuid && (
        <div className="border rounded-md p-3 bg-muted/30">
          <div className="flex items-center justify-between mb-1">
            <Label className="text-sm text-muted-foreground">UUID для API:</Label>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="h-6 px-2"
              onClick={handleCopyUuid}
            >
              {copiedUuid ? (
                <Check className="h-3.5 w-3.5 text-green-500" />
              ) : (
                <Copy className="h-3.5 w-3.5" />
              )}
            </Button>
          </div>
          <code className="text-xs font-mono block p-2 bg-muted rounded border w-full overflow-x-auto">
            {initialData.uuid}
          </code>
          <p className="text-xs text-muted-foreground mt-1">
            Этот UUID используется для доступа к агенту через внешний API
          </p>
        </div>
      )}

      <div className="space-y-4">
        <div>
          <Label htmlFor="name">Name</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="Agent name"
            disabled={isLoading}
          />
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            placeholder="Agent description"
            disabled={isLoading}
          />
        </div>

        <div>
          <Label htmlFor="promptId">Base Prompt</Label>
          <Select
            value={formData.promptId?.toString() || "none"}
            onValueChange={(value) => {
              if (value === "none") {
                setFormData({
                  ...formData,
                  promptId: undefined,
                  prompt: ""
                });
                return;
              }
              const selectedPrompt = prompts.find(p => p.id === parseInt(value));
              setFormData({
                ...formData,
                promptId: parseInt(value),
                prompt: selectedPrompt?.content || ""
              });
            }}
            disabled={isLoading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a base prompt" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">No base prompt</SelectItem>
              {prompts.map((prompt) => (
                <SelectItem key={prompt.id} value={prompt.id.toString()}>
                  {prompt.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="prompt">Prompt</Label>
          <Textarea
            id="prompt"
            value={formData.prompt}
            readOnly
            placeholder="Prompt will be shown here"
            className="h-48 bg-muted"
          />
        </div>

        <div>
          <Label htmlFor="model">Model</Label>
          <Select
            value={formData.settings.model}
            onValueChange={(value) => {
              const selectedModel = allModels.find(model => model.value === value);
              const provider = value.split('/')[0]; // Определяем провайдера из ID модели
              
              setFormData({
                ...formData,
                settings: {
                  ...formData.settings,
                  llmProvider: provider as 'openai' | 'anthropic',
                  model: value as LLMModel,
                  // Если пользователь вручную изменил maxTokens, сохраняем его значение
                  maxTokens: userModifiedMaxTokens 
                    ? formData.settings.maxTokens 
                    : selectedModel?.maxTokens || formData.settings.maxTokens
                }
              });
            }}
            disabled={isLoading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select model" />
            </SelectTrigger>
            <SelectContent>
              {allModels.map((model) => (
                <SelectItem key={model.value} value={model.value}>
                  {model.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="temperature">Temperature</Label>
          <Input
            id="temperature"
            type="number"
            min="0"
            max="1"
            step="0.1"
            value={formData.settings.temperature}
            onChange={(e) => setFormData({
              ...formData,
              settings: { ...formData.settings, temperature: parseFloat(e.target.value) }
            })}
            disabled={isLoading}
          />
        </div>

        <div>
          <Label htmlFor="maxTokens">Max Tokens</Label>
          <Input
            id="maxTokens"
            type="number"
            min="1"
            max="200000"
            value={formData.settings.maxTokens}
            onChange={(e) => {
              setUserModifiedMaxTokens(true);
              setFormData({
                ...formData,
                settings: { ...formData.settings, maxTokens: parseInt(e.target.value) }
              });
            }}
            disabled={isLoading}
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="isActive"
            checked={formData.isActive}
            onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
            disabled={isLoading}
          />
          <Label htmlFor="isActive">Active</Label>
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        {onDelete && (
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={isLoading}
          >
            Delete
          </Button>
        )}
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : initialData ? 'Update' : 'Create'}
        </Button>
      </div>
    </form>
  );
}

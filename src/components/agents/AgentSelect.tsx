import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useQuery } from "@tanstack/react-query"
import { Skeleton } from "@/components/ui/skeleton"
import { useEffect } from "react"

interface Agent {
  id: number
  name: string
  description: string
  type: string
  settings: Record<string, any>
}

interface AgentSelectProps {
  onSelect: (agent: Agent) => void
  selectedId?: number
  disabled?: boolean
}

export function AgentSelect({ onSelect, selectedId, disabled }: AgentSelectProps) {
  const { data, isLoading, error } = useQuery<{ agents: Agent[] }>({
    queryKey: ["agents"],
    queryFn: async () => {
      const response = await fetch("/api/v1/agents")
      if (!response.ok) {
        throw new Error("Ошибка загрузки агентов")
      }
      const data = await response.json()
      return { agents: Array.isArray(data.agents) ? data.agents : [] }
    },
  })

  const agents = data?.agents || []

  useEffect(() => {
    if (agents.length > 0 && !selectedId) {
      onSelect(agents[0])
    }
  }, [agents, selectedId, onSelect])

  if (error) {
    return (
      <Button variant="outline" className="w-full text-destructive">
        Ошибка загрузки агентов
      </Button>
    )
  }

  if (isLoading) {
    return <Skeleton className="h-10 w-full" />
  }

  return (
    <Select
      value={selectedId?.toString() || (agents.length > 0 ? agents[0].id.toString() : undefined)}
      onValueChange={(value) => {
        const agent = agents.find((a) => a.id === parseInt(value))
        if (agent) {
          onSelect(agent)
        }
      }}
      disabled={disabled}
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Select agent.." />
      </SelectTrigger>
      <SelectContent>
        {agents.length === 0 ? (
          <SelectItem value="empty" disabled>
            No agents
          </SelectItem>
        ) : (
          agents.map((agent) => (
            <SelectItem key={agent.id} value={agent.id.toString()}>
              <div className="flex flex-col">
                <span>{agent.name}</span>
                <span className="text-sm text-muted-foreground">
                  {agent.description}
                </span>
              </div>
            </SelectItem>
          ))
        )}
      </SelectContent>
    </Select>
  )
}

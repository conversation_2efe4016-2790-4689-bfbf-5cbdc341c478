'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Agent, CreateAgentInput } from '@/types/agent';
import { AgentsList } from './AgentsList';
import { AgentForm } from './AgentForm';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from '@/components/ui/use-toast';

export function AgentsManager() {
  const { data: session, status } = useSession();
  const [agents, setAgents] = useState<Agent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();

  const fetchAgents = async () => {
    try {
      setIsLoading(true);

      const response = await fetch('/api/v1/agents');
      const data = await response.json();
      
      
      if (!response.ok) {
        if (response.status === 401) {
          toast({
            title: 'Unauthorized',
            description: 'Please sign in to view agents',
            variant: 'destructive',
          });
          return;
        }
        throw new Error(data.error || 'Failed to fetch agents');
      }

      if (Array.isArray(data.agents)) {
        setAgents(data.agents);
      } else {
        console.warn('Unexpected data format:', data);
        setAgents([]);
      }
    } catch (error) {
      console.error('Error fetching agents:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch agents',
        variant: 'destructive',
      });
      setAgents([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (session?.user?.id) {
      fetchAgents();
    }
  }, [session]);

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-gray-500">Loading...</p>
      </div>
    );
  }

  if (status === "unauthenticated") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-gray-500">Please sign in to view agents</p>
      </div>
    );
  }

  const handleCreateAgent = async (data: CreateAgentInput) => {
    try {
      const response = await fetch('/api/v1/agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to create agent');
      }

      await fetchAgents();
      setIsDialogOpen(false);
      toast({
        title: 'Success',
        description: 'Agent created successfully',
      });
    } catch (error) {
      console.error('Error creating agent:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create agent',
        variant: 'destructive',
      });
    }
  };

  const handleEditAgent = async (agent: Agent) => {
    setSelectedAgent(agent);
    setIsDialogOpen(true);
  };

  const handleDeleteAgent = async (agent: Agent) => {
    try {
      const response = await fetch(`/api/v1/agents/${agent.id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete agent');
      }

      await fetchAgents();
      toast({
        title: 'Success',
        description: 'Agent deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting agent:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete agent',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">
          Agents ({agents.length})
        </h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>Create Agent</Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{selectedAgent ? 'Edit Agent' : 'Create Agent'}</DialogTitle>
              <DialogDescription>
                {selectedAgent 
                  ? 'Edit your agent details below.' 
                  : 'Create a new agent by filling out the form below.'}
              </DialogDescription>
            </DialogHeader>
            <AgentForm
              initialData={selectedAgent || undefined}
              onSubmit={handleCreateAgent}
            />
          </DialogContent>
        </Dialog>
      </div>

      {isLoading ? (
        <div className="text-center py-8">
          <p className="text-gray-500">Loading agents...</p>
        </div>
      ) : agents.length > 0 ? (
        <AgentsList
          agents={agents}
          onEdit={handleEditAgent}
          onDelete={handleDeleteAgent}
        />
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500">No agents found. Create your first agent!</p>
        </div>
      )}
    </div>
  );
}

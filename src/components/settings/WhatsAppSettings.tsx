"use client";

import { useState } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Plus, Pencil, Trash2, MessageCircle } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

const whatsappConfigSchema = z.object({
  apiUrl: z.string().url("Please enter a valid URL"),
  profileId: z.string().min(1, "Profile ID is required"),
  token: z.string().min(1, "Token is required"),
});

type WhatsAppConfigFormData = z.infer<typeof whatsappConfigSchema>;

interface WhatsAppConfig {
  id: string;
  apiUrl: string;
  profileId: string;
  token: string;
  createdAt: string;
  updatedAt: string;
}

export function WhatsAppSettings() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState<WhatsAppConfig | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<WhatsAppConfigFormData>({
    resolver: zodResolver(whatsappConfigSchema),
    defaultValues: {
      apiUrl: "",
      profileId: "",
      token: "",
    },
  });

  // Fetch WhatsApp configurations
  const { data: configs, isLoading } = useQuery<WhatsAppConfig[]>({
    queryKey: ['whatsapp-configs'],
    queryFn: async () => {
      const response = await fetch('/api/v1/settings/whatsapp');
      if (!response.ok) {
        throw new Error('Failed to fetch WhatsApp configurations');
      }
      return response.json();
    },
  });

  // Create configuration mutation
  const createMutation = useMutation({
    mutationFn: async (data: WhatsAppConfigFormData) => {
      const response = await fetch('/api/v1/settings/whatsapp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create configuration');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['whatsapp-configs'] });
      setIsCreateDialogOpen(false);
      form.reset();
      toast({
        title: "Success",
        description: "WhatsApp configuration created successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update configuration mutation
  const updateMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: WhatsAppConfigFormData }) => {
      const response = await fetch(`/api/v1/settings/whatsapp/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update configuration');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['whatsapp-configs'] });
      setEditingConfig(null);
      form.reset();
      toast({
        title: "Success",
        description: "WhatsApp configuration updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete configuration mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/v1/settings/whatsapp/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete configuration');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['whatsapp-configs'] });
      toast({
        title: "Success",
        description: "WhatsApp configuration deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: WhatsAppConfigFormData) => {
    if (editingConfig) {
      updateMutation.mutate({ id: editingConfig.id, data });
    } else {
      createMutation.mutate(data);
    }
  };

  const handleEdit = (config: WhatsAppConfig) => {
    setEditingConfig(config);
    form.reset({
      apiUrl: config.apiUrl,
      profileId: config.profileId,
      token: "", // Don't pre-fill token for security
    });
  };

  const handleDelete = (id: string) => {
    if (confirm("Are you sure you want to delete this WhatsApp configuration?")) {
      deleteMutation.mutate(id);
    }
  };

  const closeDialog = () => {
    setIsCreateDialogOpen(false);
    setEditingConfig(null);
    form.reset();
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="w-5 h-5" />
              WhatsApp Settings
            </CardTitle>
            <CardDescription>
              Configure WhatsApp API settings for sending messages to leads.
            </CardDescription>
          </div>
          <Dialog open={isCreateDialogOpen || !!editingConfig} onOpenChange={closeDialog}>
            <DialogTrigger asChild>
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Configuration
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {editingConfig ? "Edit" : "Add"} WhatsApp Configuration
                </DialogTitle>
              </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="apiUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>API URL</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://your-whatsapp-api.com"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="profileId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Profile ID</FormLabel>
                        <FormControl>
                          <Input placeholder="your_profile_id" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="token"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Token</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder={editingConfig ? "Leave empty to keep current token" : "your_token"}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex justify-end gap-2">
                    <Button type="button" variant="outline" onClick={closeDialog}>
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={createMutation.isPending || updateMutation.isPending}
                    >
                      {(createMutation.isPending || updateMutation.isPending) && (
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      )}
                      {editingConfig ? "Update" : "Create"}
                    </Button>
                  </div>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span className="ml-2">Loading configurations...</span>
          </div>
        ) : configs && configs.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>API URL</TableHead>
                <TableHead>Profile ID</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {configs.map((config) => (
                <TableRow key={config.id}>
                  <TableCell className="font-medium">{config.apiUrl}</TableCell>
                  <TableCell>{config.profileId}</TableCell>
                  <TableCell>
                    {new Date(config.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(config)}
                      >
                        <Pencil className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(config.id)}
                        disabled={deleteMutation.isPending}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            No WhatsApp configurations found. Add one to get started.
          </div>
        )}
      </CardContent>
    </Card>
  );
}

"use client";

import { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { format } from "date-fns";

interface HistoryItem {
  Id?: string;
  Subject?: string;
  Description?: string;
  ActivityDate?: string;
  Status?: string;
  CreatedDate: string;
  StartDateTime?: string;
  EndDateTime?: string;
  Field?: string;
  OldValue?: string;
  NewValue?: string;
}

interface LeadCommunicationsHistoryProps {
  leadId: string;
}

export function LeadCommunicationsHistory({
  leadId,
}: LeadCommunicationsHistoryProps) {
  const [history, setHistory] = useState<HistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchHistory() {
      if (!leadId) return;
      
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch(`/api/leads/${leadId}/history`);
        if (!response.ok) {
          throw new Error("Failed to fetch history");
        }
        const historyData = await response.json();
        setHistory(historyData);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch history");
      } finally {
        setIsLoading(false);
      }
    }

    fetchHistory();
  }, [leadId]);

  const renderHistoryItem = (item: HistoryItem) => {
    const date = format(new Date(item.CreatedDate), "MMM d, yyyy h:mm a");

    // Task
    if (item.Subject && !item.StartDateTime) {
      return (
        <TableRow key={item.Id}>
          <TableCell className="w-44">{date}</TableCell>
          <TableCell>Task</TableCell>
          <TableCell>
            <div className="font-medium">{item.Subject}</div>
            {item.Description && (
              <div className="text-sm text-gray-500">{item.Description}</div>
            )}
            {item.Status && (
              <div className="text-sm text-gray-500">Status: {item.Status}</div>
            )}
          </TableCell>
        </TableRow>
      );
    }

    // Event
    if (item.StartDateTime) {
      return (
        <TableRow key={item.Id}>
          <TableCell className="w-44">{date}</TableCell>
          <TableCell>Event</TableCell>
          <TableCell>
            <div className="font-medium">{item.Subject}</div>
            {item.Description && (
              <div className="text-sm text-gray-500">{item.Description}</div>
            )}
            <div className="text-sm text-gray-500">
              {format(new Date(item.StartDateTime), "MMM d, yyyy h:mm a")} -{" "}
              {format(new Date(item.EndDateTime!), "h:mm a")}
            </div>
          </TableCell>
        </TableRow>
      );
    }

    // Field History
    if (item.Field) {
      return (
        <TableRow key={`${item.Field}-${item.CreatedDate}`}>
          <TableCell className="w-44">{date}</TableCell>
          <TableCell>Field Update</TableCell>
          <TableCell>
            <div className="font-medium">{item.Field}</div>
            <div className="text-sm text-gray-500">
              Changed from: {item.OldValue || "(empty)"} to:{" "}
              {item.NewValue || "(empty)"}
            </div>
          </TableCell>
        </TableRow>
      );
    }

    return null;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Lead Communication History</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="py-4 text-center">Loading history...</div>
        ) : error ? (
          <div className="py-4 text-center text-red-500">{error}</div>
        ) : history.length === 0 ? (
          <div className="py-4 text-center">No history found</div>
        ) : (
          <div className="w-full rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Details</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {history.map(renderHistoryItem)}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

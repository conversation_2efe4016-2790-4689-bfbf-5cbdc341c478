"use client";

import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Lead } from "@/db/schema/leads";
import { StatusBadge } from "@/components/ui/status-badge";
import { LeadForm } from "./LeadForm";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  ChevronUpIcon,
  ChevronDownIcon,
  ChevronsUpDown,
  Loader2,
  Pencil,
  Trash,
  ExternalLink,
  Phone
} from "lucide-react";
import Link from "next/link";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface LeadsTableProps {
  leads: Lead[];
  isLoading?: boolean;
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  onUpdate: (id: number, data: Partial<Lead>) => Promise<void>;
  onDelete: (id: number) => Promise<void>;
  onSort: (sortBy: string, sortOrder: "asc" | "desc") => void;
  onPageChange: (page: number) => void;
  onSelectionChange?: (selectedIds: number[]) => void;
  selectedLeads?: number[];
}

export function LeadsTable({ 
  leads = [], 
  isLoading,
  pagination,
  selectedLeads = [],
  onUpdate,
  onDelete,
  onSort,
  onPageChange,
  onSelectionChange
}: LeadsTableProps) {
  const [editingLead, setEditingLead] = useState<Lead | null>(null);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [sortBy, setSortBy] = useState<string>("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // Создаем массив страниц для селекта
  const pageOptions = Array.from(
    { length: pagination.totalPages },
    (_, i) => i + 1
  );

  const getStatusVariant = (status: string | null) => {
    if (!status) return 'default';
    switch (status.toLowerCase()) {
      case 'active':
        return 'success';
      case 'pending':
        return 'warning';
      case 'inactive':
        return 'error';
      case 'new':
        return 'info';
      default:
        return 'default';
    }
  };

  const handlePageChange = (value: string) => {
    const page = parseInt(value);
    if (!isNaN(page) && page >= 1 && page <= pagination.totalPages) {
      onPageChange(page);
    }
  };

  // Обновляем инпут при изменении страницы
  useEffect(() => {
    // setPageInput(pagination.page.toString());
  }, [pagination.page]);

  const handleSort = (key: string) => {
    const direction = 
      sortBy === key && sortOrder === "asc" 
        ? "desc" 
        : "asc";
    setSortBy(key);
    setSortOrder(direction);
    onSort(key, direction);
  };

  const handleSelectAll = (checked: boolean) => {
    const newSelectedIds = checked ? leads.map(lead => lead.id) : [];
    onSelectionChange?.(newSelectedIds);
  };

  const handleSelect = (id: number, checked: boolean) => {
    const newSelectedIds = checked
      ? [...selectedLeads, id]
      : selectedLeads.filter(selectedId => selectedId !== id);
    onSelectionChange?.(newSelectedIds);
  };

  const getSortIcon = (key: string) => {
    if (sortBy !== key) return <ChevronsUpDown className="w-4 h-4" />;
    return sortOrder === "asc" 
      ? <ChevronUpIcon className="w-4 h-4" />
      : <ChevronDownIcon className="w-4 h-4" />;
  };

  if (isLoading) {
    return (
      <div className="w-full h-32 flex items-center justify-center">
        <Loader2 className="w-6 h-6 animate-spin" />
      </div>
    );
  }

  return (
    <div className="w-full space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableCell className="w-[40px]">
              <Checkbox
                checked={leads.length > 0 && leads.every(lead => selectedLeads.includes(lead.id))}
                onCheckedChange={handleSelectAll}
                aria-label="Select all"
              />
            </TableCell>
            <TableCell>
              <button
                onClick={() => handleSort("firstName")}
                className="flex items-center gap-1"
              >
                Name {getSortIcon("firstName")}
              </button>
            </TableCell>
            <TableCell>
              <button
                onClick={() => handleSort("email")}
                className="flex items-center gap-1"
              >
                Email {getSortIcon("email")}
              </button>
            </TableCell>
            <TableCell>
              <button
                onClick={() => handleSort("phone")}
                className="flex items-center gap-1"
              >
                Phone {getSortIcon("phone")}
              </button>
            </TableCell>
            <TableCell>
              <button
                onClick={() => handleSort("company")}
                className="flex items-center gap-1"
              >
                Company {getSortIcon("company")}
              </button>
            </TableCell>
            <TableCell>
              <button
                onClick={() => handleSort("title")}
                className="flex items-center gap-1"
              >
                Title {getSortIcon("title")}
              </button>
            </TableCell>
            <TableCell>
              <button
                onClick={() => handleSort("status")}
                className="flex items-center gap-1"
              >
                Status {getSortIcon("status")}
              </button>
            </TableCell>
            <TableCell className="text-right">Actions</TableCell>
          </TableRow>
        </TableHeader>
        <TableBody>
          {leads.map((lead) => (
            <TableRow key={lead.id}>
              <TableCell>
                <Checkbox
                  checked={selectedLeads.includes(lead.id)}
                  onCheckedChange={(checked) => handleSelect(lead.id, checked === true)}
                  aria-label={`Select ${lead.firstName} ${lead.lastName}`}
                />
              </TableCell>
              <TableCell>{lead.firstName} {lead.lastName}</TableCell>
              <TableCell>{lead.email}</TableCell>
              <TableCell>
                {lead.phone ? (
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-gray-400" />
                    <span>{lead.phone}</span>
                  </div>
                ) : (
                  <span className="text-gray-400">-</span>
                )}
              </TableCell>
              <TableCell>{lead.company}</TableCell>
              <TableCell>{lead.title}</TableCell>
              <TableCell>
                <StatusBadge
                  variant={getStatusVariant(lead.status)}
                >
                  {lead.status}
                </StatusBadge>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex items-center justify-end space-x-2">
                  <Link
                    href={`/leads/${lead.id}`}
                    className="text-blue-500 hover:text-blue-700"
                    target="_blank"
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Link>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setEditingLead(lead)}
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setDeletingId(lead.id)}
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Showing page {pagination.page} of {pagination.totalPages}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pagination.page - 1)}
            disabled={pagination.page <= 1}
          >
            Previous
          </Button>
          <Select
            value={pagination.page.toString()}
            onValueChange={handlePageChange}
          >
            <SelectTrigger className="w-[70px]">
              <SelectValue placeholder={pagination.page} />
            </SelectTrigger>
            <SelectContent>
              {pageOptions.map((page) => (
                <SelectItem key={page} value={page.toString()}>
                  {page}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <span className="text-sm text-muted-foreground">
            of {pagination.totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pagination.page + 1)}
            disabled={pagination.page >= pagination.totalPages}
          >
            Next
          </Button>
        </div>
      </div>

      <Dialog open={!!editingLead} onOpenChange={(open) => !open && setEditingLead(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Lead</DialogTitle>
          </DialogHeader>
          {editingLead && (
            <LeadForm
              initialData={editingLead}
              onSubmit={async (data) => {
                try {
                  await onUpdate(editingLead.id, data);
                  setEditingLead(null);
                } catch (error) {
                  console.error('Failed to update lead:', error);
                }
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      <Dialog open={!!deletingId} onOpenChange={(open) => !open && setDeletingId(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Lead</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this lead? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-4">
            <Button variant="outline" onClick={() => setDeletingId(null)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={async () => {
                if (!deletingId) return;
                try {
                  await onDelete(deletingId);
                  setDeletingId(null);
                } catch (error) {
                  console.error('Failed to delete lead:', error);
                }
              }}
            >
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

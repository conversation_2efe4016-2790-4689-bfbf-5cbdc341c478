"use client";

import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Lead } from "@/db/schema/leads";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { CheckCircle, XCircle, Loader2, MessageCircle, Phone } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface SendWhatsAppModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedLeadIds: number[];
}

interface WhatsAppConfig {
  id: string;
  apiUrl: string;
  profileId: string;
  createdAt: string;
}

interface WhatsAppMessage {
  id: number;
  leadId: number;
  content: string;
  phoneNumber: string;
  sent: boolean;
  createdAt: string;
}

interface LeadWithMessage extends Lead {
  whatsappMessage?: WhatsAppMessage;
  messageStatus?: 'generating' | 'generated' | 'sending' | 'sent' | 'error';
  error?: string;
}

export function SendWhatsAppModal({ isOpen, onClose, selectedLeadIds }: SendWhatsAppModalProps) {
  const [selectedConfig, setSelectedConfig] = useState<string>("default");
  const [selectedAgent, setSelectedAgent] = useState<string>("");
  const [leads, setLeads] = useState<LeadWithMessage[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const { toast } = useToast();

  // Fetch WhatsApp configurations
  const { data: whatsappConfigs, isLoading: isLoadingConfigs } = useQuery<WhatsAppConfig[]>({
    queryKey: ['whatsapp-configs'],
    queryFn: async () => {
      const response = await fetch('/api/v1/settings/whatsapp');
      if (!response.ok) {
        throw new Error('Failed to fetch WhatsApp configurations');
      }
      return response.json();
    },
    enabled: isOpen,
  });

  // Fetch agents
  const { data: agents, isLoading: isLoadingAgents } = useQuery({
    queryKey: ["agents"],
    queryFn: async () => {
      const response = await fetch("/api/v1/agents");
      if (!response.ok) throw new Error("Failed to fetch agents");
      const data = await response.json();
      return data;
    },
    enabled: isOpen,
  });

  // Fetch leads data
  const { data: leadsData, isLoading: isLoadingLeads } = useQuery<{ leads: Lead[] }>({
    queryKey: ['leads-for-whatsapp', selectedLeadIds],
    queryFn: async () => {
      const response = await fetch(`/api/leads?ids=${selectedLeadIds.join(',')}`);
      if (!response.ok) {
        throw new Error('Failed to fetch leads');
      }
      return response.json();
    },
    enabled: isOpen && selectedLeadIds.length > 0,
  });

  useEffect(() => {
    if (leadsData?.leads) {
      setLeads(leadsData.leads.map(lead => ({ ...lead, messageStatus: undefined })));
    }
  }, [leadsData]);

  // Set first agent as default when modal opens and agents are loaded
  useEffect(() => {
    if (isOpen && agents?.agents?.length > 0 && !selectedAgent) {
      setSelectedAgent(String(agents.agents[0].id));
    }
  }, [isOpen, agents, selectedAgent]);

  const generateMessages = async () => {
    if (leads.length === 0) return;

    setIsGenerating(true);
    
    try {
      // Filter leads that have phone numbers
      const leadsWithPhone = leads.filter(lead => lead.phone);
      
      if (leadsWithPhone.length === 0) {
        toast({
          title: "No phone numbers",
          description: "None of the selected leads have phone numbers.",
          variant: "destructive",
        });
        return;
      }

      // Generate messages for each lead
      const updatedLeads = [...leads];
      
      for (let i = 0; i < leadsWithPhone.length; i++) {
        const lead = leadsWithPhone[i];
        const leadIndex = updatedLeads.findIndex(l => l.id === lead.id);
        
        if (leadIndex === -1) continue;

        try {
          updatedLeads[leadIndex].messageStatus = 'generating';
          setLeads([...updatedLeads]);

          const response = await fetch('/api/whatsapp', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              leadId: lead.id,
              agentId: selectedAgent || undefined
            }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to generate message');
          }

          const messageData = await response.json();
          updatedLeads[leadIndex].whatsappMessage = messageData;
          updatedLeads[leadIndex].messageStatus = 'generated';
        } catch (error) {
          updatedLeads[leadIndex].messageStatus = 'error';
          updatedLeads[leadIndex].error = error instanceof Error ? error.message : 'Unknown error';
        }

        setLeads([...updatedLeads]);
      }

      toast({
        title: "Messages generated",
        description: `Generated WhatsApp messages for ${leadsWithPhone.length} leads.`,
      });
    } catch (error) {
      console.error('Error generating messages:', error);
      toast({
        title: "Error",
        description: "Failed to generate WhatsApp messages.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const sendMessages = async () => {
    const leadsWithMessages = leads.filter(lead => 
      lead.whatsappMessage && lead.messageStatus === 'generated'
    );

    if (leadsWithMessages.length === 0) {
      toast({
        title: "No messages to send",
        description: "Please generate messages first.",
        variant: "destructive",
      });
      return;
    }

    setIsSending(true);

    try {
      const updatedLeads = [...leads];

      for (const lead of leadsWithMessages) {
        const leadIndex = updatedLeads.findIndex(l => l.id === lead.id);
        if (leadIndex === -1 || !lead.whatsappMessage) continue;

        try {
          updatedLeads[leadIndex].messageStatus = 'sending';
          setLeads([...updatedLeads]);

          const response = await fetch('/api/whatsapp/send', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              messageId: lead.whatsappMessage.id,
              whatsappConfigId: selectedConfig === "default" ? undefined : selectedConfig,
            }),
          });

          const result = await response.json();

          if (!response.ok || !result.success) {
            throw new Error(result.error || 'Failed to send message');
          }

          updatedLeads[leadIndex].messageStatus = 'sent';
          if (updatedLeads[leadIndex].whatsappMessage) {
            updatedLeads[leadIndex].whatsappMessage!.sent = true;
          }
        } catch (error) {
          updatedLeads[leadIndex].messageStatus = 'error';
          updatedLeads[leadIndex].error = error instanceof Error ? error.message : 'Unknown error';
        }

        setLeads([...updatedLeads]);
      }

      const sentCount = updatedLeads.filter(l => l.messageStatus === 'sent').length;
      toast({
        title: "Messages sent",
        description: `Successfully sent ${sentCount} WhatsApp messages.`,
      });
    } catch (error) {
      console.error('Error sending messages:', error);
      toast({
        title: "Error",
        description: "Failed to send WhatsApp messages.",
        variant: "destructive",
      });
    } finally {
      setIsSending(false);
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'generating':
      case 'sending':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
      case 'generated':
        return <MessageCircle className="w-4 h-4 text-green-500" />;
      case 'sent':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  const hasGeneratedMessages = leads.some(lead => lead.messageStatus === 'generated');
  const canSend = hasGeneratedMessages && !isSending;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Send WhatsApp Messages</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Agent Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Select Agent</label>
            <Select
              value={selectedAgent}
              onValueChange={setSelectedAgent}
              disabled={isGenerating || isLoadingAgents}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select an agent" />
              </SelectTrigger>
              <SelectContent>
                {agents?.agents?.map((agent: any) => (
                  <SelectItem key={agent.id} value={String(agent.id)}>
                    <div className="flex flex-col">
                      <span>{agent.name}</span>
                      <span className="text-sm text-muted-foreground">
                        {agent.description}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* WhatsApp Configuration Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">WhatsApp Configuration (Optional)</label>
            <Select value={selectedConfig} onValueChange={setSelectedConfig}>
              <SelectTrigger>
                <SelectValue placeholder="Use default configuration or select custom" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="default">Default Configuration</SelectItem>
                {whatsappConfigs?.map((config) => (
                  <SelectItem key={config.id} value={config.id}>
                    {config.profileId} - {config.apiUrl}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4">
            <Button
              onClick={generateMessages}
              disabled={isGenerating || leads.length === 0 || !selectedAgent}
              className="flex items-center gap-2"
            >
              {isGenerating && <Loader2 className="w-4 h-4 animate-spin" />}
              Generate Messages
            </Button>

            <Button
              onClick={sendMessages}
              disabled={!canSend}
              variant="default"
              className="flex items-center gap-2"
            >
              {isSending && <Loader2 className="w-4 h-4 animate-spin" />}
              Send Messages
            </Button>
          </div>

          {/* Leads Table */}
          <div className="border rounded-lg max-h-[400px] overflow-y-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[200px]">Name</TableHead>
                  <TableHead className="w-[150px]">Phone</TableHead>
                  <TableHead className="w-[200px]">Company</TableHead>
                  <TableHead className="w-[300px]">Message</TableHead>
                  <TableHead className="w-[100px]">Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoadingLeads ? (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24">
                      <div className="flex items-center justify-center">
                        <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
                        <span className="ml-2 text-sm text-muted-foreground">Loading leads...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : leads?.map((lead) => (
                  <TableRow key={lead.id}>
                    <TableCell className="font-medium">
                      {lead.firstName} {lead.lastName}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Phone className="w-4 h-4 text-gray-400" />
                        {lead.phone || 'No phone'}
                      </div>
                    </TableCell>
                    <TableCell>{lead.company}</TableCell>
                    <TableCell>
                      {lead.whatsappMessage ? (
                        <div className="text-sm">
                          {lead.whatsappMessage.content.substring(0, 100)}
                          {lead.whatsappMessage.content.length > 100 && '...'}
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">No message generated</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(lead.messageStatus)}
                        <span className="text-sm capitalize">
                          {lead.messageStatus || 'pending'}
                        </span>
                      </div>
                      {lead.error && (
                        <div className="text-xs text-red-500 mt-1">{lead.error}</div>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

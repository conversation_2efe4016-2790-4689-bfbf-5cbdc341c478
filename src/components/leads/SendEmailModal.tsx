"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import Link from "next/link";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Lead } from "@/db/schema/leads";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { CheckCircle, XCircle, Loader2, ExternalLink, Eye } from "lucide-react";
import { EmailDetailsModal } from "@/components/emails/EmailDetailsModal";

interface SendEmailModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedLeads: number[];
}

type EmailStatus = {
  leadId: number;
  status: 'idle' | 'generating' | 'sending' | 'success' | 'error';
  error?: string;
  emailId?: number;
};

export function SendEmailModal({ isOpen, onClose, selectedLeads }: SendEmailModalProps) {
  const [selectedAgent, setSelectedAgent] = useState<string>("");
  const [selectedEmailId, setSelectedEmailId] = useState<number | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [emailStatuses, setEmailStatuses] = useState<EmailStatus[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentlySendingLeadId, setCurrentlySendingLeadId] = useState<number | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Reset statuses when modal opens or selected leads change
  useEffect(() => {
    if (isOpen) {
      // Сортируем ID лидов в порядке возрастания
      const sortedLeadIds = [...selectedLeads].sort((a, b) => a - b);
      setEmailStatuses(sortedLeadIds.map(id => ({
        leadId: id,
        status: 'idle'
      })));
      setSelectedAgent("");
      setIsGenerating(false);
      setCurrentlySendingLeadId(null);
    }
  }, [isOpen, selectedLeads]);

  const handleCancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setIsGenerating(false);
    setCurrentlySendingLeadId(null);
    setEmailStatuses(prev => prev.map(status => ({
      ...status,
      status: status.status === 'generating' || status.status === 'sending' ? 'error' : status.status,
      error: status.status === 'generating' || status.status === 'sending' ? 'Cancelled' : status.error
    })));
  }, []);

  // Cleanup abort controller when modal closes
  useEffect(() => {
    if (!isOpen) {
      handleCancel();
    }
    // Cleanup on unmount
    return () => {
      handleCancel();
    };
  }, [isOpen, handleCancel]);

  // Fetch leads data for the selected IDs
  const { data: leads, isLoading: isLoadingLeads } = useQuery({
    queryKey: ["selected-leads", selectedLeads],
    queryFn: async () => {
      const response = await fetch(`/api/leads?ids=${selectedLeads.join(",")}`);
      if (!response.ok) throw new Error("Failed to fetch leads");
      const data = await response.json();
      // Сортируем лидов по ID в порядке возрастания
      return data.leads.sort((a: Lead, b: Lead) => a.id - b.id) as Lead[];
    },
    enabled: selectedLeads.length > 0,
  });

  // Fetch agents
  const { data: agents, isLoading: isLoadingAgents } = useQuery({
    queryKey: ["agents"],
    queryFn: async () => {
      const response = await fetch("/api/v1/agents");
      if (!response.ok) throw new Error("Failed to fetch agents");
      const data = await response.json();
      return data; // Возвращаем весь объект data
    },
  });

  // Set first agent as default when modal opens and agents are loaded
  useEffect(() => {
    if (isOpen && agents?.length > 0 && !selectedAgent) {
      setSelectedAgent(String(agents[0].id));
    }
  }, [isOpen, agents, selectedAgent]);

  const handleSend = async () => {
    if (!selectedAgent) return;
    
    setIsGenerating(true);
    abortControllerRef.current = new AbortController();
    
    try {
      // Сортируем ID лидов в порядке возрастания
      const sortedLeadIds = [...selectedLeads].sort((a, b) => a - b);
      
      for (const leadId of sortedLeadIds) {
        try {
          // Проверяем, не была ли отменена операция
          if (abortControllerRef.current?.signal.aborted) {
            throw new Error('Operation cancelled');
          }

          setCurrentlySendingLeadId(leadId);
          setEmailStatuses(prev => prev.map(status => ({
            ...status,
            status: status.leadId === leadId ? 'generating' : status.status
          })));

          // Шаг 1: Генерация email
          const generateResponse = await fetch("/api/v1/generate", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              leadId,
              agentId: selectedAgent
            }),
            signal: abortControllerRef.current.signal
          });

          if (!generateResponse.ok) {
            throw new Error("Failed to generate email");
          }
          
          const emailData = await generateResponse.json();
          
          if (emailData.type === 'error') {
            throw new Error(emailData.error.message || "Failed to generate email");
          }

          // Проверяем, не была ли отменена операция
          if (abortControllerRef.current?.signal.aborted) {
            throw new Error('Operation cancelled');
          }

          // Обновляем статус на "sending"
          setEmailStatuses(prev => prev.map(status => ({
            ...status,
            status: status.leadId === leadId ? 'sending' : status.status
          })));

          // Шаг 2: Отправка email
          const sendResponse = await fetch("/api/test/email/send", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ emailId: emailData.id }),
            signal: abortControllerRef.current.signal
          });

          if (!sendResponse.ok) {
            throw new Error("Failed to send email");
          }

          setEmailStatuses(prev => prev.map(status => 
            status.leadId === leadId ? { ...status, status: 'success', emailId: emailData.id } : status
          ));
        } catch (error) {
          if (error instanceof Error && error.name === 'AbortError') {
            throw error; // Re-throw AbortError to break the main loop
          }
          setEmailStatuses(prev => prev.map(status => 
            status.leadId === leadId ? { 
              ...status, 
              status: 'error',
              error: error instanceof Error ? error.message : 'Unknown error'
            } : status
          ));
        }
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('Operation was cancelled');
      }
    } finally {
      setIsGenerating(false);
      setCurrentlySendingLeadId(null);
      abortControllerRef.current = null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Generate Emails ({selectedLeads.length} leads)</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Select Agent</label>
            <Select
              value={selectedAgent}
              onValueChange={setSelectedAgent}
              disabled={isGenerating}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select an agent" />
              </SelectTrigger>
              <SelectContent>
                {agents?.agents?.map((agent: any) => (
                  <SelectItem key={agent.id} value={String(agent.id)}>
                    <div className="flex flex-col">
                      <span>{agent.name}</span>
                      <span className="text-sm text-muted-foreground">
                        {agent.description}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="border rounded-lg max-h-[400px] overflow-y-auto">
            <div className="min-w-full">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[200px]">Name</TableHead>
                    <TableHead className="w-[250px]">Email</TableHead>
                    <TableHead className="w-[200px]">Company</TableHead>
                    <TableHead className="w-[100px]">Status</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoadingLeads ? (
                    <TableRow>
                      <TableCell colSpan={5} className="h-24">
                        <div className="flex items-center justify-center">
                          <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
                          <span className="ml-2 text-sm text-muted-foreground">Loading leads...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : leads?.map((lead) => {
                    const status = emailStatuses.find(s => s.leadId === lead.id);
                    return (
                      <TableRow key={lead.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <span>{lead.firstName} {lead.lastName}</span>
                            <Link 
                              href={`/leads/${lead.id}`}
                              target="_blank"
                              className="text-blue-500 hover:text-blue-700"
                            >
                              <ExternalLink className="w-4 h-4" />
                            </Link>
                          </div>
                        </TableCell>
                        <TableCell>{lead.email}</TableCell>
                        <TableCell>{lead.company}</TableCell>
                        <TableCell>
                          <div className="flex items-center justify-center">
                            {status?.status === 'generating' && currentlySendingLeadId === lead.id && (
                              <div className="flex items-center space-x-2">
                                <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
                                <span className="text-sm text-muted-foreground">Generating...</span>
                              </div>
                            )}
                            {status?.status === 'sending' && currentlySendingLeadId === lead.id && (
                              <div className="flex items-center space-x-2">
                                <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
                                <span className="text-sm text-muted-foreground">Sending...</span>
                              </div>
                            )}
                            {status?.status === 'success' && (
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="w-5 h-5 text-green-500" />
                                <span className="text-sm text-muted-foreground">Sent</span>
                              </div>
                            )}
                            {status?.status === 'error' && (
                              <div className="flex items-center space-x-2" title={status.error}>
                                <XCircle className="w-5 h-5 text-red-500" />
                                <span className="text-sm text-muted-foreground">Failed</span>
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-center">
                          <div className="flex items-center justify-center space-x-2">
                            {status?.status === 'success' && (
                              <button
                                onClick={() => {
                                  const leadEmail = emailStatuses.find(l => l.leadId === lead.id);
                                  setSelectedEmailId(leadEmail?.emailId || null);
                                  setIsDetailsModalOpen(true);
                                }}
                                className="p-2 hover:bg-gray-100 rounded-full"
                                title="View Email Details"
                              >
                                <Eye className="w-4 h-4" />
                              </button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose} disabled={isGenerating}>
              Cancel
            </Button>
            {isGenerating ? (
              <Button variant="destructive" onClick={handleCancel}>
                Stop
              </Button>
            ) : (
              <Button onClick={handleSend} disabled={!selectedAgent}>
                Generate Emails
              </Button>
            )}
          </div>
        </div>
      </DialogContent>

      <EmailDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedEmailId(null);
        }}
        emailId={selectedEmailId}
      />
    </Dialog>
  );
}
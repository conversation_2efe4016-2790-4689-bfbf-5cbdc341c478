import * as dotenv from "dotenv";
import { resolve } from "path";
import { addUuidToAgents } from "./migrations/add-uuid-to-agents";
import { createApiTokensTable } from "./migrations/create-api-tokens-table";
import { createOperationsTable } from "./migrations/create-operations-table";
import { columnExists, tableExists } from "./migrator";
import { db } from ".";

// Загружаем переменные окружения из файла .env
dotenv.config({ path: resolve(process.cwd(), ".env") });

async function runMigrations() {
  try {
    // Проверяем, что DATABASE_URL определен
    if (!process.env.DATABASE_URL) {
      throw new Error("DATABASE_URL не определен в файле .env");
    }

    console.log("🔄 Начало миграций...");

    // Проверка существования таблицы api_tokens
    const apiTokensTableExists = await tableExists("api_tokens");
    if (!apiTokensTableExists) {
      console.log("📝 Создание таблицы API-токенов...");
      await createApiTokensTable();
      console.log("✅ Таблица API-токенов успешно создана");
    } else {
      console.log("✓ Таблица API-токенов уже существует, пропускаем");
    }

    // Проверка наличия колонки uuid в таблице agents
    const uuidColumnExists = await columnExists("agents", "uuid");
    if (!uuidColumnExists) {
      console.log("📝 Добавление UUID к агентам...");
      await addUuidToAgents();
      console.log("✅ UUID успешно добавлен к агентам");
    } else {
      console.log("✓ Колонка UUID уже существует в таблице агентов, пропускаем");
    }

    // Проверка существования таблицы operations
    const operationsTableExists = await tableExists("operations");
    if (!operationsTableExists) {
      console.log("📝 Создание таблицы операций...");
      await createOperationsTable();
      console.log("✅ Таблица операций успешно создана");
    } else {
      console.log("✓ Таблица операций уже существует, пропускаем");
    }

    console.log("✅ Все миграции успешно выполнены!");
  } catch (error) {
    console.error("❌ Ошибка во время выполнения миграций:", error);
    process.exit(1);
  } finally {
    // В зависимости от драйвера БД, может потребоваться другой способ закрытия соединения
    try {
      // @ts-ignore - игнорируем ошибку типа, т.к. разные драйверы могут иметь разные методы
      if (db.$client && typeof db.$client.end === 'function') {
        await db.$client.end();
      }
    } catch (err) {
      console.error('Ошибка при закрытии соединения с БД:', err);
    }
    process.exit(0);
  }
}

runMigrations();

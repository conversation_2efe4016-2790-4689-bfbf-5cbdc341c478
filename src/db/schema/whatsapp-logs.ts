import { pgTable, serial, text, timestamp, integer, jsonb } from "drizzle-orm/pg-core";
import { whatsappMessages } from "./whatsapp-messages";

export const whatsappLogs = pgTable("whatsapp_logs", {
    id: serial("id").primaryKey(),
    messageId: integer("message_id")
        .notNull()
        .references(() => whatsappMessages.id, { onDelete: "cascade" }),
    rawPrompt: text("raw_prompt").notNull(),
    formattedPrompt: text("formatted_prompt").notNull(),
    variables: jsonb("variables").notNull(),
    aiResponse: jsonb("ai_response").notNull(),
    executionTimeMs: integer("execution_time_ms"),
    modelName: text("model_name"),
    promptTokens: integer("prompt_tokens"),
    completionTokens: integer("completion_tokens"),
    totalTokens: integer("total_tokens"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
});

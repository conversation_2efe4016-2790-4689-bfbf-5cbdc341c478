import { pgTable, timestamp, text, integer, varchar } from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { users } from "./users";

export const emailConfigs = pgTable("email_configs", {
    id: varchar("id", { length: 191 }).primaryKey().notNull(),
    smtp: text("smtp").notNull(),
    port: integer("port").notNull(),
    email: text("email").notNull(),
    password: text("password").notNull(),
    createdBy: integer("created_by")
        .notNull()
        .references(() => users.id),
    createdAt: timestamp("created_at")
        .notNull()
        .default(sql`CURRENT_TIMESTAMP`),
    updatedAt: timestamp("updated_at")
        .notNull()
        .default(sql`CURRENT_TIMESTAMP`),
});

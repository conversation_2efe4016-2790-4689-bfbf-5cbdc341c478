import {
    pgTable,
    serial,
    text,
    timestamp,
    uuid,
    varchar,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';

// Define operation status enum
export const operationStatusEnum = ['PENDING', 'PROCESSING', 'SUCCESS', 'ERROR', 'CANCELLED'] as const;

// Define operation types enum
export const operationTypeEnum = ['LEAD_ENRICHMENT', 'EMAIL_GENERATION', 'DATA_IMPORT', 'DATA_EXPORT'] as const;

// Operations table schema
export const operations = pgTable("operations", {
    id: serial("id").primaryKey(),
    uuid: uuid("uuid").defaultRandom().notNull().unique(),
    type: varchar("type", { length: 50 }).notNull().$type<typeof operationTypeEnum[number]>(),
    status: varchar("status", { length: 20 }).notNull().$type<typeof operationStatusEnum[number]>().default('PENDING'),
    details: text("details"),
    error: text("error"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
    completedAt: timestamp("completed_at"),
});

// Zod schemas for validation
export const insertOperationSchema = createInsertSchema(operations, {
    type: z.enum(operationTypeEnum),
    status: z.enum(operationStatusEnum),
});

export const selectOperationSchema = createSelectSchema(operations, {
    type: z.enum(operationTypeEnum),
    status: z.enum(operationStatusEnum),
});

// Define types for use in the application
export type Operation = z.infer<typeof selectOperationSchema>;
export type NewOperation = z.infer<typeof insertOperationSchema>;
export type OperationStatus = typeof operationStatusEnum[number];
export type OperationType = typeof operationTypeEnum[number];

import { 
  pgTable, 
  text, 
  timestamp, 
  jsonb,
  varchar, 
} from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';

// Статусы теста промпта
export const promptTestStatusEnum = ['PENDING', 'PROCESSING', 'SUCCESS', 'ERROR'] as const;

// Схема таблицы тестов промптов
export const promptTests = pgTable('prompt_tests', {
  // Уникальный идентификатор теста
  id: varchar('id', { length: 255 }).primaryKey(),
  
  // Исходный промпт
  prompt: text('prompt').notNull(),
  
  // Настройки модели в формате JSON
  settings: jsonb('settings').$type<{
    model: string;
    temperature: number;
    maxTokens: number;
  }>().notNull(),
  
  // Результат генерации
  result: jsonb('result').$type<{
    subject: string;
    body: string;
    formattedPrompt: string;
    [key: string]: any;
  }>(),
  
  // Статус выполнения теста
  status: varchar('status', { length: 20 })
    .notNull()
    .$type<typeof promptTestStatusEnum[number]>()
    .default('PENDING'),
  
  // Сообщение об ошибке при неудаче
  error: text('error'),
  
  // Время начала теста
  startedAt: timestamp('started_at').defaultNow().notNull(),
  
  // Время завершения теста
  completedAt: timestamp('completed_at'),
  
  // Время создания записи
  createdAt: timestamp('created_at').defaultNow().notNull(),
  
  // Время последнего обновления записи
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Zod-схемы для валидации данных
export const insertPromptTestSchema = createInsertSchema(promptTests, {
  status: z.enum(promptTestStatusEnum),
  settings: z.object({
    model: z.string(),
    temperature: z.number(),
    maxTokens: z.number(),
  }),
  result: z.object({
    subject: z.string(),
    body: z.string(),
    formattedPrompt: z.string(),
  }).nullable(),
});

export const selectPromptTestSchema = createSelectSchema(promptTests, {
  status: z.enum(promptTestStatusEnum),
  settings: z.object({
    model: z.string(),
    temperature: z.number(),
    maxTokens: z.number(),
  }),
  result: z.object({
    subject: z.string(),
    body: z.string(),
    formattedPrompt: z.string(),
  }).nullable(),
});

// Определяем типы для использования в приложении через Zod
export const promptTestSchema = z.object({
  id: z.string(),
  prompt: z.string(),
  settings: z.object({
    model: z.string(),
    temperature: z.number(),
    maxTokens: z.number(),
  }),
  result: z.object({
    subject: z.string(),
    body: z.string(),
    formattedPrompt: z.string(),
  }).nullable(),
  status: z.enum(promptTestStatusEnum),
  error: z.string().nullable(),
  startedAt: z.date(),
  completedAt: z.date().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Типы для использования в приложении
export type PromptTest = z.infer<typeof promptTestSchema>;
export type NewPromptTest = z.infer<typeof insertPromptTestSchema>;
export type PromptTestStatus = typeof promptTestStatusEnum[number]; 
import { InferModel, sql } from "drizzle-orm";
import {
    text,
    timestamp,
    pgTable,
    json,
    varchar,
    integer,
    serial,
} from "drizzle-orm/pg-core";

export const leads = pgTable("leads", {
    id: serial("id").primaryKey(),
    salesforceId: varchar("salesforce_id", { length: 255 }),
    firstName: varchar("first_name", { length: 255 }),
    lastName: varchar("last_name", { length: 255 }),
    email: varchar("email", { length: 255 }),
    company: text("company").notNull(),
    title: varchar("title", { length: 255 }),
    phone: varchar("phone", { length: 255 }),
    industry: varchar("industry", { length: 255 }),
    rating: varchar("rating", { length: 255 }),
    leadSource: varchar("lead_source", { length: 255 }),
    description: text("description"),
    website: varchar("website", { length: 255 }),
    numberOfEmployees: integer("number_of_employees"),
    status: text("status"),
    details: json("details").$type<any>(),
    history: json("history").$type<any>(),
    company_info: json("company_info").$type<any>(),
    communications: json("communications").$type<any>(),
    lastModifiedDate: timestamp("last_modified_date"),
    createdBy: varchar("created_by", { length: 255 }).notNull(),
    createdAt: timestamp("created_at")
        .default(sql`CURRENT_TIMESTAMP`)
        .notNull(),
    updatedAt: timestamp("updated_at")
        .default(sql`CURRENT_TIMESTAMP`)
        .notNull(),
});

export type Lead = InferModel<typeof leads>;
export type NewLead = InferModel<typeof leads, "insert">;

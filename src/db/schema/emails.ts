import { pgTable, serial, text, timestamp, boolean, varchar, integer } from "drizzle-orm/pg-core";
import { leads } from "./leads";

export const emails = pgTable("emails", {
    id: serial("id").primaryKey(),
    leadId: integer("lead_id")
        .notNull()
        .references(() => leads.id, { onDelete: "cascade" }),
    subject: varchar("subject", { length: 255 }).notNull(),
    content: text("content").notNull(),
    prompt: text("prompt").notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    createdBy: varchar("created_by", { length: 255 }).default(""),
    sent: boolean("sent").default(false).notNull(),
    sentAt: timestamp("sent_at"),
});

export const emailHistory = pgTable("email_history", {
    id: serial("id").primaryKey(),
    emailId: integer("email_id").references(() => emails.id),
    status: text("status").notNull(), // draft, sent, failed
    sentAt: timestamp("sent_at"),
    error: text("error"),
    createdAt: timestamp("created_at").defaultNow(),
});

import { pgTable, serial, text, varchar, boolean, timestamp, jsonb } from 'drizzle-orm/pg-core';

export const promptTypes = {
  SYSTEM: 'system',
  USER: 'user',
  ASSISTANT: 'assistant',
} as const;

export type PromptType = typeof promptTypes[keyof typeof promptTypes];

export interface PromptSettings {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string[];
}

export type PromptMetadata = {
  variables?: string[];
  requiredFields?: string[];
  examples?: string[];
  lastTestResult?: {
    success: boolean;
    message?: string;
    timestamp: string;
  };
  [key: string]: unknown;
};

export const prompts = pgTable('prompts', {
  id: serial('id').primaryKey(),
  title: varchar('title', { length: 255 }).notNull(),
  type: varchar('type', { length: 50 }).notNull().default(promptTypes.SYSTEM),
  content: text('content').notNull(),
  description: text('description'),
  isActive: boolean('is_active').default(true).notNull(),
  version: serial('version').notNull(),
  settings: jsonb('settings').notNull().default({}),
  metadata: jsonb('metadata').notNull().default({}),
  createdBy: varchar('created_by', { length: 255 }).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export type Prompt = {
  id: number;
  title: string;
  type: string;
  content: string;
  description: string | null;
  settings: PromptSettings;
  metadata: PromptMetadata;
  isActive: boolean;
  version: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
};

export type NewPrompt = {
  title: string;
  type: string;
  content: string;
  description: string | null;
  settings: PromptSettings;
  metadata: PromptMetadata;
  isActive?: boolean;
  version?: number;
  createdBy: string;
};
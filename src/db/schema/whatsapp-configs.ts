import { pgTable, timestamp, text, varchar } from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { users } from "./users";

export const whatsappConfigs = pgTable("whatsapp_configs", {
    id: varchar("id", { length: 191 }).primaryKey().notNull(),
    apiUrl: text("api_url").notNull(),
    profileId: text("profile_id").notNull(),
    token: text("token").notNull(),
    createdBy: varchar("created_by", { length: 255 })
        .notNull()
        .references(() => users.email),
    createdAt: timestamp("created_at")
        .notNull()
        .default(sql`CURRENT_TIMESTAMP`),
    updatedAt: timestamp("updated_at")
        .notNull()
        .default(sql`CURRENT_TIMESTAMP`),
});

import { pgTable, timestamp, text, varchar, integer } from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { users } from "./users";

export const whatsappConfigs = pgTable("whatsapp_configs", {
    id: varchar("id", { length: 191 }).primaryKey().notNull(),
    apiUrl: text("api_url").notNull(),
    profileId: text("profile_id").notNull(),
    token: text("token").notNull(),
    createdBy: integer("created_by")
        .notNull()
        .references(() => users.id, { onDelete: "cascade" }),
    createdAt: timestamp("created_at")
        .notNull()
        .default(sql`CURRENT_TIMESTAMP`),
    updatedAt: timestamp("updated_at")
        .notNull()
        .default(sql`CURRENT_TIMESTAMP`),
});

import { pgTable, serial, text, timestamp, boolean, varchar, integer } from "drizzle-orm/pg-core";
import { leads } from "./leads";

export const whatsappMessages = pgTable("whatsapp_messages", {
    id: serial("id").primaryKey(),
    leadId: integer("lead_id")
        .notNull()
        .references(() => leads.id, { onDelete: "cascade" }),
    content: text("content").notNull(),
    prompt: text("prompt").notNull(),
    phoneNumber: varchar("phone_number", { length: 20 }).notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    createdBy: varchar("created_by", { length: 255 }).default(""),
    sent: boolean("sent").default(false).notNull(),
    sentAt: timestamp("sent_at"),
});

export const whatsappHistory = pgTable("whatsapp_history", {
    id: serial("id").primary<PERSON>ey(),
    messageId: integer("message_id").references(() => whatsappMessages.id),
    status: text("status").notNull(), // draft, sent, failed
    sentAt: timestamp("sent_at"),
    error: text("error"),
    createdAt: timestamp("created_at").defaultNow(),
});

import {
    pgTable,
    serial,
    text,
    timestamp,
    boolean,
    varchar,
    jsonb,
    integer,
    uuid,
} from "drizzle-orm/pg-core";
import { prompts } from "./prompts";

export const agents = pgTable("agents", {
    id: serial("id").primary<PERSON>ey(),
    uuid: uuid("uuid").defaultRandom().notNull().unique(),
    name: varchar("name", { length: 255 }).notNull(),
    description: text("description").notNull(),
    prompt: text("prompt").notNull(),
    promptId: integer("prompt_id").references(() => prompts.id, { onDelete: "set null" }),
    settings: jsonb("settings").notNull().default({}),
    metadata: jsonb("metadata").notNull().default({}),
    isActive: boolean("is_active").default(true).notNull(),
    version: integer("version").default(1).notNull(),
    createdBy: varchar("created_by", { length: 255 }).notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

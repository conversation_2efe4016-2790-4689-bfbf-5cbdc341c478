import {
    pgTable,
    serial,
    text,
    timestamp,
    boolean,
    varchar,
    integer,
} from "drizzle-orm/pg-core";
import { users } from "./users";
import { relations } from "drizzle-orm";

export const apiTokens = pgTable("api_tokens", {
    id: serial("id").primaryKey(),
    token: text("token").notNull().unique(),
    name: varchar("name", { length: 255 }).notNull(),
    description: text("description"),
    userId: integer("user_id").references(() => users.id, { onDelete: "cascade" }).notNull(),
    isActive: boolean("is_active").default(true).notNull(),
    expiresAt: timestamp("expires_at"),
    lastUsedAt: timestamp("last_used_at"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const apiTokensRelations = relations(apiTokens, ({ one }) => ({
    user: one(users, {
        fields: [apiTokens.userId],
        references: [users.id],
    }),
})); 
import { drizzle } from "drizzle-orm/node-postgres";
import { Pool } from "pg";
import * as schema from "./schema";

if (!process.env.DATABASE_URL) {
    throw new Error("DATABASE_URL is not set");
}

// Create a persistent database instance
const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: {
        rejectUnauthorized: true,
    },
});

// // Initialize the database
// export const initDB = async () => {
//   try {
//     // Create tables if they don't exist
//     await pool.query(`
//       CREATE TABLE IF NOT EXISTS leads (
//         id SERIAL PRIMARY KEY,
//         salesforce_id VARCHAR(18) UNIQUE NOT NULL,
//         first_name TEXT,
//         last_name TEXT,
//         email TEXT,
//         company TEXT,
//         title TEXT,
//         phone TEXT,
//         status TEXT,
//         industry TEXT,
//         rating TEXT,
//         lead_source TEXT,
//         description TEXT,
//         website TEXT,
//         number_of_employees INTEGER,
//         last_modified_date TIMESTAMP,
//         created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
//         updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
//       );

//       CREATE TABLE IF NOT EXISTS prompts (
//         id SERIAL PRIMARY KEY,
//         title TEXT NOT NULL,
//         content TEXT NOT NULL,
//         created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
//         updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
//       );

//       CREATE TABLE IF NOT EXISTS emails (
//         id SERIAL PRIMARY KEY,
//         lead_id VARCHAR(18) NOT NULL REFERENCES leads(sf_id),
//         subject TEXT NOT NULL,
//         content TEXT NOT NULL,
//         sent BOOLEAN DEFAULT FALSE,
//         created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
//         updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
//         prompt_id INTEGER REFERENCES prompts(id)
//       );

//       CREATE TABLE IF NOT EXISTS email_history (
//         id SERIAL PRIMARY KEY,
//         email_id INTEGER REFERENCES emails(id),
//         status TEXT NOT NULL,
//         sent_at TIMESTAMP,
//         error TEXT,
//         created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
//       );
//     `);

//     console.log("Database initialized successfully");
//   } catch (error) {
//     console.error("Error initializing database:", error);
//     throw error;
//   }
// };

export const db = drizzle(pool, { schema });

// Helper function to test the database connection
export const testConnection = async () => {
    try {
        const client = await pool.connect();
        console.log("Successfully connected to the database");
        client.release();
        return true;
    } catch (error) {
        console.error("Error connecting to the database:", error);
        return false;
    }
};

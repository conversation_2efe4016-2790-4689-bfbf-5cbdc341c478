import { db } from "./index";
import { sql } from "drizzle-orm";
import { PgTransaction } from "drizzle-orm/pg-core";

/**
 * Тип для транзакции, чтобы избежать циклической зависимости
 */
type DatabaseTransaction = PgTransaction<any, any, any>;

/**
 * Функция для выполнения произвольных SQL-миграций
 */
export async function migrate(migration: (tx: DatabaseTransaction) => Promise<void>): Promise<void> {
  try {
    // Начинаем транзакцию для безопасного выполнения миграции
    await db.transaction(async (tx) => {
      await migration(tx);
    });
  } catch (error) {
    console.error("Ошибка при выполнении миграции:", error);
    throw error;
  }
}

/**
 * Функция для проверки существования колонки в таблице
 */
export async function columnExists(tableName: string, columnName: string): Promise<boolean> {
  const result = await db.execute(sql`
    SELECT EXISTS (
      SELECT 1 
      FROM information_schema.columns 
      WHERE table_name = ${tableName} AND column_name = ${columnName}
    );
  `);
  
  return result.rows[0]?.exists === true;
}

/**
 * Функция для проверки существования таблицы
 */
export async function tableExists(tableName: string): Promise<boolean> {
  const result = await db.execute(sql`
    SELECT EXISTS (
      SELECT 1 
      FROM information_schema.tables 
      WHERE table_name = ${tableName}
    );
  `);
  
  return result.rows[0]?.exists === true;
} 
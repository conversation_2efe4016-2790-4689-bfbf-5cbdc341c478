import { sql } from "drizzle-orm";
import { migrate } from "../migrator";

export async function createOperationsTable() {
  await migrate(async (db) => {
    // Check if the table already exists
    const tableCheck = await db.execute(sql`
      SELECT EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_name = 'operations'
      );
    `);
    
    if (tableCheck.rows.length === 0 || !tableCheck.rows[0].exists) {
      // Create the operations table
      await db.execute(sql`
        CREATE TABLE operations (
          id SERIAL PRIMARY KEY,
          uuid UUID DEFAULT gen_random_uuid() NOT NULL UNIQUE,
          type VARCHAR(50) NOT NULL,
          status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
          details TEXT,
          error TEXT,
          created_at TIMESTAMP NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
          completed_at TIMESTAMP
        );
        
        -- Create index for faster lookups by uuid
        CREATE INDEX idx_operations_uuid ON operations(uuid);
        
        -- Create index for faster lookups by status
        CREATE INDEX idx_operations_status ON operations(status);
        
        -- Create index for faster lookups by type
        CREATE INDEX idx_operations_type ON operations(type);
      `);
    }
  });
  
  console.log("Migration completed successfully: Operations table created");
}

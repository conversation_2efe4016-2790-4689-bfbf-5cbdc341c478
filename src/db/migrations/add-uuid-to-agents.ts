import { sql } from "drizzle-orm";
import { migrate } from "../migrator";

export async function addUuidToAgents() {
  await migrate(async (db) => {
    // Добавление расширения uuid-ossp, если оно ещё не установлено
    await db.execute(sql`
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    `);
    
    // Проверяем наличие колонки uuid
    const columnCheck = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'agents' AND column_name = 'uuid';
    `);
    
    if (columnCheck.rows.length === 0) {
      // Добавление колонки uuid к таблице agents
      await db.execute(sql`
        ALTER TABLE agents
        ADD COLUMN uuid UUID DEFAULT uuid_generate_v4() NOT NULL;
      `);
      
      // Проверяем наличие дубликатов (это может происходить при генерации UUID для существующих записей)
      const duplicateCheck = await db.execute(sql`
        SELECT uuid, COUNT(*) 
        FROM agents 
        GROUP BY uuid 
        HAVING COUNT(*) > 1;
      `);
      
      if (duplicateCheck.rows.length > 0) {
        // Если найдены дубликаты, обновляем их значения
        await db.execute(sql`
          UPDATE agents
          SET uuid = uuid_generate_v4()
          WHERE id IN (
            SELECT id FROM agents a
            JOIN (
              SELECT uuid FROM agents
              GROUP BY uuid
              HAVING COUNT(*) > 1
            ) d ON a.uuid = d.uuid
            ORDER BY id
            OFFSET 1
          );
        `);
      }
      
      // Добавляем ограничение уникальности, если его еще нет
      await db.execute(sql`
        ALTER TABLE agents
        ADD CONSTRAINT agents_uuid_unique UNIQUE (uuid);
      `);
    }
  });
  
  console.log("Миграция успешно выполнена: UUID добавлен к агентам");
} 
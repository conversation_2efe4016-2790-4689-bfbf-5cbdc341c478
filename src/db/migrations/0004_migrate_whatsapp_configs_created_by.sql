-- Migration: Change whatsapp_configs.created_by from users.email to users.id
-- This migration will:
-- 1. Add a new column created_by_id (integer) that references users.id
-- 2. Populate it with user IDs based on existing emails
-- 3. Drop the old created_by column
-- 4. <PERSON><PERSON> created_by_id to created_by

BEGIN;

-- Step 1: Add new column created_by_id
ALTER TABLE whatsapp_configs 
ADD COLUMN created_by_id INTEGER;

-- Step 2: Populate created_by_id with user IDs based on existing emails
UPDATE whatsapp_configs 
SET created_by_id = users.id 
FROM users 
WHERE whatsapp_configs.created_by = users.email;

-- Step 3: Check if all records were updated (optional verification)
-- If any records have NULL created_by_id, they reference non-existent users
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM whatsapp_configs WHERE created_by_id IS NULL) THEN
        RAISE EXCEPTION 'Some whatsapp_configs records reference non-existent users. Migration aborted.';
    END IF;
END $$;

-- Step 4: Make created_by_id NOT NULL and add foreign key constraint
ALTER TABLE whatsapp_configs 
ALTER COLUMN created_by_id SET NOT NULL;

ALTER TABLE whatsapp_configs 
ADD CONSTRAINT whatsapp_configs_created_by_id_users_id_fk 
FOREIGN KEY (created_by_id) REFERENCES users(id) ON DELETE CASCADE;

-- Step 5: Drop the old foreign key constraint and column
ALTER TABLE whatsapp_configs 
DROP CONSTRAINT whatsapp_configs_created_by_users_email_fk;

ALTER TABLE whatsapp_configs 
DROP COLUMN created_by;

-- Step 6: Rename created_by_id to created_by
ALTER TABLE whatsapp_configs 
RENAME COLUMN created_by_id TO created_by;

-- Step 7: Update index if it exists
DROP INDEX IF EXISTS idx_whatsapp_configs_created_by;
CREATE INDEX idx_whatsapp_configs_created_by ON whatsapp_configs(created_by);

COMMIT;

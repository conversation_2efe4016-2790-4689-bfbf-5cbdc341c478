import { db } from "../index";
import { sql } from "drizzle-orm";

export async function createWhatsAppTables() {
  try {
    console.log("Creating WhatsApp tables...");

    // Create whatsapp_messages table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS whatsapp_messages (
        id SERIAL PRIMARY KEY,
        lead_id INTEGER NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
        content TEXT NOT NULL,
        prompt TEXT NOT NULL,
        phone_number VARCHAR(20) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
        created_by VARCHAR(255) DEFAULT '',
        sent B<PERSON><PERSON>EA<PERSON> DEFAULT FALSE NOT NULL,
        sent_at TIMESTAMP
      );
    `);

    // Create whatsapp_history table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS whatsapp_history (
        id SERIAL PRIMARY KEY,
        message_id INTEGER REFERENCES whatsapp_messages(id),
        status TEXT NOT NULL,
        sent_at TIMESTAMP,
        error TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create whatsapp_configs table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS whatsapp_configs (
        id VARCHAR(191) PRIMARY KEY NOT NULL,
        api_url TEXT NOT NULL,
        profile_id TEXT NOT NULL,
        token TEXT NOT NULL,
        created_by VARCHAR(255) NOT NULL REFERENCES users(email),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
      );
    `);

    // Create whatsapp_logs table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS whatsapp_logs (
        id SERIAL PRIMARY KEY,
        message_id INTEGER NOT NULL REFERENCES whatsapp_messages(id) ON DELETE CASCADE,
        raw_prompt TEXT NOT NULL,
        formatted_prompt TEXT NOT NULL,
        variables JSONB NOT NULL,
        ai_response JSONB NOT NULL,
        execution_time_ms INTEGER,
        model_name TEXT,
        prompt_tokens INTEGER,
        completion_tokens INTEGER,
        total_tokens INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
      );
    `);

    // Create indexes for better performance
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_lead_id ON whatsapp_messages(lead_id);
    `);

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_phone_number ON whatsapp_messages(phone_number);
    `);

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_whatsapp_history_message_id ON whatsapp_history(message_id);
    `);

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_whatsapp_logs_message_id ON whatsapp_logs(message_id);
    `);

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_whatsapp_configs_created_by ON whatsapp_configs(created_by);
    `);

    console.log("WhatsApp tables created successfully!");
  } catch (error) {
    console.error("Error creating WhatsApp tables:", error);
    throw error;
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  createWhatsAppTables()
    .then(() => {
      console.log("Migration completed successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Migration failed:", error);
      process.exit(1);
    });
}

import { sql } from "drizzle-orm";
import { migrate } from "../migrator";

export async function createApiTokensTable() {
  await migrate(async (db) => {
    // Проверяем, существует ли таблица api_tokens
    const tableCheck = await db.execute(sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name = 'api_tokens';
    `);
    
    if (tableCheck.rows.length === 0) {
      // Создание таблицы api_tokens
      await db.execute(sql`
        CREATE TABLE api_tokens (
          id SERIAL PRIMARY KEY,
          token TEXT NOT NULL UNIQUE,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          is_active BOOLEAN NOT NULL DEFAULT TRUE,
          expires_at TIMESTAMP,
          last_used_at TIMESTAMP,
          created_at TIMESTAMP NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMP NOT NULL DEFAULT NOW()
        );
        
        -- Создание индекса для быстрого поиска по токену
        CREATE INDEX idx_api_tokens_token ON api_tokens(token);
        
        -- Создание индекса для быстрого поиска по user_id
        CREATE INDEX idx_api_tokens_user_id ON api_tokens(user_id);
      `);
    }
  });
  
  console.log("Миграция успешно выполнена: Таблица api_tokens создана");
} 
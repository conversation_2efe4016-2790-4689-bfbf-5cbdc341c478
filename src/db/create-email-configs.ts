import { drizzle } from "drizzle-orm/postgres-js";
import { sql } from "drizzle-orm";
import postgres from "postgres";
import * as dotenv from "dotenv";
import path from "path";

// Load environment variables from .env file
dotenv.config({ path: path.resolve(process.cwd(), ".env") });

async function createEmailConfigsTable() {
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
        throw new Error("DATABASE_URL is not set in .env file");
    }

    const connection = postgres(databaseUrl, { ssl: { rejectUnauthorized: true } });
    const db = drizzle(connection);

    try {
        await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "email_configs" (
        "id" varchar(191) PRIMARY KEY NOT NULL,
        "smtp" text NOT NULL,
        "port" integer NOT NULL,
        "email" text NOT NULL,
        "password" text NOT NULL,
        "created_by" varchar(191) NOT NULL,
        "created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
        "updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
      );
    `);
    } catch (error) {
        console.error("Error creating email configs table:", error);
    } finally {
        await connection.end();
    }
}

createEmailConfigsTable().catch(console.error);

import { 
  parseWhatsAppPlaceholders, 
  normalizeWhatsAppPromptVariables,
  detectUnknownWhatsAppPlaceholders,
  validateWhatsAppTemplate,
  WhatsAppContext 
} from "../utils/whatsapp-placeholder-parser";

console.log("=== Testing WhatsApp Placeholder Parsing ===\n");

// Mock lead data for testing
const mockLead = {
  id: 123,
  firstName: "Иван",
  lastName: "Петров",
  email: "<EMAIL>",
  company: "TechCorp",
  title: "CTO",
  phone: "79115576367",
  industry: "IT",
  website: "https://techcorp.ru",
  status: "New",
  description: "Опытный технический директор",
  details: {
    city: "Москва",
    country: "Россия",
    linkedin: "https://linkedin.com/in/ivanpetrov"
  },
  communications: {
    linkedin: "https://linkedin.com/in/ivanpetrov",
    email: "<EMAIL>",
    phone: "79115576367",
    telegram: "@ivanpetrov"
  },
  company_info: {
    name: "TechCorp",
    description: "Инновационная IT компания",
    industry: "Software Development",
    size: "50-100",
    website: "https://techcorp.ru",
    location: "Москва, Россия"
  }
} as any;

const whatsappContext: WhatsAppContext = {
  lead: mockLead,
  senderName: "Анна Смирнова",
  senderCompany: "SalesFlow",
  senderPosition: "Sales Manager"
};

console.log("1. Mock Lead Data:");
console.log(`   Name: ${mockLead.firstName} ${mockLead.lastName}`);
console.log(`   Company: ${mockLead.company}`);
console.log(`   City: ${mockLead.details.city}`);
console.log(`   LinkedIn: ${mockLead.communications.linkedin}`);

console.log("\n2. Testing Basic Placeholders:");

const basicTemplate = `Здравствуйте, {{leadFirstName}} {{leadLastName}}!

Я заметил, что вы работаете в {{leadCompany}} в должности {{leadTitle}}.

С уважением,
{{senderName}}`;

const basicResult = parseWhatsAppPlaceholders(basicTemplate, whatsappContext);
console.log("Template:");
console.log(basicTemplate);
console.log("\nResult:");
console.log(basicResult);

console.log("\n3. Testing JSON Field Placeholders:");

const jsonTemplate = `Привет {{leadFirstName}}! 👋

Мы нашли ваш профиль в {{communications_linkedin}} и заметили, что вы находитесь в городе {{details_city}}.

Ваша компания {{company_info_name}} занимается {{company_info_industry}}, что очень интересно!

Хотели бы обсудить возможности сотрудничества.

{{senderName}} из {{senderCompany}}`;

const jsonResult = parseWhatsAppPlaceholders(jsonTemplate, whatsappContext);
console.log("Template:");
console.log(jsonTemplate);
console.log("\nResult:");
console.log(jsonResult);

console.log("\n4. Testing Normalization:");

const unnormalizedTemplate = `Привет {{lead.firstName}}!

Работаете в {{lead.company}}?

{{manager.name}} из {{manager.company}}`;

const normalizedTemplate = normalizeWhatsAppPromptVariables(unnormalizedTemplate);
console.log("Original:");
console.log(unnormalizedTemplate);
console.log("\nNormalized:");
console.log(normalizedTemplate);
console.log("\nParsed:");
console.log(parseWhatsAppPlaceholders(normalizedTemplate, whatsappContext));

console.log("\n5. Testing Unknown Placeholders Detection:");

const templateWithUnknown = `Привет {{leadFirstName}}!

Ваш {{unknownField}} и {{anotherUnknown}} интересны.

{{validPlaceholder}} - {{leadCompany}}`;

const unknownPlaceholders = detectUnknownWhatsAppPlaceholders(templateWithUnknown);
console.log("Template with unknown placeholders:");
console.log(templateWithUnknown);
console.log("\nUnknown placeholders found:");
console.log(unknownPlaceholders);

console.log("\n6. Testing Template Validation:");

const validationResult = validateWhatsAppTemplate(templateWithUnknown);
console.log("Validation result:");
console.log(`   Valid: ${validationResult.isValid}`);
console.log(`   Unknown placeholders: ${validationResult.unknownPlaceholders.join(", ")}`);
console.log(`   Warnings: ${validationResult.warnings.join(", ")}`);

console.log("\n7. Testing Missing Data Handling:");

const leadWithMissingData = {
  ...mockLead,
  firstName: "",
  details: {},
  communications: {},
  company_info: null
} as any;

const contextWithMissingData: WhatsAppContext = {
  lead: leadWithMissingData,
  senderName: "Анна Смирнова"
};

const missingDataResult = parseWhatsAppPlaceholders(jsonTemplate, contextWithMissingData);
console.log("Template with missing data:");
console.log(jsonTemplate);
console.log("\nResult (missing data handled):");
console.log(missingDataResult);

console.log("\n8. Testing Complex Nested Data:");

const complexLead = {
  ...mockLead,
  details: {
    city: "Санкт-Петербург",
    country: "Россия",
    address: "Невский проспект, 123",
    social: {
      linkedin: "https://linkedin.com/in/complex",
      facebook: "https://facebook.com/complex"
    }
  }
} as any;

const complexContext: WhatsAppContext = {
  lead: complexLead,
  senderName: "Мария Иванова"
};

const complexTemplate = `{{leadFirstName}}, привет!

Вы из {{details_city}}, {{details_country}}?
Адрес: {{details_address}}

LinkedIn: {{details_linkedin}}`;

const complexResult = parseWhatsAppPlaceholders(complexTemplate, complexContext);
console.log("Complex template:");
console.log(complexTemplate);
console.log("\nComplex result:");
console.log(complexResult);

console.log("\n=== All Placeholder Tests Completed ===");

console.log("\n9. Available Placeholders Reference:");
console.log("Basic Lead Info:");
console.log("   {{leadFirstName}}, {{leadLastName}}, {{leadFullName}}");
console.log("   {{leadCompany}}, {{leadTitle}}, {{leadEmail}}, {{leadPhone}}");
console.log("   {{leadIndustry}}, {{leadWebsite}}, {{leadStatus}}");

console.log("\nSender Info:");
console.log("   {{senderName}}, {{senderCompany}}, {{senderPosition}}");

console.log("\nJSON Fields - Details:");
console.log("   {{details_city}}, {{details_country}}, {{details_region}}");
console.log("   {{details_address}}, {{details_linkedin}}");

console.log("\nJSON Fields - Communications:");
console.log("   {{communications_linkedin}}, {{communications_email}}");
console.log("   {{communications_phone}}, {{communications_telegram}}");

console.log("\nJSON Fields - Company Info:");
console.log("   {{company_info_name}}, {{company_info_description}}");
console.log("   {{company_info_industry}}, {{company_info_size}}");

import { validatePhoneNumber } from "../utils/phone-validation";

// Test phone number validation
console.log("Testing phone number validation:");

const testNumbers = [
  "79115576367",
  "89115576367", 
  "9115576367",
  "+79115576367",
  "7 911 557 63 67",
  "8 (911) 557-63-67",
  "invalid",
  "",
  "123456789",
];

testNumbers.forEach(number => {
  const result = validatePhoneNumber(number);
  console.log(`${number} -> ${result.isValid ? result.formatted : result.error}`);
});

console.log("\nWhatsApp functionality is ready for testing!");
console.log("To test the full functionality:");
console.log("1. Set up environment variables in .env:");
console.log("   WHATSAPP_API_URL=https://your-api-url.com");
console.log("   WHATSAPP_PROFILE_ID=your_profile_id");
console.log("   WHATSAPP_TOKEN=your_token");
console.log("2. Run the database migration");
console.log("3. Start the development server");
console.log("4. Navigate to the leads page and try sending WhatsApp messages");

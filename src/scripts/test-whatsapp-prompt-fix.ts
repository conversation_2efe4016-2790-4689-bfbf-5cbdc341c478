import { 
  parseWhatsAppPlaceholders, 
  WhatsAppContext 
} from "../utils/whatsapp-placeholder-parser";

console.log("=== Testing WhatsApp Prompt Fix ===\n");

// Mock lead data with missing fields (like the real case)
const mockLeadWithMissingData = {
  id: 1030,
  firstName: "Николай",
  lastName: "Хотеев",
  email: "niko<PERSON>@example.com",
  company: "Таттелеком",
  title: "CTO",
  phone: "77055102891",
  industry: "Telecommunications",
  website: null,
  status: "New",
  description: null,
  details: {}, // Empty details - no city
  communications: {}, // Empty communications - no linkedin
  company_info: {
    name: "Таттелеком",
    description: "Телекоммуникационная компания",
    industry: "Telecommunications"
  }
} as any;

const whatsappContext: WhatsAppContext = {
  lead: mockLeadWithMissingData,
  senderName: "SalesFlow Team",
  senderCompany: "SalesFlow",
  senderPosition: "Sales Manager"
};

console.log("1. Testing Original Problematic Template:");

const problematicTemplate = `Здравствуйте, {{leadFirstName}} {{leadLastName}}, 

Я заметил, что вы работаете в {{leadCompany}} и находитесь в городе {{details_city}}.

Мы нашли ваш профиль в {{communications_linkedin}} и хотели бы предложить вам наши услуги.

С уважением,
{{senderName}}`;

console.log("Original Template:");
console.log(problematicTemplate);

const problematicResult = parseWhatsAppPlaceholders(problematicTemplate, whatsappContext);
console.log("\nResult BEFORE fix:");
console.log(problematicResult);

console.log("\n" + "=".repeat(60) + "\n");

console.log("2. Testing Improved Template (without empty fields):");

const improvedTemplate = `Привет {{leadFirstName}}! 👋

Заметил, что вы работаете в {{leadCompany}}{{#if details_city}} в городе {{details_city}}{{/if}}.

{{#if communications_linkedin}}Нашел ваш профиль в LinkedIn - впечатляющий опыт!{{/if}}

Хотел бы обсудить, как {{senderCompany}} может помочь автоматизировать ваши продажи.

Есть 5 минут для быстрого звонка? 📞

{{senderName}}`;

console.log("Improved Template (conditional logic):");
console.log(improvedTemplate);

// For now, let's test a simpler version without conditional logic
const simpleImprovedTemplate = `Привет {{leadFirstName}}! 👋

Заметил, что вы работаете в {{leadCompany}}. Впечатляющая компания!

Хотел бы обсудить, как {{senderCompany}} может помочь автоматизировать ваши продажи.

Есть 5 минут для быстрого звонка? 📞

{{senderName}}`;

console.log("\nSimple Improved Template (no empty fields):");
console.log(simpleImprovedTemplate);

const improvedResult = parseWhatsAppPlaceholders(simpleImprovedTemplate, whatsappContext);
console.log("\nResult AFTER improvement:");
console.log(improvedResult);

console.log("\n" + "=".repeat(60) + "\n");

console.log("3. Testing New Default WhatsApp Prompt:");

const newDefaultPrompt = `
Ты - профессиональный менеджер по продажам, который пишет WhatsApp сообщение потенциальному клиенту.

ВАЖНО: Ты должен создать сообщение ОТ ИМЕНИ отправителя ДЛЯ получателя, а НЕ ответ от получателя!

Информация о лиде (получателе):
- Имя: {{leadFirstName}} {{leadLastName}}
- Компания: {{leadCompany}}
- Должность: {{leadTitle}}
- Отрасль: {{leadIndustry}}
- Город: {{details_city}}
- LinkedIn: {{communications_linkedin}}

Информация об отправителе (тебе):
- Имя: {{senderName}}
- Компания: {{senderCompany}}
- Должность: {{senderPosition}}

Инструкции:
1. Создай персонализированное WhatsApp сообщение (максимум 300 символов)
2. Пиши от первого лица (от имени {{senderName}})
3. Будь дружелюбным и разговорным
4. Включи четкий призыв к действию
5. Используй эмодзи уместно
6. Сохраняй профессионализм, но будь неформальным
7. Упоминай конкретные детали о лиде, если они доступны
8. НЕ упоминай детали, которые отсутствуют (пустые поля)

Формат ответа:
Message: [Твое WhatsApp сообщение здесь]
`;

console.log("New Default Prompt:");
console.log(newDefaultPrompt);

const newPromptResult = parseWhatsAppPlaceholders(newDefaultPrompt, whatsappContext);
console.log("\nNew Prompt After Placeholder Parsing:");
console.log(newPromptResult);

console.log("\n" + "=".repeat(60) + "\n");

console.log("4. Testing Cleanup Function with Various Cases:");

const testCases = [
  {
    name: "Empty city",
    template: "Вы работаете в {{leadCompany}} и находитесь в городе {{details_city}}.",
    expected: "Вы работаете в Таттелеком."
  },
  {
    name: "Empty LinkedIn",
    template: "Мы нашли ваш профиль в {{communications_linkedin}} и хотели бы связаться.",
    expected: "Мы нашли ваш профиль и хотели бы связаться."
  },
  {
    name: "Multiple empty fields",
    template: "{{leadFirstName}} из {{leadCompany}} в {{details_city}} с профилем {{communications_linkedin}}.",
    expected: "Николай из Таттелеком с профилем."
  }
];

testCases.forEach((testCase, index) => {
  console.log(`\nTest Case ${index + 1}: ${testCase.name}`);
  console.log(`Template: ${testCase.template}`);
  
  const result = parseWhatsAppPlaceholders(testCase.template, whatsappContext);
  console.log(`Result: ${result}`);
  console.log(`Expected: ${testCase.expected}`);
  console.log(`✅ Cleaned: ${result !== testCase.template}`);
});

console.log("\n" + "=".repeat(60) + "\n");

console.log("5. Expected Claude Response with New Prompt:");

console.log(`
С новым промптом Claude должен генерировать что-то вроде:

Message: Привет Николай! 👋

Заметил, что вы работаете в Таттелеком - впечатляющая телекоммуникационная компания!

Хотел бы обсудить, как SalesFlow может помочь автоматизировать ваши продажи.

Есть 5 минут для быстрого звонка? 📞

SalesFlow Team

ВМЕСТО предыдущего ответа от лица лида.
`);

console.log("\n=== Key Improvements ===");
console.log("✅ Четкие инструкции для Claude о том, от чьего имени писать");
console.log("✅ Указание НЕ упоминать отсутствующие данные");
console.log("✅ Автоматическая очистка фраз с пустыми плейсхолдерами");
console.log("✅ Русскоязычный промпт для лучшего понимания");
console.log("✅ Акцент на создании сообщения ОТ ИМЕНИ отправителя");

console.log("\n=== WhatsApp Prompt Fix Testing Complete ===");

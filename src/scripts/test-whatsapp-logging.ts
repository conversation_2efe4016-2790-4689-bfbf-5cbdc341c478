import { WhatsAppLogger, LogContext, LLMRequestParams, LLMResponse, MessageProcessing } from "../utils/whatsapp-logger";

console.log("=== Testing WhatsApp Service Logging ===\n");

// Mock data for testing
const mockLogContext: LogContext = {
  leadId: 123,
  leadName: "Иван Петров",
  leadCompany: "TechCorp",
  agentId: 5,
  agentName: "WhatsApp Sales Agent",
  sessionId: "whatsapp_123_1703123456789"
};

console.log("1. Testing Session Start/End Logging:");

// Test session start
WhatsAppLogger.logSessionStart(mockLogContext, "MESSAGE_GENERATION");

// Simulate some processing time
await new Promise(resolve => setTimeout(resolve, 100));

// Test session end (success)
WhatsAppLogger.logSessionEnd(mockLogContext, "MESSAGE_GENERATION", true, 1250);

console.log("\n" + "=".repeat(50) + "\n");

// Test session end (failure)
WhatsAppLogger.logSessionEnd(mockLogContext, "MESSAGE_GENERATION", false, 850);

console.log("\n2. Testing Prompt Processing Logging:");

const originalPrompt = `
You are a WhatsApp sales expert. Create a personalized message for:

Lead: {{leadFirstName}} {{leadLastName}}
Company: {{leadCompany}}
City: {{details_city}}
LinkedIn: {{communications_linkedin}}

Sender: {{senderName}} from {{senderCompany}}
`;

const finalPrompt = `
You are a WhatsApp sales expert. Create a personalized message for:

Lead: Иван Петров
Company: TechCorp
City: Москва
LinkedIn: https://linkedin.com/in/ivanpetrov

Sender: Анна Смирнова from SalesFlow
`;

const placeholdersFound = ["leadFirstName", "leadLastName", "leadCompany", "details_city", "communications_linkedin", "senderName", "senderCompany"];

WhatsAppLogger.logPromptProcessing(mockLogContext, originalPrompt, finalPrompt, placeholdersFound);

console.log("\n3. Testing LLM Request Logging:");

const llmParams: LLMRequestParams = {
  model: "claude-3-sonnet-20240229",
  maxTokens: 1000,
  temperature: 0.2,
  promptLength: finalPrompt.length
};

WhatsAppLogger.logLLMRequest(mockLogContext, llmParams);

console.log("\n4. Testing LLM Response Logging:");

const llmResponse: LLMResponse = {
  content: `Message: Привет Иван! 👋

Заметил, что вы работаете в TechCorp в Москве. Нашел ваш профиль в LinkedIn - впечатляющий опыт!

Хотел бы обсудить, как SalesFlow может помочь автоматизировать ваши продажи. 

Есть 5 минут для быстрого звонка? 📞

Анна Смирнова
SalesFlow Team`,
  usage: {
    input_tokens: 245,
    output_tokens: 89,
    total_tokens: 334
  },
  executionTimeMs: 1850
};

WhatsAppLogger.logLLMResponse(mockLogContext, llmResponse);

console.log("\n5. Testing Message Processing Logging:");

const messageProcessing: MessageProcessing = {
  rawResponse: llmResponse.content,
  processedContent: llmResponse.content.replace(/^Message:\s*/i, "").trim(),
  finalMessage: `Привет Иван! 👋

Заметил, что вы работаете в TechCorp в Москве. Нашел ваш профиль в LinkedIn - впечатляющий опыт!

Хотел бы обсудить, как SalesFlow может помочь автоматизировать ваши продажи. 

Есть 5 минут для быстрого звонка? 📞

Анна Смирнова
SalesFlow Team`,
  placeholdersReplaced: 7
};

WhatsAppLogger.logMessageProcessing(mockLogContext, messageProcessing);

console.log("\n6. Testing Success Logging:");

WhatsAppLogger.logGenerationSuccess(mockLogContext, 456, "79115576367", 3200);

console.log("\n7. Testing Error Logging:");

const testError = new Error("Failed to connect to Anthropic API");
testError.stack = `Error: Failed to connect to Anthropic API
    at WhatsAppService.generatePersonalizedMessage (/app/src/services/whatsapp.ts:180:15)
    at async POST (/app/src/app/api/whatsapp/route.ts:45:28)`;

WhatsAppLogger.logGenerationError(mockLogContext, testError, "llm_request");

console.log("\n8. Testing Phone Validation Logging:");

const phoneValidationSuccess = {
  isValid: true,
  formatted: "+79115576367",
};

const phoneValidationFailure = {
  isValid: false,
  formatted: "",
  error: "Invalid phone number format"
};

WhatsAppLogger.logPhoneValidation(mockLogContext, "79115576367", phoneValidationSuccess);
WhatsAppLogger.logPhoneValidation(mockLogContext, "invalid-phone", phoneValidationFailure);

console.log("\n9. Testing Agent Retrieval Logging:");

const agentData = {
  id: 5,
  name: "WhatsApp Sales Agent",
  prompt: "You are a WhatsApp sales expert...",
  settings: {
    model: "claude-3-sonnet-20240229",
    temperature: 0.2,
    maxTokens: 1000
  }
};

WhatsAppLogger.logAgentRetrieval(mockLogContext, 5, true, agentData);
WhatsAppLogger.logAgentRetrieval(mockLogContext, 999, false);

console.log("\n10. Testing Database Operations Logging:");

WhatsAppLogger.logDatabaseOperation(mockLogContext, "INSERT", "whatsappMessages", true, {
  messageId: 456,
  contentLength: 245,
  phoneNumber: "+79115576367"
});

WhatsAppLogger.logDatabaseOperation(mockLogContext, "UPDATE", "whatsappMessages", false, {
  messageId: 456,
  error: "Database connection timeout"
});

console.log("\n11. Testing Sending Operations Logging:");

WhatsAppLogger.logSendingAttempt(mockLogContext, 456, "79115576367", "config_123");

const apiResponse = {
  status: "success",
  messageId: "wa_msg_789",
  timestamp: "2024-01-15T10:30:00Z"
};

WhatsAppLogger.logSendingSuccess(mockLogContext, 456, "79115576367", apiResponse);

const sendingError = new Error("WhatsApp API rate limit exceeded");
const errorApiResponse = {
  status: "error",
  error: "Rate limit exceeded",
  retryAfter: 60
};

WhatsAppLogger.logSendingError(mockLogContext, 456, "79115576367", sendingError, errorApiResponse);

console.log("\n=== All Logging Tests Completed ===");

console.log("\n12. Testing Privacy Features:");
console.log("Phone number masking examples:");
console.log("- Original: 79115576367");
console.log("- Masked: 79*******67");
console.log("- Original: +1234567890");
console.log("- Masked: +1*******90");

console.log("\n13. Log Structure Benefits:");
console.log("✅ Structured prefixes for easy filtering");
console.log("✅ Timestamps for chronological tracking");
console.log("✅ Context information for traceability");
console.log("✅ Data truncation for readability");
console.log("✅ Privacy protection (masked phone numbers)");
console.log("✅ Error details with stack traces");
console.log("✅ Performance metrics (execution times)");
console.log("✅ Token usage statistics");
console.log("✅ Session boundaries for complete flows");

console.log("\n14. Monitoring Use Cases:");
console.log("📊 Performance monitoring - track LLM response times");
console.log("🔍 Debugging - trace complete message generation flow");
console.log("📈 Analytics - analyze token usage and costs");
console.log("🚨 Error tracking - identify failure patterns");
console.log("👥 User behavior - understand agent usage patterns");
console.log("🔒 Security - monitor API access and phone number handling");
console.log("💰 Cost optimization - track token consumption per lead");

console.log("\n=== WhatsApp Service Logging Ready for Production ===");

import { Metadata } from 'next'
import { CodeBlock } from '@/components/docs/CodeBlock';

export const metadata: Metadata = {
  title: 'API Documentation | Agent Service',
  description: 'API documentation for the Agent service endpoints',
}

// Примеры кода как константы для лучшей читаемости
const REQUEST_EXAMPLE = `POST /api/v1/agent
Content-Type: application/json
Authorization: Bearer your_api_token

{
  "agentId": "550e8400-e29b-41d4-a716-446655440000",
  "leadData": {
    "name": "Александр Петров",
    "company": "ТехноСофт",
    "position": "CTO",
    "email": "<EMAIL>",
    "phone": "+7 (999) 123-45-67",
    "linkedIn": "https://linkedin.com/in/apetrov"
  },
  "leadHistory": [
    {
      "date": "2024-03-15",
      "type": "website_visit",
      "description": "Посетил страницу продукта и просмотрел демо"
    }
  ],
  "managerInfo": {
    "name": "Мария Иванова",
    "position": "Account Executive",
    "company": "SalesTech Solutions"
  },
  "productInfo": {
    "name": "AI Sales Assistant Pro",
    "description": "Платформа автоматизации продаж на базе ИИ",
    "benefits": [
      "Увеличение конверсии на 35%",
      "Экономия времени менеджеров до 4 часов в день",
      "Персонализированная коммуникация с клиентами"
    ]
  },
  "emailType": "initial_outreach",
  "tone": "professional",
  "language": "ru",
}`;

const RESPONSE_EXAMPLE = `{
  "success": true,
  "email": {
    "subject": "Оптимизация продаж в ТехноСофт с помощью AI Sales Assistant Pro",
    "body": "Добрый день, Александр!..."
  },
  "metadata": {
    "tokensUsed": 842,
    "processingTime": "1.2s",
  }
}`;

const AGENT_SETTINGS_EXAMPLE = `{
  "settings": {
    "model": "anthropic/claude-3.5-haiku-********",
    "maxTokens": 4000,
    "temperature": 0.7,
    "llmProvider": "anthropic"
  }
}`;

const ERROR_RESPONSES_EXAMPLE = `// 401 Unauthorized
{
  "error": "Отсутствует или некорректный токен авторизации"
}

// 400 Bad Request
{
  "error": "Ошибка валидации данных",
  "details": {
    "url": ["Требуется действительный URL"]
  }
}

// 500 Internal Server Error
{
  "error": "Ошибка при извлечении данных",
  "details": "Failed to fetch URL content"
}`;

// Добавляем новые примеры для enrich endpoint
const ENRICH_REQUEST_EXAMPLE = `POST /api/v1/agent/enrich
Content-Type: application/json
Authorization: Bearer your_api_token

{
  "url": "https://example.com/profile"
}`;

const ENRICH_RESPONSE_EXAMPLE = `{
  "success": true,
  "data": {
    "title": "Profile Page Title",
    "content": "Extracted content from the URL...",
    "metadata": {
      "author": "John Doe",
      "publishDate": "2024-01-01"
    }
  },
  "requestId": "550e8400-e29b-41d4-a716-446655440000"
}`;

export default function ApiDocsPage() {
  return (
    <>
      <script async src="https://unpkg.com/prismjs@1.29.0/components/prism-core.min.js" />
      <script async src="https://unpkg.com/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js" />
      <link 
        href="https://unpkg.com/prismjs@1.29.0/themes/prism-tomorrow.min.css" 
        rel="stylesheet" 
      />
      
      <div className="max-w-5xl mx-auto py-8 px-4">
        <h1 className="text-3xl font-bold mb-8">API Documentation</h1>
        
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Email Generation API</h2>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-xl font-medium mb-4">POST /api/v1/agent</h3>
            
            <div className="mb-6">
              <h4 className="font-medium mb-2">Description</h4>
              <p className="text-gray-600">
                Generates an email using AI based on the provided agent configuration and lead data.
              </p>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Authentication</h4>
              <p className="text-gray-600">
                Requires API token in the Authorization header:
                <code className="block bg-gray-100 p-2 mt-2 rounded">
                  Authorization: Bearer your_api_token
                </code>
              </p>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Request Body</h4>
              <CodeBlock 
                code={`{
  "agentId": "uuid",
  "leadData": {
    "name": "string",
    "company": "string",
    "position": "string",
    "email": "string",
    "phone": "string?",
    "linkedIn": "string?"
  },
  "leadHistory": [
    {
      "date": "string",
      "type": "string",
      "description": "string"
    }
  ],
  "managerInfo": {
    "name": "string",
    "position": "string",
    "company": "string"
  },
  "productInfo": {
    "name": "string",
    "description": "string",
    "benefits": "string[]"
  },
  "emailType": "initial_outreach" | "follow_up" | "meeting_request" | "custom",
  "customInstructions": "string?",
  "tone": "formal" | "friendly" | "persuasive" | "professional",
  "language": "ru" | "en",
  "settings": {
    "model": "string",
    "maxTokens": "number",
    "temperature": "number"
  }
}`}
                language="json"
              />
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Response</h4>
              <CodeBlock 
                code={`{
  "success": true,
  "email": {
    "subject": "string",
    "body": "string"
  }
}`}
                language="json"
              />
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Example Request & Response</h4>
              
              <div className="space-y-4">
                <div>
                  <h5 className="font-medium mb-2">Request</h5>
                  <CodeBlock code={REQUEST_EXAMPLE} language="json" />
                </div>

                <div>
                  <h5 className="font-medium mb-2">Response</h5>
                  <CodeBlock code={RESPONSE_EXAMPLE} language="json" />
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Error Responses</h4>
              <div className="space-y-4">
                <div>
                  <h5 className="text-sm text-gray-600">401 Unauthorized</h5>
                  <CodeBlock 
                    code={`{
  "error": "Invalid or missing API token"
}`} 
                    language="json" 
                  />
                </div>
                
                <div>
                  <h5 className="text-sm text-gray-600">400 Bad Request</h5>
                  <CodeBlock 
                    code={`{
  "error": "Validation error",
  "details": {
    "leadData": ["Email is required"],
    "emailType": ["Invalid email type specified"]
  }
}`}
                    language="json"
                  />
                </div>

                <div>
                  <h5 className="text-sm text-gray-600">429 Too Many Requests</h5>
                  <CodeBlock 
                    code={`{
  "error": "Rate limit exceeded",
  "retryAfter": 60
}`}
                    language="json"
                  />
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Example Requests</h4>
              
              <div className="space-y-4">
                <div>
                  <h5 className="font-medium mb-2">cURL</h5>
                  <CodeBlock 
                    code={`curl -X POST \\
  'https://your-domain.com/api/v1/agent' \\
  -H 'Authorization: Bearer your_api_token' \\
  -H 'Content-Type: application/json' \\
  -d '{
    "agentId": "550e8400-e29b-41d4-a716-446655440000",
    "leadData": {
      "name": "John Doe",
      "company": "Acme Corp",
      "position": "CEO",
      "email": "<EMAIL>"
    },
    "emailType": "initial_outreach",
    "tone": "professional",
    "language": "en"
  }'`}
                    language="bash"
                  />
                </div>

                <div>
                  <h5 className="font-medium mb-2">Node.js</h5>
                  <CodeBlock 
                    code={`const response = await fetch('https://your-domain.com/api/v1/agent', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_api_token',
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    agentId: '550e8400-e29b-41d4-a716-446655440000',
    leadData: {
      name: 'John Doe',
      company: 'Acme Corp',
      position: 'CEO',
      email: '<EMAIL>',
    },
    emailType: 'initial_outreach',
    tone: 'professional',
    language: 'en',
  }),
});

const data = await response.json();`}
                    language="javascript"
                  />
                </div>

                <div>
                  <h5 className="font-medium mb-2">Python</h5>
                  <CodeBlock 
                    code={`import requests

response = requests.post(
    'https://your-domain.com/api/v1/agent',
    headers={
        'Authorization': 'Bearer your_api_token',
        'Content-Type': 'application/json',
    },
    json={
        'agentId': '550e8400-e29b-41d4-a716-446655440000',
        'leadData': {
            'name': 'John Doe',
            'company': 'Acme Corp',
            'position': 'CEO',
            'email': '<EMAIL>',
        },
        'emailType': 'initial_outreach',
        'tone': 'professional',
        'language': 'en',
    }
)

data = response.json()`}
                    language="python"
                  />
                </div>

                <div>
                  <h5 className="font-medium mb-2">PHP</h5>
                  <CodeBlock 
                    code={`<?php
$curl = curl_init();

curl_setopt_array($curl, [
    CURLOPT_URL => 'https://your-domain.com/api/v1/agent',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_HTTPHEADER => [
        'Authorization: Bearer your_api_token',
        'Content-Type: application/json',
    ],
    CURLOPT_POSTFIELDS => json_encode([
        'agentId' => '550e8400-e29b-41d4-a716-446655440000',
        'leadData' => [
            'name' => 'John Doe',
            'company' => 'Acme Corp',
            'position' => 'CEO',
            'email' => '<EMAIL>',
        ],
        'emailType' => 'initial_outreach',
        'tone' => 'professional',
        'language' => 'en',
    ]),
]);

$response = curl_exec($curl);
$data = json_decode($response, true);

curl_close($curl);`}
                    language="php"
                  />
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Available Settings</h4>
              <p className="text-gray-600 mb-2">
                You can customize the AI model behavior using the following settings:
              </p>
              <CodeBlock code={AGENT_SETTINGS_EXAMPLE} language="json" />
              <ul className="list-disc list-inside mt-2 text-gray-600">
                <li>model - AI model to use for generation</li>
                <li>maxTokens - maximum number of tokens in the response</li>
                <li>temperature - controls randomness (0.0 - 1.0)</li>
                <li>llmProvider - provider of the AI model</li>
              </ul>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Error Handling</h4>
              <p className="text-gray-600 mb-2">
                The API returns appropriate HTTP status codes and error messages:
              </p>
              <CodeBlock code={ERROR_RESPONSES_EXAMPLE} language="json" />
            </div>
          </div>
        </section>

        {/* Добавляем новую секцию для URL Enrichment API */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">URL Enrichment API</h2>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-xl font-medium mb-4">POST /api/v1/agent/enrich</h3>
            
            <div className="mb-6">
              <h4 className="font-medium mb-2">Description</h4>
              <p className="text-gray-600">
                Extracts and enriches content from a provided URL using AI-powered analysis.
              </p>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Authentication</h4>
              <p className="text-gray-600">
                Requires API token in the Authorization header:
                <code className="block bg-gray-100 p-2 mt-2 rounded">
                  Authorization: Bearer your_api_token
                </code>
              </p>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Request Body</h4>
              <CodeBlock 
                code={`{
  "url": "string" // Valid URL to extract content from
}`}
                language="json"
              />
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Response</h4>
              <CodeBlock 
                code={`{
  "success": true,
  "data": {
    "title": "string",
    "content": "string",
    "metadata": object
  },
  "requestId": "string"
}`}
                language="json"
              />
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Example Request & Response</h4>
              <div className="space-y-4">
                <div>
                  <h5 className="font-medium mb-2">Request</h5>
                  <CodeBlock code={ENRICH_REQUEST_EXAMPLE} language="json" />
                </div>
                <div>
                  <h5 className="font-medium mb-2">Response</h5>
                  <CodeBlock code={ENRICH_RESPONSE_EXAMPLE} language="json" />
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Error Responses</h4>
              <div className="space-y-4">
                <div>
                  <h5 className="text-sm text-gray-600">400 Bad Request</h5>
                  <CodeBlock 
                    code={`{
  "error": "Ошибка валидации данных",
  "details": {
    "url": ["Требуется действительный URL"]
  }
}`}
                    language="json"
                  />
                </div>
                <div>
                  <h5 className="text-sm text-gray-600">401 Unauthorized</h5>
                  <CodeBlock 
                    code={`{
  "error": "Отсутствует или некорректный токен авторизации"
}`}
                    language="json"
                  />
                </div>
                <div>
                  <h5 className="text-sm text-gray-600">500 Internal Server Error</h5>
                  <CodeBlock 
                    code={`{
  "error": "Ошибка при извлечении данных",
  "details": "Failed to fetch URL content"
}`}
                    language="json"
                  />
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Rate Limiting</h4>
              <p className="text-gray-600">
                This endpoint is subject to rate limiting. When exceeded, it will return a 429 status code with a retry-after header.
              </p>
            </div>
          </div>
        </section>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Best Practices</h2>
          <div className="bg-white rounded-lg shadow p-6">
            <ul className="list-disc list-inside space-y-2 text-gray-600">
              <li>Always include proper authentication headers</li>
              <li>Use appropriate temperature settings based on your needs:
                <ul className="list-circle list-inside ml-4">
                  <li>0.3-0.5 for more focused responses</li>
                  <li>0.6-0.8 for creative content</li>
                </ul>
              </li>
              <li>Handle rate limits and implement proper error handling</li>
              <li>Cache enrichment API responses when possible</li>
            </ul>
          </div>
        </section>
      </div>
    </>
  );
}

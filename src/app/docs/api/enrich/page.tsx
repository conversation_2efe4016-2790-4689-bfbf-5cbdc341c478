import { Metadata } from 'next'
import { CodeBlock } from '@/components/docs/CodeBlock';

export const metadata: Metadata = {
  title: 'Lead Enrichment API | SalesFlow',
  description: 'API documentation for the Lead Enrichment endpoints',
}

// Code examples as constants for better readability
const ENRICH_REQUEST_EXAMPLE = `POST /api/v1/leads/enrich
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN

{
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "company": "Example Corp",
  "communications": {
    "linkedin": "https://linkedin.com/in/johndoe"
  }
}`;

const COMPLETE_REQUEST_EXAMPLE = `{
  "first_name": "<PERSON>",                      // Lead_First_Name
  "last_name": "<PERSON><PERSON>",                        // Lead_Last_Name
  "email": "<EMAIL>",           // Lead_Email
  "company": "Example Corp",                 // Lead_Company
  "description": "Met at conference. Interested in our enterprise solution.",
  "communications": {
    // Social media and contact information
    "facebook": "https://facebook.com/johndoe",  // Lead_Facebook
    "telegram": "@johndoe",                      // Lead telegram
    "vkontakte": "https://vk.com/johndoe"        // Lead_VK
  },
  "details": {
    "linkedin_info": {
      "url": "https://linkedin.com/in/johndoe",  // Lead_LinkedIn
      "content": "<PERSON>e is the CEO of Example Corp, a technology company specializing in AI solutions. He has over 15 years of experience in the tech industry."  // Lead_Description
    }
  },
  "lang": "ru",                              // Language for enrichment (default: "ru")
  "webhook_url": "https://example.com/webhook"
}`;

const ENRICH_RESPONSE_EXAMPLE = `{
  "success": true,
  "uuid": "123e4567-e89b-12d3-a456-************",
  "operation": {
    "uuid": "123e4567-e89b-12d3-a456-************",
    "type": "LEAD_ENRICHMENT",
    "status": "PENDING",
    "createdAt": "2023-01-01T00:00:00.000Z"
  },
  "lead": {
    "id": 123
  }
}`;

const OPERATION_STATUS_EXAMPLE = `{
  "success": true,
  "operation": {
    "uuid": "123e4567-e89b-12d3-a456-************",
    "type": "LEAD_ENRICHMENT",
    "status": "completed",
    "details": "...",
    "error": null,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:01:00.000Z",
    "completedAt": "2023-01-01T00:01:00.000Z"
  }
}`;

const LEAD_DATA_EXAMPLE = `{
  "success": true,
  "lead": {
    "id": 123,
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "company": "Example Corp",
    "title": "CEO",
    "description": "John Doe is the CEO of Example Corp, a technology company...",
    "communications": {
      "linkedin": "https://linkedin.com/in/johndoe"
    },
    "details": {
      "linkedin_info": {
        "url": "https://linkedin.com/in/johndoe",
        "content": "LinkedIn profile content"
      }
    }
  }
}`;

const POLLING_EXAMPLE = `async function pollOperationStatus(operationUuid) {
  const maxAttempts = 20; // Poll for up to 5 minutes (15s * 20)
  let attempts = 0;

  while (attempts < maxAttempts) {
    const response = await fetch(\`https://app.salesflow.team/api/v1/operations/\${operationUuid}\`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer YOUR_API_TOKEN'
      }
    });

    const data = await response.json();

    if (data.operation.status === 'completed' ||
        data.operation.status === 'ERROR' ||
        data.operation.status === 'CANCELLED') {
      return data;
    }

    // Wait for 15 seconds before next poll
    await new Promise(resolve => setTimeout(resolve, 15000));
    attempts++;
  }

  throw new Error('Polling timed out');
}`;

const CURL_EXAMPLE = `curl -X POST \\
  https://app.salesflow.team/api/v1/leads/enrich \\
  -H 'Content-Type: application/json' \\
  -H 'Authorization: Bearer YOUR_API_TOKEN' \\
  -d '{
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "company": "Example Corp",
    "communications": {
      "linkedin": "https://linkedin.com/in/johndoe"
    }
  }'`;

const NODE_EXAMPLE = `const response = await fetch('https://app.salesflow.team/api/v1/leads/enrich', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_API_TOKEN'
  },
  body: JSON.stringify({
    first_name: 'John',
    last_name: 'Doe',
    email: '<EMAIL>',
    company: 'Example Corp',
    communications: {
      linkedin: 'https://linkedin.com/in/johndoe'
    }
  })
});

const data = await response.json();
console.log(data);`;

const PYTHON_EXAMPLE = `import requests
import json

url = "https://app.salesflow.team/api/v1/leads/enrich"

payload = json.dumps({
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "company": "Example Corp",
  "communications": {
    "linkedin": "https://linkedin.com/in/johndoe"
  }
})
headers = {
  'Content-Type': 'application/json',
  'Authorization': 'Bearer YOUR_API_TOKEN'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)`;

const PHP_EXAMPLE = `<?php
$curl = curl_init();

curl_setopt_array($curl, [
  CURLOPT_URL => "https://app.salesflow.team/api/v1/leads/enrich",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 30,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "POST",
  CURLOPT_POSTFIELDS => json_encode([
    "first_name" => "John",
    "last_name" => "Doe",
    "email" => "<EMAIL>",
    "company" => "Example Corp",
    "communications" => [
      "linkedin" => "https://linkedin.com/in/johndoe"
    ]
  ]),
  CURLOPT_HTTPHEADER => [
    "Authorization: Bearer YOUR_API_TOKEN",
    "Content-Type: application/json"
  ],
]);

$response = curl_exec($curl);
$err = curl_error($curl);

curl_close($curl);

if ($err) {
  echo "cURL Error #:" . $err;
} else {
  echo $response;
}
?>`;

export default function EnrichApiDocsPage() {
  return (
    <>
      <script async src="https://unpkg.com/prismjs@1.29.0/components/prism-core.min.js" />
      <script async src="https://unpkg.com/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js" />
      <link
        href="https://unpkg.com/prismjs@1.29.0/themes/prism-tomorrow.min.css"
        rel="stylesheet"
      />

      <div className="max-w-5xl mx-auto py-8 px-4">
        <h1 className="text-3xl font-bold mb-8">Lead Enrichment API Documentation</h1>

        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">Overview</h2>
          <div className="bg-white rounded-lg shadow p-6">
            <p className="text-gray-600 mb-4">
              The Lead Enrichment API allows you to create or update leads and enrich them with additional data.
              The enrichment process follows this flow:
            </p>

            <ol className="list-decimal list-inside space-y-2 text-gray-600 ml-4">
              <li><strong>Create Lead & Start Enrichment</strong> - <code className="bg-gray-100 px-1 py-0.5 rounded">POST /api/v1/leads/enrich</code></li>
              <li><strong>Check Operation Status</strong> - <code className="bg-gray-100 px-1 py-0.5 rounded">GET /api/v1/operations/:uuid</code></li>
              <li><strong>Retrieve Enriched Lead Data</strong> - <code className="bg-gray-100 px-1 py-0.5 rounded">GET /api/v1/leads/:id</code></li>
            </ol>
          </div>
        </section>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Create Lead & Start Enrichment</h2>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-xl font-medium mb-4">POST /api/v1/leads/enrich</h3>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Description</h4>
              <p className="text-gray-600">
                Initiates the lead enrichment process by creating or updating a lead and sending it to the enrichment service.
                If a lead with the same email already exists, it will be updated instead of creating a new one.
              </p>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Language Parameter</h4>
              <p className="text-gray-600">
                The <code>lang</code> field specifies the language for the enrichment process:
              </p>
              <ul className="list-disc list-inside text-gray-600 ml-4 mt-2">
                <li><strong>Supported values</strong>: <code>&quot;ru&quot;</code> (Russian), <code>&quot;en&quot;</code> (English)</li>
                <li><strong>Default</strong>: <code>&quot;ru&quot;</code> (Russian)</li>
                <li><strong>Usage</strong>: This parameter is passed to the external enrichment service to determine the language for processing and generating enriched content.</li>
              </ul>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Authentication</h4>
              <p className="text-gray-600">
                Requires API token in the Authorization header:
                <code className="block bg-gray-100 p-2 mt-2 rounded">
                  Authorization: Bearer YOUR_API_TOKEN
                </code>
              </p>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Example Request & Response</h4>

              <div className="space-y-4">
                <div>
                  <h5 className="font-medium mb-2">Basic Request</h5>
                  <CodeBlock code={ENRICH_REQUEST_EXAMPLE} language="json" />
                </div>

                <div>
                  <h5 className="font-medium mb-2">Complete Request with All Possible Parameters</h5>
                  <p className="text-gray-600 mb-2">
                    This example shows all possible parameters that can be included in the request:
                  </p>
                  <CodeBlock code={COMPLETE_REQUEST_EXAMPLE} language="json" />
                </div>

                <div>
                  <h5 className="font-medium mb-2">Response</h5>
                  <CodeBlock code={ENRICH_RESPONSE_EXAMPLE} language="json" />
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Example Implementations</h4>

              <div className="space-y-4">
                <div>
                  <h5 className="font-medium mb-2">cURL</h5>
                  <CodeBlock code={CURL_EXAMPLE} language="bash" />
                </div>

                <div>
                  <h5 className="font-medium mb-2">Node.js</h5>
                  <CodeBlock code={NODE_EXAMPLE} language="javascript" />
                </div>

                <div>
                  <h5 className="font-medium mb-2">Python</h5>
                  <CodeBlock code={PYTHON_EXAMPLE} language="python" />
                </div>

                <div>
                  <h5 className="font-medium mb-2">PHP</h5>
                  <CodeBlock code={PHP_EXAMPLE} language="php" />
                </div>
              </div>
            </div>
          </div>
        </section>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Check Operation Status</h2>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-xl font-medium mb-4">GET /api/v1/operations/:uuid</h3>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Description</h4>
              <p className="text-gray-600">
                Checks the status of a lead enrichment operation. The UUID is returned from the enrichment request.
              </p>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Response</h4>
              <CodeBlock code={OPERATION_STATUS_EXAMPLE} language="json" />
              <p className="text-gray-600 mt-2">
                The <code>status</code> field can be one of: <code>&quot;PENDING&quot;</code>, <code>&quot;PROCESSING&quot;</code>, <code>&quot;completed&quot;</code> (for success), <code>&quot;ERROR&quot;</code>, or <code>&quot;CANCELLED&quot;</code>.
              </p>
            </div>
          </div>
        </section>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Retrieve Enriched Lead Data</h2>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-xl font-medium mb-4">GET /api/v1/leads/:id</h3>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Description</h4>
              <p className="text-gray-600">
                Retrieves complete lead data by lead ID, including any enriched information.
                The lead ID is returned in the initial enrichment response.
              </p>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Response</h4>
              <CodeBlock code={LEAD_DATA_EXAMPLE} language="json" />
            </div>
          </div>
        </section>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Polling for Operation Status</h2>
          <div className="bg-white rounded-lg shadow p-6">
            <p className="text-gray-600 mb-4">
              To implement polling for lead enrichment status, check the operation status every 15 seconds until it becomes &apos;completed&apos;:
            </p>

            <CodeBlock code={POLLING_EXAMPLE} language="javascript" />
          </div>
        </section>
      </div>
    </>
  );
}

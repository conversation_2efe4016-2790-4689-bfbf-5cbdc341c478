"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";

export default function TestFunctions() {
  const [result, setResult] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [leadId, setLeadId] = useState<string>("");

  const handleTest = async (endpoint: string) => {
    setLoading(true);
    try {
      const response = await fetch(endpoint);
      const data = await response.json();
      setResult(JSON.stringify(data, null, 2));
    } catch (error) {
      setResult(error instanceof Error ? error.message : "An error occurred");
    }
    setLoading(false);
  };

  const handleLeadTest = async (endpoint: string) => {
    if (!leadId) {
      setResult("Please enter a lead ID");
      return;
    }
    setLoading(true);
    try {
      const response = await fetch(`${endpoint}?salesforceId=${leadId}`);
      const data = await response.json();
      setResult(JSON.stringify(data, null, 2));
    } catch (error) {
      setResult(error instanceof Error ? error.message : "An error occurred");
    }
    setLoading(false);
  };

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">Test Functions</h1>

      <div className="mb-6">
        <Input
          type="text"
          placeholder="Enter Lead ID"
          value={leadId}
          onChange={(e) => setLeadId(e.target.value)}
          className="max-w-md"
        />
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Salesforce Functions</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-wrap gap-4">
            <Button
              onClick={() => handleTest("/api/test/leads")}
              disabled={loading}
              variant="default"
            >
              Get Leads
            </Button>
            <Button
              onClick={() => handleLeadTest("/api/test/leads")}
              disabled={loading}
              variant="default"
            >
              Get Lead by ID
            </Button>
            <Button
              onClick={() => handleLeadTest("/api/test/leads/history")}
              disabled={loading}
              variant="default"
            >
              Get Lead History
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Email Functions</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-wrap gap-4">
            <Button
              onClick={() => handleLeadTest("/api/test/email/generate")}
              disabled={loading}
              variant="secondary"
            >
              Generate Email
            </Button>
            <Button
              onClick={() => handleLeadTest("/api/test/email/send")}
              disabled={loading}
              variant="secondary"
            >
              Send Email
            </Button>
          </CardContent>
        </Card>
      </div>

      {loading && (
        <div className="mt-6">
          <Skeleton className="h-[200px] w-full" />
        </div>
      )}

      {result && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Result</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertDescription>
                <pre className="whitespace-pre-wrap overflow-auto max-h-[400px]">
                  {result}
                </pre>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

"use client";

import { useEffect, useState, useCallback } from "react";
import { LeadsTable } from "@/components/leads/LeadsTable";
import { LeadForm } from "@/components/leads/LeadForm";
import { Lead } from "@/db/schema/leads";
import { LeadsService } from "@/services/leads";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { Search, MessageCircle } from "lucide-react";
import { SendEmailModal } from "@/components/leads/SendEmailModal";
import { SendWhatsAppModal } from "@/components/leads/SendWhatsAppModal";

// Define the form data type to match the LeadForm's schema
type LeadFormData = {
  firstName: string;
  lastName: string;
  email: string;
  company?: string;
  title?: string;
  phone?: string;
  status?: string;
  industry?: string;
  website?: string;
};

export default function LeadsPage() {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [search, setSearch] = useState("");
  const [selectedLeads, setSelectedLeads] = useState<number[]>([]);
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const [isWhatsAppModalOpen, setIsWhatsAppModalOpen] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 1
  });
  const { toast } = useToast();

  const fetchLeads = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await LeadsService.getLeads({
        page,
        sortBy,
        sortOrder,
        limit: 10,
        search
      });
      setLeads(data.leads);
      setPagination(data.pagination);
    } catch (error: any) {
      console.error("Error fetching leads:", error);
      setError(error.message || "Failed to fetch leads");
    } finally {
      setIsLoading(false);
    }
  }, [page, sortBy, sortOrder, search]);

  useEffect(() => {
    fetchLeads();
  }, [page, sortBy, sortOrder, search, fetchLeads]);

  const handleCreate = async (formData: LeadFormData) => {
    if (!formData.company) {
      toast({
        title: "Error",
        description: "Company is required",
        variant: "destructive",
      });
      return;
    }

    try {
      // Add required fields and transform to match the expected type
      const leadData = {
        ...formData,
        company: formData.company,
        createdBy: "system", // You might want to get this from your auth context
        firstName: formData.firstName || null,
        lastName: formData.lastName || null,
        email: formData.email || null,
        title: formData.title || null,
        phone: formData.phone || null,
        industry: formData.industry || null,
        status: formData.status || null,
        website: formData.website || null,
      };

      await LeadsService.createLead(leadData);
      await fetchLeads();
      setIsCreating(false);
      toast({
        title: "Success",
        description: "Lead created successfully",
      });
    } catch (_error) {
      toast({
        title: "Error",
        description: "Failed to create lead",
        variant: "destructive",
      });
    }
  };

  const handleUpdate = async (id: number, data: Partial<Lead>) => {
    try {
      await LeadsService.updateLead(id, data);
      await fetchLeads();
      toast({
        title: "Success",
        description: "Lead updated successfully",
      });
    } catch (_error) {
      toast({
        title: "Error",
        description: "Failed to update lead",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (id: number) => {
    try {
      await LeadsService.deleteLead(id);
      await fetchLeads();
      toast({
        title: "Success",
        description: "Lead deleted successfully",
      });
    } catch (_error) {
      toast({
        title: "Error",
        description: "Failed to delete lead",
        variant: "destructive",
      });
    }
  };

  const handleSearch = (value: string) => {
    setSearch(value);
    setPage(1); // Reset to first page when searching
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button variant="default" onClick={() => setIsCreating(true)}>
            Create Lead
          </Button>
          {selectedLeads.length > 0 && (
            <Button 
              variant="outline" 
              onClick={() => setSelectedLeads([])}
            >
              Clear Selection ({selectedLeads.length})
            </Button>
          )}
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="default"
            onClick={() => setIsEmailModalOpen(true)}
            disabled={selectedLeads.length === 0}
          >
            Send Email {selectedLeads.length > 0 && `(${selectedLeads.length})`}
          </Button>
          <Button
            variant="outline"
            onClick={() => setIsWhatsAppModalOpen(true)}
            disabled={selectedLeads.length === 0}
            className="flex items-center gap-2"
          >
            <MessageCircle className="w-4 h-4" />
            Send WhatsApp {selectedLeads.length > 0 && `(${selectedLeads.length})`}
          </Button>
        </div>
      </div>

      <div className="relative mb-4">
        <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search leads..."
          value={search}
          onChange={(e) => handleSearch(e.target.value)}
          className="pl-10"
        />
      </div>

      {error ? (
        <div className="text-red-500 mb-4">{error}</div>
      ) : (
        <LeadsTable
          leads={leads}
          isLoading={isLoading}
          pagination={pagination}
          selectedLeads={selectedLeads}
          onUpdate={handleUpdate}
          onDelete={handleDelete}
          onSort={(newSortBy, newSortOrder) => {
            setSortBy(newSortBy);
            setSortOrder(newSortOrder);
          }}
          onPageChange={setPage}
          onSelectionChange={setSelectedLeads}
        />
      )}

      <Dialog open={isCreating} onOpenChange={setIsCreating}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Lead</DialogTitle>
            <DialogDescription>
              Add a new lead to your pipeline.
            </DialogDescription>
          </DialogHeader>
          <LeadForm onSubmit={handleCreate} />
        </DialogContent>
      </Dialog>

      <SendEmailModal
        isOpen={isEmailModalOpen}
        onClose={() => setIsEmailModalOpen(false)}
        selectedLeads={selectedLeads}
      />

      <SendWhatsAppModal
        isOpen={isWhatsAppModalOpen}
        onClose={() => setIsWhatsAppModalOpen(false)}
        selectedLeadIds={selectedLeads}
      />
    </div>
  );
}

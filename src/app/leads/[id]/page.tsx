"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import {
  Mail,
  Send,
  Trash2,
  FileEdit,
  Loader2,
  ArrowLeft,
  ChevronDown,
  ChevronUp,
  RefreshCw,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSession } from "next-auth/react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { AgentSelect } from "@/components/agents/AgentSelect";
import { useToast } from "@/components/ui/use-toast";
import { LeadCommunicationsHistory } from "@/components/leads/LeadCommunicationsHistory";

interface Lead {
  id: number;
  salesforceId: string;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  company: string | null;
  title: string | null;
  phone: string | null;
  status: string | null;
  industry: string | null;
  rating: string | null;
  leadSource: string | null;
  description: string | null;
  website: string | null;
  numberOfEmployees: number | null;
  lastModifiedDate: string | null;
  communications: {
    facebook?: string;
    linkedin?: string;
    telegram?: string;
    vkontakte?: string;
    last_activity?: string;
    other_contact?: string;
    other_website?: string;
    contact_history?: string;
    [key: string]: any;
  } | null;
  history: any | null;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  details: {
    bitrix_id?: string;
    middle_name?: string;
    birth_date?: string;
    role?: string;
    contact_type?: string;
    tags?: string;
    city?: string;
    products?: string;
    projects?: string;
    mailing?: string;
    language?: string;
    tech_field?: string;
    export?: string;
    commented?: string;
    linkedin_info?: {
      content?: string;
      raw_content?: string;
      url?: string;
      extractedAt?: string;
      title?: string;
      summary?: string;
    };
    facebook_info?: {
      content?: string;
      raw_content?: string;
    };
    [key: string]: any;
  } | null;
  company_info: {
    name?: string;
    website?: string;
    industry?: string | null;
    [key: string]: any;
  } | null;
}

interface Email {
  id: number;
  subject: string;
  content: string;
  prompt: string;
  sent: boolean;
  createdAt: string;
}

interface EmailConfig {
  id: string;
  smtp: string;
  port: number;
  email: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export default function LeadPage() {
  const router = useRouter();
  const params = useParams();
  const [lead, setLead] = useState<Lead | null>(null);
  const [emails, setEmails] = useState<Email[]>([]);
  const [loading, setLoading] = useState(false);
  const [emailConfigs, setEmailConfigs] = useState<EmailConfig[]>([]);
  const [selectedEmailConfig, setSelectedEmailConfig] = useState<string>("");
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const [emailToSend, setEmailToSend] = useState<number | null>(null);
  const [editingEmail, setEditingEmail] = useState<number | null>(null);
  const [editedSubject, setEditedSubject] = useState("");
  const [editedContent, setEditedContent] = useState("");
  const [selectedAgent, setSelectedAgent] = useState<any | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isEnriching, setIsEnriching] = useState(false);
  const [enrichmentStatus, setEnrichmentStatus] = useState<string>("");
  const [extraPrompt, setExtraPrompt] = useState<string>("");
  const session = useSession();
  const { toast } = useToast();
  const [showPromptMap, setShowPromptMap] = useState<Record<number, boolean>>({});

  useEffect(() => {
    fetchLead();
    fetchEmailConfigs();
  }, [params.id]);

  useEffect(() => {
    if (lead) {
      fetchEmails(lead.id.toString());
    }
  }, [lead]);

  const fetchLead = async () => {
    try {
      const response = await fetch(`/api/leads/${params.id}`);
      const data = await response.json();
      setLead(data);
    } catch (error) {
      console.error("Error fetching lead:", error);
      toast({
        title: "Error",
        description: "Failed to fetch lead details",
        variant: "destructive",
      });
    }
  };

  const fetchEmailConfigs = async () => {
    try {
      if (session.data?.user?.email) {
        const response = await fetch("/api/v1/settings/email");
        const data = await response.json();
        setEmailConfigs(Array.isArray(data) ? data : []);
      } else {
        setEmailConfigs([]);
      }
    } catch (error) {
      console.error("Error fetching email configs:", error);
      setEmailConfigs([]);
    }
  };

  const fetchEmails = async (leadId: string) => {
    try {
      const response = await fetch(`/api/emails?leadId=${leadId}`);
      const data = await response.json();
      const sortedEmails = Array.isArray(data) ?
        data.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()) :
        [];
      setEmails(sortedEmails);
    } catch (error) {
      console.error("Error fetching emails:", error);
      setEmails([]);
    }
  };

  const handleSendClick = (emailId: number) => {
    setEmailToSend(emailId);
    setShowEmailDialog(true);
  };

  const sendEmail = async (emailId?: number) => {
    setLoading(true);
    try {
      if (session.data?.user?.email) {
        const response = await fetch("/api/test/email/send", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            emailId: emailToSend,
            emailConfigId: selectedEmailConfig,
          }),
        });

        if (!response.ok) throw new Error("Failed to send email");

        setEmails((prev) =>
          prev.map((email) =>
            email.id === emailToSend ? { ...email, sent: true } : email
          )
        );

        setShowEmailDialog(false);
        setSelectedEmailConfig("");
        setEmailToSend(null);
      } else {
        const response = await fetch("/api/test/email/send", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            emailId,
          }),
        });

        if (!response.ok) throw new Error("Failed to send email");

        setEmails((prev) =>
          prev.map((email) =>
            email.id === emailId ? { ...email, sent: true } : email
          )
        );
      }

      toast({
        title: "Success",
        description: "Email sent successfully",
      });
    } catch (error) {
      console.error("Error sending email:", error);
      toast({
        title: "Error",
        description: "Failed to send email",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditClick = (email: Email) => {
    setEditingEmail(email.id);
    setEditedSubject(email.subject);
    setEditedContent(email.content);
  };

  const handleSaveEdit = async (emailId: number) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/emails/${emailId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subject: editedSubject,
          content: editedContent,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update email');
      }

      const updatedEmail = await response.json();
      setEmails(emails.map(email =>
        email.id === emailId ? updatedEmail : email
      ).sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));
      setEditingEmail(null);

      toast({
        title: "Success",
        description: "Email updated successfully",
      });
    } catch (error) {
      console.error('Error updating email:', error);
      toast({
        title: "Error",
        description: "Failed to update email",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingEmail(null);
    setEditedSubject("");
    setEditedContent("");
  };

  const handleDeleteEmail = async (emailId: number) => {
    if (!confirm("Are you sure you want to delete this email?")) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/emails/${emailId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete email');
      }

      setEmails(emails.filter(email => email.id !== emailId));
      toast({
        title: "Success",
        description: "Email deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting email:', error);
      toast({
        title: "Error",
        description: "Failed to delete email",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateEmail = async () => {
    if (!lead || !selectedAgent) {
      toast({
        title: "Error",
        description: "Please select an agent to generate email",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsGenerating(true);
      const response = await fetch("/api/v1/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          leadId: lead.id,
          agentId: selectedAgent.id,
          extraPrompt,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Error generating email");
      }

      const generatedEmail = await response.json();
      setEmails((prevEmails) => [generatedEmail, ...prevEmails]);

      toast({
        title: "Success",
        description: "Email generated successfully",
      });
    } catch (error) {
      console.error("Error generating email:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error generating email",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Функция для переключения видимости промпта
  const togglePromptVisibility = (emailId: number) => {
    setShowPromptMap((prev) => ({
      ...prev,
      [emailId]: !prev[emailId],
    }));
  };

  // Функция для вставки переменной в текстовое поле промпта
  const insertVariable = (variable: string) => {
    setExtraPrompt((prev) => prev + ` ${variable}`);
  };

  // Function to poll operation status
  const pollOperationStatus = async (uuid: string) => {
    // Set a polling interval of 15 seconds
    const pollingInterval = 15000;

    // Create a polling function
    const checkStatus = async () => {
      try {
        setEnrichmentStatus(`Checking operation status...`);
        const response = await fetch(`/api/v1/operations?uuid=${uuid}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch operation status');
        }

        const data = await response.json();
        const operation = data.operation;

        // Update status based on operation status
        setEnrichmentStatus(`Operation status: ${operation.status}`);

        // Check if operation is completed
        if (operation.status === 'completed' || operation.status === 'SUCCESS') {
          // Operation is completed, data is already updated in the database
          setEnrichmentStatus('Refreshing lead data...');

          // Refresh lead data from the server
          await fetchLead();

          toast({
            title: "Success",
            description: "Lead description enriched successfully",
          });

          // End polling
          setEnrichmentStatus('');
          setIsEnriching(false);
          return true;
        } else if (operation.status === 'error' || operation.status === 'ERROR') {
          // Handle error
          toast({
            title: "Error",
            description: operation.error || "Failed to enrich lead",
            variant: "destructive",
          });

          // End polling
          setEnrichmentStatus('');
          setIsEnriching(false);
          return true;
        }

        // Continue polling
        return false;
      } catch (error) {
        console.error('Error polling operation status:', error);
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to check operation status",
          variant: "destructive",
        });

        // End polling on error
        setEnrichmentStatus('');
        setIsEnriching(false);
        return true;
      }
    };

    // Start polling
    const poll = async () => {
      const shouldStop = await checkStatus();
      if (!shouldStop) {
        setTimeout(poll, pollingInterval);
      }
    };

    // Initial poll
    poll();
  };

  // Function to enrich lead description
  const enrichLeadDescription = async () => {
    if (!lead) return;

    setIsEnriching(true);
    setEnrichmentStatus('Starting lead enrichment...');

    try {
      const response = await fetch(process.env.NEXT_ENRICH_URL || '', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ leadId: lead.id }),
      });

      if (!response.ok) {
        throw new Error('Failed to enrich lead');
      }

      const data = await response.json();
      console.log('Enrich response:', data);

      // Check if the response contains an operation with UUID
      if (data && data.uuid) {
        // Start polling for operation status
        toast({
          title: "Processing",
          description: "Lead enrichment started. This may take a few moments.",
        });

        pollOperationStatus(data.uuid);
      } else if (data.text) {
        // Handle direct response (backward compatibility)
        let enrichedDescription = data.text;

        // Update lead description with the parsed data
        const updatedLead = await fetch(`/api/leads/${lead.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            description: enrichedDescription,
          }),
        });

        if (!updatedLead.ok) {
          throw new Error('Failed to update lead description');
        }

        // Refresh lead data
        await fetchLead();

        toast({
          title: "Success",
          description: "Lead description enriched successfully",
        });

        setEnrichmentStatus('');
        setIsEnriching(false);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error enriching lead:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to enrich lead",
        variant: "destructive",
      });
      setEnrichmentStatus('');
      setIsEnriching(false);
    }
  };

  if (!lead) {
    return <div className="container mx-auto p-4">Loading...</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <div className="mb-6">
        <Button
          variant="outline"
          onClick={() => router.push('/leads')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Leads
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6">
        {/* Lead Details & Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Lead Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>First Name</Label>
                  <Input value={lead.firstName || ''} readOnly />
                </div>
                <div>
                  <Label>Last Name</Label>
                  <Input value={lead.lastName || ''} readOnly />
                </div>
              </div>
              <div>
                <Label>Email</Label>
                <Input value={lead.email || ''} readOnly />
              </div>
              <div>
                <Label>Company</Label>
                <Input value={lead.company || ''} readOnly />
              </div>
              <div>
                <Label>Title</Label>
                <Input value={lead.title || ''} readOnly />
              </div>
              <div>
                <Label>Status</Label>
                <Input value={lead.status || ''} readOnly />
              </div>

              {/* Additional Basic Information */}
              <div className="border p-4 rounded-md mt-4">
                <h3 className="font-medium mb-3">Additional Information</h3>
                <div className="mb-3">
                  <span className="text-sm font-medium">Updated At: {lead.updatedAt ? new Date(lead.updatedAt).toLocaleString() : 'N/A'}</span>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="col-span-2">
                    <div className="flex justify-between items-center mb-1">
                      <Label>Description</Label>
                      <div className="flex items-center gap-2">
                        {enrichmentStatus && (
                          <span className="text-xs text-muted-foreground">{enrichmentStatus}</span>
                        )}
                        <Button
                          variant="default"
                          size="sm"
                          onClick={enrichLeadDescription}
                          disabled={isEnriching}
                          className="flex items-center gap-1 bg-blue-500 hover:bg-blue-600 text-white"
                        >
                          {isEnriching ? (
                            <>
                              <Loader2 className="h-4 w-4 animate-spin" />
                              Enriching...
                            </>
                          ) : (
                            <>
                              <RefreshCw className="h-4 w-4" />
                              Enrich Lead
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                    <Textarea value={lead.description || ''} readOnly className="min-h-[100px]" />
                  </div>
                  {lead.phone && (
                    <div>
                      <Label>Phone</Label>
                      <Input value={lead.phone} readOnly />
                    </div>
                  )}
                  {lead.industry && (
                    <div>
                      <Label>Industry</Label>
                      <Input value={lead.industry} readOnly />
                    </div>
                  )}
                  {lead.rating && (
                    <div>
                      <Label>Rating</Label>
                      <Input value={lead.rating} readOnly />
                    </div>
                  )}
                  {lead.leadSource && (
                    <div>
                      <Label>Lead Source</Label>
                      <Input value={lead.leadSource} readOnly />
                    </div>
                  )}
                  {lead.website && (
                    <div>
                      <Label>Website</Label>
                      <Input value={lead.website} readOnly />
                    </div>
                  )}
                  {lead.numberOfEmployees && (
                    <div>
                      <Label>Number of Employees</Label>
                      <Input value={lead.numberOfEmployees.toString()} readOnly />
                    </div>
                  )}
                </div>
              </div>

              {/* Дополнительная информация из JSON-полей */}
              {lead.details && (
                <div className="border p-4 rounded-md mt-4">
                  <h3 className="font-medium mb-3">Дополнительная информация</h3>
                  <div className="grid grid-cols-2 gap-4">
                    {lead.details.bitrix_id && (
                      <div>
                        <Label>ID в Битрикс</Label>
                        <Input value={lead.details.bitrix_id} readOnly />
                      </div>
                    )}
                    {lead.details.middle_name && (
                      <div>
                        <Label>Отчество</Label>
                        <Input value={lead.details.middle_name} readOnly />
                      </div>
                    )}
                    {lead.details.role && (
                      <div>
                        <Label>Роль</Label>
                        <Input value={lead.details.role} readOnly />
                      </div>
                    )}
                    {lead.details.city && (
                      <div>
                        <Label>Город</Label>
                        <Input value={lead.details.city} readOnly />
                      </div>
                    )}
                    {lead.details.language && (
                      <div>
                        <Label>Язык</Label>
                        <Input value={lead.details.language} readOnly />
                      </div>
                    )}
                    {lead.details.tags && (
                      <div>
                        <Label>Теги</Label>
                        <Input value={lead.details.tags} readOnly />
                      </div>
                    )}
                    {lead.details?.facebook_info?.content && (
                      <div>
                        <Label>Facebook Info</Label>
                        <Textarea value={lead.details.facebook_info.content} readOnly />
                      </div>
                    )}

                  </div>
                </div>
              )}
          {/* LinkedIn Info */}
          <div className="border p-4 rounded-md mt-4">
            <h3 className="font-medium mb-3">LinkedIn Information</h3>
            <div className="grid grid-cols-1 gap-8">
              <div>
                <Label>LinkedIn Info</Label>
                <Textarea
                  cols={100}
                  className="min-h-[200px]"
                  value={lead?.details?.linkedin_info?.content || lead?.details?.linkedin_info?.raw_content || ''}
                  readOnly
                />
              </div>
            </div>
          </div>
          {/* Facebook Info */}
          {(lead?.details?.facebook_info?.content || lead?.details?.facebook_info?.raw_content) && (
            <div className="border p-4 rounded-md mt-4">
              <h3 className="font-medium mb-3">Facebook Information</h3>
              <div className="grid grid-cols-1 gap-8">
                <div>
                  <Label>Facebook Info</Label>
                  <Textarea
                    cols={100}
                    className="min-h-[200px]"
                    value={lead?.details?.facebook_info?.content || lead?.details?.facebook_info?.raw_content || ''}
                    readOnly
                  />
                </div>
              </div>
            </div>
          )}

              {/* Информация о коммуникациях */}
              {lead.communications && typeof lead.communications === 'object' && (
                <div className="border p-4 rounded-md mt-4">
                  <h3 className="font-medium mb-3">Контактная информация</h3>
                  <div className="grid grid-cols-2 gap-4">
                    {lead.communications.facebook && (
                      <div>
                        <Label>Facebook</Label>
                        <Input value={lead.communications.facebook} readOnly />
                      </div>
                    )}
                    {lead.communications.telegram && (
                      <div>
                        <Label>Telegram</Label>
                        <Input value={lead.communications.telegram} readOnly />
                      </div>
                    )}
                    {lead.communications.vkontakte && (
                      <div>
                        <Label>ВКонтакте</Label>
                        <Input value={lead.communications.vkontakte} readOnly />
                      </div>
                    )}
                    {lead.communications.linkedin && (
                      <div>
                        <Label>LinkedIn</Label>
                        <Input value={lead.communications.linkedin} readOnly />
                      </div>
                    )}
                    {lead.communications.last_activity && (
                      <div>
                        <Label>Последняя активность</Label>
                        <Input value={lead.communications.last_activity} readOnly />
                      </div>
                    )}
                    {lead.communications.contact_history && (
                      <div className="col-span-2">
                        <Label>История контактов</Label>
                        <Textarea value={lead.communications.contact_history} readOnly className="mt-1" />
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Информация о компании */}
              {lead.company_info && (
                <div className="border p-4 rounded-md mt-4">
                  <h3 className="font-medium mb-3">Информация о компании</h3>
                  <div className="grid grid-cols-2 gap-4">
                    {lead.company_info.name && (
                      <div>
                        <Label>Название</Label>
                        <Input value={lead.company_info.name} readOnly />
                      </div>
                    )}
                    {lead.company_info.website && (
                      <div>
                        <Label>Веб-сайт</Label>
                        <Input value={lead.company_info.website} readOnly />
                      </div>
                    )}
                    {lead.company_info.industry && (
                      <div>
                        <Label>Отрасль</Label>
                        <Input value={lead.company_info.industry} readOnly />
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div>
              <Label>Agent</Label>
                <AgentSelect
                  selectedId={selectedAgent?.id}
                  onSelect={(agent) => setSelectedAgent(agent)}
                />
              </div>
              <div>
                    <Label htmlFor="description">Extra Prompt</Label>
                    <Textarea
                      id="description"
                      value={extraPrompt || ''}
                      onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                        setExtraPrompt(e.target.value)}
                      className="min-h-[200px]"
                    />
                    <div className="mt-2 p-3 border rounded-md bg-muted/30">
                      <h4 className="text-sm font-medium mb-2">Доступные переменные:</h4>
                      <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                        <div>
                          <h5 className="font-medium">Основные:</h5>
                          <ul className="list-disc pl-4">
                            <li className="flex items-center justify-between">
                              <span>{'{{leadFirstName}}'} - имя лида</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 px-2 text-xs"
                                onClick={() => insertVariable('{{leadFirstName}}')}
                              >
                                +
                              </Button>
                            </li>
                            <li className="flex items-center justify-between">
                              <span>{'{{leadLastName}}'} - фамилия лида</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 px-2 text-xs"
                                onClick={() => insertVariable('{{leadLastName}}')}
                              >
                                +
                              </Button>
                            </li>
                            <li className="flex items-center justify-between">
                              <span>{'{{leadCompany}}'} - компания лида</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 px-2 text-xs"
                                onClick={() => insertVariable('{{leadCompany}}')}
                              >
                                +
                              </Button>
                            </li>
                            <li className="flex items-center justify-between">
                              <span>{'{{leadEmail}}'} - email лида</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 px-2 text-xs"
                                onClick={() => insertVariable('{{leadEmail}}')}
                              >
                                +
                              </Button>
                            </li>
                            <li className="flex items-center justify-between">
                              <span>{'{{lead_details}}'} - детали лида</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 px-2 text-xs"
                                onClick={() => insertVariable('{{lead_details}}')}
                              >
                                +
                              </Button>
                            </li>
                            <li className="flex items-center justify-between">
                              <span>{'{{lead_history}}'} - история лида</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 px-2 text-xs"
                                onClick={() => insertVariable('{{lead_history}}')}
                              >
                                +
                              </Button>
                            </li>
                            <li className="flex items-center justify-between">
                              <span>{'{{company_info}}'} - информация о компании</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 px-2 text-xs"
                                onClick={() => insertVariable('{{company_info}}')}
                              >
                                +
                              </Button>
                            </li>
                            <li className="flex items-center justify-between">
                              <span>{'{{senderName}}'} - имя отправителя</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 px-2 text-xs"
                                onClick={() => insertVariable('{{senderName}}')}
                              >
                                +
                              </Button>
                            </li>
                          </ul>
                        </div>
                        <div>
                          <h5 className="font-medium">Дополнительные поля:</h5>
                          <ul className="list-disc pl-4">
                            {lead.details && Object.keys(lead.details).map(key => (
                              <li key={`details_${key}`} className="flex items-center justify-between">
                                <span>{'{{details_' + key + '}}'} - {String(lead.details![key] || '')}</span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 px-2 text-xs"
                                  onClick={() => insertVariable(`{{details_${key}}}`)}
                                >
                                  +
                                </Button>
                              </li>
                            ))}
                            {lead.communications && Object.keys(lead.communications).map(key => (
                              <li key={`communications_${key}`} className="flex items-center justify-between">
                                <span>{'{{communications_' + key + '}}'} - {String(lead.communications![key] || '')}</span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 px-2 text-xs"
                                  onClick={() => insertVariable(`{{communications_${key}}}`)}
                                >
                                  +
                                </Button>
                              </li>
                            ))}
                            {lead.company_info && Object.keys(lead.company_info).map(key => (
                              <li key={`company_${key}`} className="flex items-center justify-between">
                                <span>{'{{company_' + key + '}}'} - {String(lead.company_info![key] || '')}</span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 px-2 text-xs"
                                  onClick={() => insertVariable(`{{company_${key}}}`)}
                                >
                                  +
                                </Button>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
              <div>
                <Button
                  onClick={handleGenerateEmail}
                  disabled={isGenerating || !selectedAgent}
                  className="w-full"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="h-5 w-5 animate-spin mr-2" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Mail className="h-5 w-5 mr-2" />
                      Generate Email
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs for Generated Emails and Communication History */}
        <Tabs defaultValue="emails" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="emails">Generated Emails</TabsTrigger>
            <TabsTrigger value="history">Communication History</TabsTrigger>
          </TabsList>

          <TabsContent value="emails">
            <Card>
              <CardHeader>
                <CardTitle>Generated Emails</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {emails.map((email) => (
                  <Card key={email.id}>
                    <CardContent className="pt-6">
                      {editingEmail === email.id ? (
                        // Edit mode
                        <div className="space-y-4">
                          <div>
                            <Label>Subject</Label>
                            <Input
                              value={editedSubject}
                              onChange={(e) => setEditedSubject(e.target.value)}
                            />
                          </div>
                          <div>
                            <Label>Content</Label>
                            <Textarea
                              value={editedContent}
                              onChange={(e) => setEditedContent(e.target.value)}
                              className="min-h-[200px]"
                            />
                          </div>
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleCancelEdit}
                              disabled={loading}
                            >
                              Cancel
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => handleSaveEdit(email.id)}
                              disabled={loading}
                            >
                              Save
                            </Button>
                          </div>
                        </div>
                      ) : (
                        // View mode
                        <>
                          <h3 className="font-medium">{email.subject}</h3>
                          <p className="text-sm text-muted-foreground mt-2 whitespace-pre-wrap">
                            {email.content}
                          </p>
                          <div className="mt-4">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => togglePromptVisibility(email.id)}
                              className="flex items-center text-xs text-muted-foreground"
                            >
                              {showPromptMap[email.id] ? (
                                <>
                                  <ChevronUp className="h-3 w-3 mr-1" />
                                  Скрыть промпт
                                </>
                              ) : (
                                <>
                                  <ChevronDown className="h-3 w-3 mr-1" />
                                  Показать промпт
                                </>
                              )}
                            </Button>
                            {showPromptMap[email.id] && (
                              <div className="mt-2 p-3 border rounded-md bg-muted/30">
                                <Label className="text-xs mb-1 block">Промпт для генерации</Label>
                                <p className="text-xs whitespace-pre-wrap">{email.prompt}</p>
                              </div>
                            )}
                          </div>
                          <div className="mt-2 flex justify-between items-center text-sm text-muted-foreground">
                            <span>{new Date(email.createdAt).toLocaleDateString()}</span>
                            <div className="flex items-center gap-2">
                              <Badge variant={email.sent ? "default" : "secondary"}>
                                {email.sent ? "Sent" : "Draft"}
                              </Badge>
                              {!email.sent && (
                                <>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleEditClick(email)}
                                    disabled={loading}
                                  >
                                    <FileEdit className="h-4 w-4 mr-1" />
                                    Edit
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() =>
                                      session.data?.user?.email
                                        ? handleSendClick(email.id)
                                        : sendEmail(email.id)
                                    }
                                    disabled={loading}
                                  >
                                    <Send className="h-4 w-4 mr-1" />
                                    Send
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleDeleteEmail(email.id)}
                                    disabled={loading}
                                  >
                                    <Trash2 className="h-4 w-4 mr-1" />
                                    Delete
                                  </Button>
                                </>
                              )}
                            </div>
                          </div>
                        </>
                      )}
                    </CardContent>
                  </Card>
                ))}
                {emails.length === 0 && (
                  <p className="text-muted-foreground">No emails generated yet</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history">
            {lead.salesforceId && (
              <LeadCommunicationsHistory leadId={lead.salesforceId} />
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Email Selection Dialog */}
      <Dialog open={showEmailDialog} onOpenChange={setShowEmailDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Select Email to Send From</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Select value={selectedEmailConfig} onValueChange={setSelectedEmailConfig}>
              <SelectTrigger>
                <SelectValue placeholder="Select an email" />
              </SelectTrigger>
              <SelectContent>
                {session.data?.user?.email && (
                  <SelectItem value="logged_in">
                    {session.data.user.email} (Logged In)
                  </SelectItem>
                )}
                {emailConfigs.map((config) => (
                  <SelectItem key={config.id} value={config.id}>
                    {config.email}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="mt-4 flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowEmailDialog(false)}>
                Cancel
              </Button>
              <Button onClick={() => sendEmail()} disabled={!selectedEmailConfig || loading}>
                Send Email
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState, useCallback } from 'react';
import { AgentForm } from '@/components/agents/AgentForm';
import { Agent, CreateAgentInput } from '@/types/agent';
import { useToast } from '@/hooks/useToast';

interface EditAgentClientWrapperProps {
  params: {
    id: string;
  };
}

export default function EditAgentClientWrapper({ params }: EditAgentClientWrapperProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [agent, setAgent] = useState<Agent | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchAgent = useCallback(async () => {
    try {
      const response = await fetch(`/api/v1/agents/${params.id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch agent');
      }
      const data = await response.json();
      setAgent(data);
    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch agent',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [params.id, toast]);

  useEffect(() => {
    fetchAgent();
  }, [fetchAgent]);

  const handleUpdate = async (data: CreateAgentInput) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/v1/agents/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to update agent');
      }

      await fetchAgent();
      toast({
        title: 'Success',
        description: 'Agent updated successfully',
      });
    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to update agent',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/v1/agents/${params.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete agent');
      }

      router.push('/agents');
      toast({
        title: 'Success',
        description: 'Agent deleted successfully',
      });
    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to delete agent',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!agent) {
    return <div>Agent not found</div>;
  }

  return <AgentForm onSubmit={handleUpdate} initialData={agent} onDelete={handleDelete} />;
}

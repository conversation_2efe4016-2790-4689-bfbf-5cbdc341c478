'use client';

import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { CreateAgentInput } from '@/types/agent';
import { AgentForm } from '@/components/agents/AgentForm';
import { useToast } from '@/hooks/useToast';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

async function fetchAgent(id: string) {
  const response = await fetch(`/api/v1/agents/${id}`);
  if (!response.ok) {
    throw new Error('Failed to fetch agent');
  }
  return response.json();
}

async function updateAgent(id: string, data: CreateAgentInput) {
  const response = await fetch(`/api/v1/agents/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: 'Failed to update agent' }));
    throw new Error(error.error || 'Failed to update agent');
  }

  return response.json();
}

export default function EditAgentPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: agent, isLoading: isLoadingAgent } = useQuery({
    queryKey: ['agent', params.id],
    queryFn: () => fetchAgent(params.id as string),
    enabled: !!params.id,
    staleTime: 30000, // Consider data fresh for 30 seconds
  });

  const updateMutation = useMutation({
    mutationFn: (data: CreateAgentInput) => 
      updateAgent(params.id as string, data),
    onSuccess: (data) => {
      toast({
        title: 'Success',
        description: `Agent "${data.name}" has been updated successfully`,
        variant: 'default',
      });
      queryClient.invalidateQueries({ queryKey: ['agents'] });
      router.push('/agents');
    },
    onError: (error: Error) => {
      console.error('Error updating agent:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to update agent. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = async (data: CreateAgentInput) => {
    updateMutation.mutate(data);
  };

  const handleBack = () => {
    router.push('/agents');
  };

  if (isLoadingAgent) {
    return (
      <div className="py-6">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
          <div className="text-center py-8">
            <p className="text-gray-500">Loading agent...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!agent) {
    return (
      <div className="py-6">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
          <div className="text-center py-8">
            <p className="text-gray-500">Agent not found</p>
            <Button
              onClick={handleBack}
              variant="outline"
              className="mt-4"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Agents
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="py-6">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button
              onClick={handleBack}
              variant="ghost"
              className="mr-4"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <h1 className="text-2xl font-semibold text-gray-900">
              Edit Agent: {agent.name}
            </h1>
          </div>
        </div>
        <AgentForm 
          initialData={agent} 
          onSubmit={handleSubmit}
          isLoading={updateMutation.isPending}
        />
      </div>
    </div>
  );
}

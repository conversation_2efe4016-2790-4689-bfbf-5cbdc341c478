import EditAgentClientWrapper from './EditAgentClientWrapper';

// interface EditAgentPageProps {
//   params: {
//     id: string;
//   };
// }

export default function EditAgentPage({ params }: { params: { id: string } }) {
  return (
    <div className="py-6">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
        <h1 className="text-2xl font-semibold text-gray-900">Edit Agent</h1>
        <div className="mt-8">
          <EditAgentClientWrapper params={params} />
        </div>
      </div>
    </div>
  );
}

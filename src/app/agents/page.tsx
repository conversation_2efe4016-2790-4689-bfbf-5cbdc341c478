import Link from 'next/link';
import AgentsClientWrapper from './AgentsClientWrapper';

export default async function AgentsPage() {
  return (
    <div className="py-6">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold text-gray-900">My Agents</h1>
          <Link
            href="/agents/new"
            className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
          >
            Create New Agent
          </Link>
        </div>
        <div className="mt-8">
          <AgentsClientWrapper />
        </div>
      </div>
    </div>
  );
}

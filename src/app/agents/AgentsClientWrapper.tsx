'use client';

import { useRouter } from 'next/navigation';
import { Agent } from '@/types/agent';
import { AgentsList } from '@/components/agents/AgentsList';
import { useToast } from '@/hooks/useToast';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

async function fetchAgents() {
  const response = await fetch('/api/v1/agents');
  if (!response.ok) {
    throw new Error('Failed to fetch agents');
  }
  return response.json();
}

export default function AgentsClientWrapper() {
  const router = useRouter();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data, isLoading } = useQuery({
    queryKey: ['agents'],
    queryFn: fetchAgents,
    staleTime: 30000, // Consider data fresh for 30 seconds
  });

  const deleteMutation = useMutation({
    mutationFn: async (agent: Agent) => {
      const response = await fetch(`/api/v1/agents/${agent.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete agent');
      }

      return response.json();
    },
    onSuccess: (_, agent) => {
      toast({
        title: 'Success',
        description: `Agent "${agent.name}" has been deleted successfully`,
        variant: 'default',
      });
      queryClient.invalidateQueries({ queryKey: ['agents'] });
    },
    onError: (error) => {
      console.error('Error deleting agent:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete agent. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const handleEdit = (agent: Agent) => {
    router.push(`/agents/${agent.id}/edit`);
  };

  const handleDelete = (agent: Agent) => {
    if (window.confirm(`Are you sure you want to delete agent "${agent.name}"?`)) {
      deleteMutation.mutate(agent);
    }
  };

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Loading agents...</p>
      </div>
    );
  }

  const agents = data?.agents || [];

  if (agents.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">No agents found</h3>
        <p className="mt-1 text-sm text-gray-500">Get started by creating a new agent.</p>
      </div>
    );
  }

  return (
    <AgentsList
      agents={agents}
      onEdit={handleEdit}
      onDelete={handleDelete}
      isLoading={deleteMutation.isPending}
    />
  );
}

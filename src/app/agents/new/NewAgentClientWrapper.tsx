'use client';

import { useRouter } from 'next/navigation';
import { AgentForm } from '@/components/agents/AgentForm';
import { CreateAgentInput } from '@/types/agent';
import { useToast } from '@/hooks/useToast';

export default function NewAgentClientWrapper() {
  const router = useRouter();
  const { toast } = useToast();

  const handleSubmit = async (data: CreateAgentInput) => {
    try {
      const response = await fetch('/api/v1/agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) throw new Error('Failed to create agent');

      toast({
        title: 'Success',
        description: 'Agent created successfully',
      });
      router.push('/agents');
    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to create agent',
        variant: 'destructive',
      });
    }
  };

  return <AgentForm onSubmit={handleSubmit} />;
}

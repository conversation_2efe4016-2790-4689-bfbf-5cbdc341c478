"use client";

import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { Eye, ExternalLink } from "lucide-react";
import Link from "next/link";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { EmailDetailsModal } from "@/components/emails/EmailDetailsModal";
import { StatusBadge } from "@/components/ui/status-badge";

export default function EmailsPage() {
  const [selectedEmailId, setSelectedEmailId] = useState<number | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  const { data: emails, isLoading } = useQuery({
    queryKey: ["emails"],
    queryFn: async () => {
      const response = await fetch("/api/emails");
      if (!response.ok) throw new Error("Failed to fetch emails");
      return response.json();
    },
  });

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Emails</h1>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Subject</TableHead>
              <TableHead className="w-[200px]">Lead</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
                  </div>
                </TableCell>
              </TableRow>
            ) : emails?.map((email: any) => (
              <TableRow key={email.id}>
                <TableCell>{email.subject}</TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <span>{email.leadFirstName} {email.leadLastName}</span>
                    <Link
                      href={`/leads/${email.leadId}`}
                      className="text-blue-500 hover:text-blue-700"
                      target="_blank"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </Link>
                  </div>
                </TableCell>
                <TableCell>
                  <StatusBadge
                    variant={email.sent ? "success" : "warning"}
                  >
                    {email.sent ? "Sent" : "Draft"}
                  </StatusBadge>
                </TableCell>
                <TableCell>{new Date(email.createdAt).toLocaleString()}</TableCell>
                <TableCell>
                  <div className="flex items-center justify-center">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => {
                        setSelectedEmailId(email.id);
                        setIsDetailsModalOpen(true);
                      }}
                      title="View Email Details"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <EmailDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedEmailId(null);
        }}
        emailId={selectedEmailId}
      />
    </div>
  );
}

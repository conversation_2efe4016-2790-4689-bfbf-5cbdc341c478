import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { jwtVerify } from "jose";

export async function GET() {
    const cookieStore = cookies();
    const jwtToken = cookieStore.get("token");
    const sfSession = cookieStore.get("sf_session");

    try {
        if (jwtToken) {
            // Verify JWT token
            const secret = new TextEncoder().encode(
                process.env.JWT_SECRET || "your-default-secret-key-change-this"
            );
            await jwtVerify(jwtToken.value, secret);
            return NextResponse.json({ authenticated: true });
        }

        // Check Salesforce session
        if (sfSession) {
            return NextResponse.json({ authenticated: true });
        }

        // No valid authentication found
        return NextResponse.json({ authenticated: false });
    } catch (_error) {
        // JWT verification failed
        return NextResponse.json({ authenticated: false });
    }
}

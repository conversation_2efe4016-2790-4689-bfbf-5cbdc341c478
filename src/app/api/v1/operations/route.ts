import { NextRequest, NextResponse } from "next/server";
import { OperationsService } from "@/services/operations";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { operationStatusEnum, operationTypeEnum } from "@/db/schema/operations";

// Set dynamic to force-dynamic to ensure we always get the latest operation status
export const dynamic = 'force-dynamic';

// Schema for creating a new operation
const createOperationSchema = z.object({
  type: z.enum(operationTypeEnum),
  details: z.string().optional(),
});

// Schema for updating an operation
const updateOperationSchema = z.object({
  uuid: z.string().uuid(),
  status: z.enum(operationStatusEnum),
  error: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = createOperationSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: "Validation error",
          details: validationResult.error.format(),
        },
        { status: 400 }
      );
    }

    const { type, details } = validationResult.data;

    // Create a new operation
    const operation = await OperationsService.createOperation(type, details);

    return NextResponse.json({
      success: true,
      operation,
    });
  } catch (error: any) {
    console.error("Error creating operation:", error);
    return NextResponse.json(
      {
        error: "Failed to create operation",
        message: error.message,
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = updateOperationSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: "Validation error",
          details: validationResult.error.format(),
        },
        { status: 400 }
      );
    }

    const { uuid, status, error } = validationResult.data;

    // Update the operation status
    const operation = await OperationsService.updateOperationStatus(uuid, status, error);

    if (!operation) {
      return NextResponse.json(
        { error: "Operation not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      operation,
    });
  } catch (error: any) {
    console.error("Error updating operation:", error);
    return NextResponse.json(
      {
        error: "Failed to update operation",
        message: error.message,
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const uuid = searchParams.get("uuid");
    const type = searchParams.get("type") as any;
    const status = searchParams.get("status") as any;

    let result;

    // Get operation by UUID
    if (uuid) {
      result = await OperationsService.getOperation(uuid);
      if (!result) {
        return NextResponse.json(
          { error: "Operation not found" },
          { status: 404 }
        );
      }

      // Return in the format expected by the frontend
      // The frontend expects an 'operation' property with status that can be 'completed', 'SUCCESS', 'error', or 'ERROR'
      return NextResponse.json({
        operation: {
          ...result,
          // Ensure status is in the format expected by the frontend
          status: result.status === 'SUCCESS' ? 'completed' : result.status
        }
      });
    }

    // Get operations by type
    if (type && operationTypeEnum.includes(type)) {
      result = await OperationsService.getOperationsByType(type);
      return NextResponse.json({ operations: result });
    }

    // Get operations by status
    if (status && operationStatusEnum.includes(status)) {
      result = await OperationsService.getOperationsByStatus(status);
      return NextResponse.json({ operations: result });
    }

    // If no specific query, return error
    return NextResponse.json(
      { error: "Missing query parameters. Use uuid, type, or status." },
      { status: 400 }
    );
  } catch (error: any) {
    console.error("Error fetching operations:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch operations",
        message: error.message,
      },
      { status: 500 }
    );
  }
}

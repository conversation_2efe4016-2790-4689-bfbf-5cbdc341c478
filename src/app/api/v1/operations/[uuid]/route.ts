import { NextRequest, NextResponse } from "next/server";
import { verifyApiToken } from "@/utils/api-tokens";
import { OperationsService } from "@/services/operations";
import { authOptions } from "@/lib/auth";
import { getServerSession } from "next-auth";

// Set dynamic to force-dynamic to ensure we always get the latest operation status
export const dynamic = 'force-dynamic';

export async function GET(
  request: NextRequest,
  { params }: { params: { uuid: string } }
) {
  try {
    // Verify authentication
    const authHeader = request.headers.get("Authorization");

    if (authHeader && authHeader.startsWith("Bearer ")) {
      // API token authentication
      const token = authHeader.split(" ")[1];
      const { valid } = await verifyApiToken(token);

      if (!valid) {
        return NextResponse.json(
          { error: "Invalid API token" },
          { status: 401 }
        );
      }
    } else {
      // Session authentication
      const session = await getServerSession(authOptions);

      if (!session?.user) {
        return NextResponse.json(
          { error: "Unauthorized" },
          { status: 401 }
        );
      }
    }

    // Get operation by UUID
    const operation = await OperationsService.getOperation(params.uuid);

    if (!operation) {
      return NextResponse.json(
        { error: "Operation not found" },
        { status: 404 }
      );
    }

    // Return operation details in the format expected by the frontend
    return NextResponse.json({
      success: true,
      operation: {
        uuid: operation.uuid,
        type: operation.type,
        // Ensure status is in the format expected by the frontend
        status: operation.status === 'SUCCESS' ? 'completed' : operation.status,
        details: operation.details,
        error: operation.error,
        createdAt: operation.createdAt,
        updatedAt: operation.updatedAt,
        completedAt: operation.completedAt,
      },
    });
  } catch (error: any) {
    console.error("Error fetching operation:", error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

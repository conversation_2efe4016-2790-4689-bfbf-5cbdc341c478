import { NextRequest, NextResponse } from "next/server";
import { verifyApiToken } from "@/utils/api-tokens";
import { db } from "@/db";
import { leads } from "@/db/schema/leads";
import { eq } from "drizzle-orm";
import { authOptions } from "@/lib/auth";
import { getServerSession } from "next-auth";

// Set dynamic to force-dynamic to ensure we always get the latest data
export const dynamic = 'force-dynamic';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication
    const authHeader = request.headers.get("Authorization");

    if (authHeader && authHeader.startsWith("Bearer ")) {
      // API token authentication
      const token = authHeader.split(" ")[1];
      const { valid } = await verifyApiToken(token);

      if (!valid) {
        return NextResponse.json(
          { error: "Invalid API token" },
          { status: 401 }
        );
      }
    } else {
      // Session authentication
      const session = await getServerSession(authOptions);

      if (!session?.user) {
        return NextResponse.json(
          { error: "Unauthorized" },
          { status: 401 }
        );
      }
    }

    // Get lead by ID
    const [lead] = await db
      .select()
      .from(leads)
      .where(eq(leads.id, parseInt(params.id)))
      .limit(1);

    if (!lead) {
      return NextResponse.json(
        { error: "Lead not found" },
        { status: 404 }
      );
    }

    // Return lead data
    return NextResponse.json({
      success: true,
      lead: {
        id: lead.id,
        firstName: lead.firstName,
        lastName: lead.lastName,
        email: lead.email,
        company: lead.company,
        title: lead.title,
        phone: lead.phone,
        industry: lead.industry,
        rating: lead.rating,
        leadSource: lead.leadSource,
        description: lead.description,
        website: lead.website,
        numberOfEmployees: lead.numberOfEmployees,
        status: lead.status,
        details: lead.details,
        history: lead.history,
        company_info: lead.company_info,
        communications: lead.communications,
        lastModifiedDate: lead.lastModifiedDate,
        createdAt: lead.createdAt,
        updatedAt: lead.updatedAt,
      },
    });
  } catch (error: any) {
    console.error("Error fetching lead:", error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

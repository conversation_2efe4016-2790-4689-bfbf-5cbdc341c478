import { NextRequest, NextResponse } from "next/server";
import { verifyApiToken } from "@/utils/api-tokens";
import { leadEnrichmentRequestSchema } from "@/lib/schemas/lead-enrichment-schemas";
import { db } from "@/db";
import { leads } from "@/db/schema/leads";
import { getServerSession } from "next-auth";
import { users } from "@/db/schema/users";
import { eq } from "drizzle-orm";
import { LeadEnrichmentService } from "@/services/lead-enrichment";
import { authOptions } from "@/lib/auth";

// Set dynamic to force-dynamic to ensure we always get the latest data
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const authHeader = request.headers.get("Authorization");
    let userId: string;

    if (authHeader && authHeader.startsWith("Bearer ")) {
      // API token authentication
      const token = authHeader.split(" ")[1];
      const { valid, userId: tokenUserId } = await verifyApiToken(token);

      if (!valid) {
        return NextResponse.json(
          { error: "Invalid API token" },
          { status: 401 }
        );
      }

      userId = String(tokenUserId);
    } else {
      // Session authentication
      const session = await getServerSession(authOptions);

      if (!session?.user) {
        return NextResponse.json(
          { error: "Unauthorized" },
          { status: 401 }
        );
      }

      // Get user ID from email
      if (session.user.email) {
        const emailUserId = await db
          .select({ id: users.id })
          .from(users)
          .where(eq(users.email, session.user.email))
          .limit(1);

        if (emailUserId.length === 0) {
          return NextResponse.json(
            { error: "User not found" },
            { status: 404 }
          );
        }

        userId = String(emailUserId[0].id);
      } else {
        return NextResponse.json(
          { error: "User email not found" },
          { status: 400 }
        );
      }
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = leadEnrichmentRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: "Validation error",
          details: validationResult.error.format(),
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Check if a lead with the same email already exists
    let existingLead = null;
    if (data.email) {
      const existingLeads = await db
        .select()
        .from(leads)
        .where(eq(leads.email, data.email))
        .limit(1);

      if (existingLeads.length > 0) {
        existingLead = existingLeads[0];
      }
    }

    // Map the request data to the database schema
    const leadData: any = {
      firstName: data.first_name,
      lastName: data.last_name,
      email: data.email,
      company: data.company || "Unknown Company",
      description: data.description,
      communications: data.communications || {},
      details: data.details || {},
      createdBy: userId,
      updatedAt: new Date(),
    };

    let newLead;

    if (existingLead) {
      // Update existing lead
      console.log(`Updating existing lead with ID ${existingLead.id} and email ${data.email}`);
      [newLead] = await db
        .update(leads)
        .set(leadData)
        .where(eq(leads.id, existingLead.id))
        .returning();
    } else {
      // Create a new lead
      console.log(`Creating new lead with email ${data.email}`);
      [newLead] = await db
        .insert(leads)
        .values(leadData)
        .returning();
    }

    // Get webhook URL from environment variable or request
    const webhookUrl = process.env.NEXT_ENRICH_URL || data.webhook_url;

    // Process lead enrichment synchronously
    if (webhookUrl) {
      // Call the lead enrichment service directly with the lead ID
      // The webhook will create the operation and return its UUID
      const enrichmentResult = await LeadEnrichmentService.processLeadEnrichment(
        null, // No operation UUID - the webhook will create it
        newLead.id,
        webhookUrl,
        data.lang || "ru" // Pass language with default to Russian
      );

      // Return the operation UUID in the format expected by the frontend
      return NextResponse.json({
        success: true,
        uuid: enrichmentResult.uuid,
        operation: {
          uuid: enrichmentResult.uuid,
          type: "LEAD_ENRICHMENT",
          status: enrichmentResult.status || "SUCCESS",
          createdAt: new Date(),
        },
        lead: {
          id: newLead.id,
        },
      });
    } else {
      // If no webhook URL is provided, create a simple success response
      return NextResponse.json({
        success: true,
        lead: {
          id: newLead.id,
        },
      });
    }
  } catch (error: any) {
    console.error("Error in lead enrichment:", error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

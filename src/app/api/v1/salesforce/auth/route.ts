import { NextResponse } from 'next/server';
import { salesforce } from '@/services/salesforce';

export async function GET() {
    try {
        // Generate authorization URL and code verifier
        const { url, codeVerifier } = salesforce.getAuthorizationUrl();

        // Create response with URL
        const response = NextResponse.json({
            url,
            timestamp: new Date().toISOString()
        });

        // Set code verifier in a cookie on the response
        response.cookies.set('sf_code_verifier', codeVerifier, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            path: '/',
            maxAge: 300, // 5 minutes
        });

        return response;
    } catch (error) {
        console.error('Error generating auth URL:', error);
        return NextResponse.json(
            { error: 'Failed to generate authorization URL' },
            { status: 500 }
        );
    }
}

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { salesforce } from '@/services/salesforce';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const code = request.nextUrl.searchParams.get('code');
    
    if (!code) {
      return NextResponse.redirect(new URL('/login?error=missing_code', request.nextUrl.origin));
    }

    // Get code verifier from cookie
    const cookieStore = cookies();
    const codeVerifier = cookieStore.get('sf_code_verifier')?.value;

    if (!codeVerifier) {
      console.error('Code verifier not found in cookies');
      return NextResponse.redirect(new URL('/login?error=missing_verifier', request.nextUrl.origin));
    }

    // Create response for redirect
    const response = NextResponse.redirect(new URL('/', request.nextUrl.origin));

    // Delete code verifier cookie
    response.cookies.set('sf_code_verifier', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 0,
    });

    // Exchange code for tokens using code verifier
    const tokens = await salesforce.getAccessToken(code, codeVerifier);
    
    if (!tokens || !tokens.access_token || !tokens.instance_url) {
      throw new Error('Failed to get access token');
    }

    // Set tokens in cookies
    response.cookies.set('sf_session', tokens.access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 60 * 60 * 24, // 24 hours
    });

    if (tokens.refresh_token) {
      response.cookies.set('sf_refresh_token', tokens.refresh_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
        maxAge: 60 * 60 * 24 * 30, // 30 days
      });
    }

    response.cookies.set('sf_instance_url', tokens.instance_url, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 60 * 60 * 24 * 30, // 30 days
    });

    // Update Salesforce connection with new tokens
    salesforce.setTokens(tokens.access_token, tokens.refresh_token, tokens.instance_url);

    return response;
  } catch (error) {
    console.error('Error in callback:', error);
    return NextResponse.redirect(new URL('/login?error=auth_failed', request.nextUrl.origin));
  }
}

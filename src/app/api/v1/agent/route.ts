import { NextRequest, NextResponse } from "next/server";
import { verifyApiToken } from "@/utils/api-tokens";
import { agentRequestSchema } from "@/lib/schemas/agent-api-schemas";
import { db } from "@/db";
import { agents } from "@/db/schema/agents";
import { eq } from "drizzle-orm";
import { createEmailWithAI } from "@/lib/ai/generate-email";
import { prompts } from "@/db/schema/prompts";
import { emailHistory, emails } from "@/db/schema/emails";

// Функция для обработки CORS
function corsHeaders(req: NextRequest) {
    // Проверяем origin запроса
    const origin = req.headers.get("origin") || "*";
    
    return {
        "Access-Control-Allow-Origin": origin,
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
        "Access-Control-Max-Age": "86400",
    };
}

// Обработчик OPTIONS запросов для CORS preflight
export async function OPTIONS(req: NextRequest) {
    return new NextResponse(null, {
        status: 200,
        headers: corsHeaders(req),
    });
}

export async function POST(req: NextRequest) {
    try {
        // Добавляем CORS заголовки к ответу
        const headers = corsHeaders(req);
        
        // Проверка заголовка авторизации
        const authHeader = req.headers.get("Authorization");
        
        if (!authHeader || !authHeader.startsWith("Bearer ")) {
            return NextResponse.json(
                { error: "Отсутствует или некорректный токен авторизации" },
                { status: 401, headers }
            );
        }
        
        const token = authHeader.split(" ")[1];
        const { valid, userId } = await verifyApiToken(token);
        
        if (!valid) {
            return NextResponse.json(
                { error: "Недействительный API-токен" },
                { status: 401, headers }
            );
        }
        
        // Парсинг и валидация тела запроса
        const body = await req.json();
        console.log('body', body)
        const validationResult = agentRequestSchema.safeParse(body);
        
        if (!validationResult.success) {
            return NextResponse.json(
                { 
                    error: "Ошибка валидации данных", 
                    details: validationResult.error.format() 
                },
                { status: 400, headers }
            );
        }
        
        const data = validationResult.data;
        
        // Поиск агента по UUID
        const agent = await db.query.agents.findFirst({
            where: eq(agents.uuid, data.agentId),
        });
        
        if (!agent) {
            return NextResponse.json(
                { error: "Агент не найден" },
                { status: 404, headers }
            );
        }
        
        if (!agent.isActive) {
            return NextResponse.json(
                { error: "Агент неактивен" },
                { status: 403, headers }
            );
        }

        // console.log('agent', agent)
        let prompt = agent.prompt;

        // Проверяем, есть ли в запросе promptId
        if (agent.promptId) {
            // Ищем промпт в таблице промптов по promptId
            const promptFromDb = await db.query.prompts.findFirst({
                where: eq(prompts.id, agent.promptId),
            });

            // console.log('promptFromDb', promptFromDb)
            
            if (!promptFromDb) {
                return NextResponse.json(
                    { error: "Промпт не найден" },
                    { status: 404, headers }
                );
            }
            
            // Используем промпт из таблицы вместо промпта агента
            prompt = promptFromDb.content;
            
            // console.log('Используется промпт из таблицы:', promptFromDb.id);
        }
        
        // Получаем настройки для генерации (из запроса или используем дефолтные)
        const settings = agent.settings as {
            model: string;
            maxTokens: number;
            temperature: number;
            llmProvider: string;
        } || {
            model: 'anthropic/claude-3.5-haiku-20241022',
            maxTokens: 4000,
            temperature: 0.7,
            llmProvider: 'anthropic'
        };
        
        // console.log('Настройки генерации:', settings);
        
        // Генерация email с помощью ИИ на основе промпта агента
        const emailContent = await createEmailWithAI({
            agentPrompt: prompt,
            leadData: data.leadData,
            leadHistory: data.leadHistory || [],
            managerInfo: data.managerInfo,
            productInfo: data.productInfo,
            emailType: data.emailType,
            customInstructions: data.customInstructions,
            tone: data.tone,
            settings,
            language: data.language,
            userId: String(userId),
        });
        
        // Сохраняем сгенерированный email в базу данных
        try {
            // Создаем запись о сгенерированном email
            // Сохраняем email в базу данных
            const savedEmail = await db.insert(emails).values({
                // TODO: Удалить после тестирования
                // Lead id Николая ))
                leadId: 28,
                subject: emailContent.subject,
                content: emailContent.body,
                prompt: emailContent.systemPrompt,
                createdBy: String(userId),
                sent: false,
            }).returning({ id: emails.id });
            
            // Добавляем запись в историю email
            if (savedEmail && savedEmail.length > 0) {
                await db.insert(emailHistory).values({
                    emailId: savedEmail[0].id,
                    status: "generated",
                    createdAt: new Date(),
                });
            }
            
            console.log('Email успешно сохранен в базу данных');
        } catch (saveError) {
            // Логируем ошибку, но не прерываем выполнение запроса
            console.error('Ошибка при сохранении email в базу данных:', saveError);
        }
        // Возвращаем результат в структурированном формате
        return NextResponse.json({
            success: true,
            email: {
                subject: emailContent.subject,
                body: emailContent.body,
            },
            agentId: data.agentId,
            requestId: crypto.randomUUID(),
        }, { headers });
        
    } catch (error: any) {
        console.error("Ошибка обработки API-запроса:", error);
        
        // Используем функцию corsHeaders даже для ошибок
        const headers = corsHeaders(req);
        
        return NextResponse.json(
            { 
                error: "Внутренняя ошибка сервера",
                message: error.message || "Неизвестная ошибка" 
            },
            { status: 500, headers }
        );
    }
} 
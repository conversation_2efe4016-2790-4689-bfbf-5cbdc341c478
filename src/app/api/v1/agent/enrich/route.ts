import { NextRequest, NextResponse } from "next/server";
import { verifyApiToken } from "@/utils/api-tokens";
import { extractDataFromUrl } from "@/services/tavily";
import { z } from "zod";

// Схема для запроса обогащения данных
const enrichRequestSchema = z.object({
  url: z.string().url("Требуется действительный URL")
});

// Функция для обработки CORS
function corsHeaders(req: NextRequest) {
  // Проверяем origin запроса
  const origin = req.headers.get("origin") || "*";
  
  return {
    "Access-Control-Allow-Origin": origin,
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
    "Access-Control-Max-Age": "86400",
  };
}

// Обработчик OPTIONS запросов для CORS preflight
export async function OPTIONS(req: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: corsHeaders(req),
  });
}

export async function POST(req: NextRequest) {
  try {
    // Добавляем CORS заголовки к ответу
    const headers = corsHeaders(req);
    
    // Проверка заголовка авторизации
    const authHeader = req.headers.get("Authorization");
    
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Отсутствует или некорректный токен авторизации" },
        { status: 401, headers }
      );
    }
    
    const token = authHeader.split(" ")[1];
    const { valid, userId } = await verifyApiToken(token);
    
    if (!valid) {
      return NextResponse.json(
        { error: "Недействительный API-токен" },
        { status: 401, headers }
      );
    }
    
    // Парсинг и валидация тела запроса
    const body = await req.json();
    const validationResult = enrichRequestSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: "Ошибка валидации данных", 
          details: validationResult.error.format() 
        },
        { status: 400, headers }
      );
    }
    
    const { url } = validationResult.data;
    
    // Извлечение данных с использованием Tavily API
    const extractResult = await extractDataFromUrl(url);
    
    if (!extractResult.success) {
      return NextResponse.json(
        { 
          error: "Ошибка при извлечении данных", 
          details: extractResult.error 
        },
        { status: 500, headers }
      );
    }
    
    // Возвращаем извлеченные данные
    return NextResponse.json({
      success: true,
      data: extractResult.data,
      requestId: crypto.randomUUID(),
    }, { headers });
    
  } catch (error: any) {
    console.error("Ошибка обработки API-запроса:", error);
    
    // Используем функцию corsHeaders даже для ошибок
    const headers = corsHeaders(req);
    
    return NextResponse.json(
      { 
        error: "Внутренняя ошибка сервера",
        message: error.message || "Неизвестная ошибка" 
      },
      { status: 500, headers }
    );
  }
}

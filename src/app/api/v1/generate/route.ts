import { NextResponse } from "next/server"
import { db } from "@/db"
import { eq } from "drizzle-orm"
import { getServerSession } from "next-auth"
import { anthropic } from "@/services/anthropic"
import { formatPrompt } from "@/services/prompts"
import { agents, emails, leads } from "@/db/schema"
import { authOptions } from "@/lib/auth"
import { salesforce } from "@/services/salesforce"
import { tavily } from "@tavily/core"

// interface AgentSettings {
//     model?: string;
//     temperature?: number;
// }

async function prepareVariables(lead: any, _agent: any) {
    // Подготавливаем детали о лиде
    const lead_details = [
        `Role: ${lead.title || 'Not specified'}`,
        `Status: ${lead.status || 'Not specified'}`,
        `Source: ${lead.leadSource || 'Not specified'}`
    ].join('\n');

    // Подготавливаем информацию о компании
    const company_info = [
        `Industry: ${lead.industry || 'Not specified'}`,
        `Size: ${lead.numberOfEmployees ? `${lead.numberOfEmployees} employees` : 'Not specified'}`,
        `Website: ${lead.website || 'Not specified'}`
    ].join('\n');

    const table_lead_history = lead.communications ?
        lead.communications :
        ['No history available'];

    const sf_lead_history = lead.salesforceId ?
        await salesforce.getLeadComprehensiveHistory(lead.salesforceId) :
       table_lead_history;

    // История лида (можно расширить в будущем)
    const lead_history = Array.isArray(sf_lead_history) 
        ? JSON.stringify(sf_lead_history)
        : String(sf_lead_history);

    // Добавляем базовые переменные
    const variables: Record<string, string> = {
        leadFirstName: lead.firstName || '',
        leadLastName: lead.lastName || '',
        leadCompany: lead.company || '',
        leadEmail: lead.email || '',
        lead_details,
        company_info,
        lead_history
    };

    // Добавляем переменные из JSONB поля details
    if (lead.details) {
        Object.entries(lead.details).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                variables[`details_${key}`] = String(value);
            }
        });
    }

    // Добавляем переменные из JSONB поля communications
    if (lead.communications) {
        Object.entries(lead.communications).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                variables[`communications_${key}`] = String(value);
            }
        });
    }

    // Добавляем переменные из JSONB поля company_info
    if (lead.company_info) {
        Object.entries(lead.company_info).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                variables[`company_${key}`] = String(value);
            }
        });
    }

    return variables;
}

export async function POST(req: Request, _agent: any) {
    try {
        const session = await getServerSession(authOptions)
        if (!session?.user) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
        }

        const { leadId, agentId, extraPrompt } = await req.json()

        if (!leadId || !agentId) {
            return NextResponse.json(
                { error: "Missing required fields" },
                { status: 400 }
            )
        }

        // Получаем информацию о лиде
        const lead = await db.query.leads.findFirst({
            where: eq(leads.id, leadId),
        })

        if (!lead) {
            return NextResponse.json(
                { error: "Lead not found" },
                { status: 404 }
            )
        }

        // Получаем информацию об агенте
        const agent = await db.query.agents.findFirst({
            where: eq(agents.id, agentId),
        })

        console.log('agent', agent);

        if (!agent) {
            return NextResponse.json(
                { error: "Agent not found" },
                { status: 404 }
            )
        }

        console.log('lead', lead)

        let variables;
        try {
            // Подготавливаем переменные для промпта
            variables = await prepareVariables(lead, agent);
        } catch (error) {
            console.error('Error preparing variables:', error);
            return NextResponse.json({
                error: 'Error preparing variables',
                details: error
            }, { status: 500 });
        }

        if (extraPrompt) {
            variables.extraPrompt = extraPrompt
        }

        // Форматируем промпт с переменными
        const formattedPrompt = formatPrompt(agent.prompt || '', {
            ...variables,
            senderName: session.user.name!
        });

        const fullPrompt = formattedPrompt
        // Проверяем, содержит ли промпт URL-ы, которые нужно заменить на данные
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        const urls = fullPrompt.match(urlRegex);
        
        if (urls && urls.length > 0) {
            console.log('Found URLs in prompt:', urls);
            
            try {
                // Используем Tavily для извлечения данных по URL
                const tvly = tavily({ apiKey: process.env.NEXT_TAVILY_API_KEY });
                
                // Извлекаем содержимое для каждого URL
                const extractResults = await tvly.extract(urls, {
                    includeRawContent: true
                });
                
                console.log('Extracted content from URLs');
                
                // Заменяем URL-ы на их содержимое в промпте
                let enhancedPrompt = fullPrompt;
                urls.forEach((url, index) => {
                    const content = extractResults.results[index]?.rawContent || 
                                   `[Не удалось извлечь содержимое с ${url}]`;
                    
                    enhancedPrompt = enhancedPrompt.replace(
                        url, 
                        `\n\nСодержимое с ${url}:\n${content}\n\n`  
                    );
                });
                
                // Обновляем промпт с извлеченным содержимым
                console.log('Enhanced prompt with URL content');
            } catch (error) {
                console.error('Error extracting content from URLs:', error);
                // Продолжаем выполнение с исходным промптом, если произошла ошибка
            }
        }
        try {
            console.log('try to generate email')
            const settings = agent.settings as {
                model: string;
                maxTokens: number;
                temperature: number;
            };
            
            // Генерируем email используя Anthropic
            const response = await anthropic.generateEmail(formattedPrompt, {
                model: settings.model,
                max_tokens: settings.maxTokens,
                temperature: settings.temperature,
              });
            console.log('email generated')
            // Форматируем ответ с переменными
            const formattedResponse = {
                ...response,
                subject: formatPrompt(response.subject, {
                    ...variables,
                    senderName: session.user.name!
                }),
                body: formatPrompt(response.body, {
                    ...variables,
                    senderName: session.user.name!
                })
            };

            console.log('formattedResponse', formattedResponse)
            let savedEmail;
            console.log('try to save email')
            try {
                // Сохраняем email в базу данных
                [savedEmail] = await db.insert(emails).values({
                    leadId: lead.id,
                    subject: formattedResponse.subject,
                    content: formattedResponse.body,
                    prompt: fullPrompt,
                    createdBy: session.user.id,
                    sent: false,
                    createdAt: new Date()
                }).returning();
            } catch (err) {
                console.log('saving', err)
                return NextResponse.json({
                    error: 'Error saving email',
                    details: err
                }, { status: 500 });
            }

            console.log('savedEmail', savedEmail)

            return NextResponse.json(savedEmail);
        } catch (error: any) {
            console.log('error', error)
            // Проверяем, является ли ошибка нашей APIError
            if (error.name === 'APIError') {
                return NextResponse.json({
                    type: 'error',
                    error: {
                        type: error.type,
                        message: error.message,
                        request_id: error.requestId,
                        details: error.error
                    }
                }, { 
                    status: error.status,
                    headers: error.requestId ? {
                        'x-request-id': error.requestId
                    } : undefined
                });
            }

            // Для остальных ошибок возвращаем общее сообщение
            return NextResponse.json({
                type: 'error',
                error: {
                    type: 'api_error',
                    message: 'Внутренняя ошибка сервера',
                    details: error.message || error
                }
            }, { status: 500 });
        }
    } catch (error: any) {
        console.error('API Error:', error);
        return NextResponse.json({
            type: 'error',
            error: {
                type: 'api_error',
                message: 'Внутренняя ошибка сервера',
                details: error.message || error
            }
        }, { status: 500 });
    }
}



import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { emailConfigs } from "@/db/schema/email-configs";
import { users } from "@/db/schema/users";
import { getServerSession } from "next-auth";
import { and, eq } from "drizzle-orm";

export async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession();
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const { id } = params;

        // Get the user's ID
        const [user] = await db
            .select()
            .from(users)
            .where(eq(users.email, session.user.email))
            .limit(1);

        if (!user) {
            return NextResponse.json({ error: "User not found" }, { status: 404 });
        }

        // Get the email config and verify it belongs to the user
        const [config] = await db
            .select()
            .from(emailConfigs)
            .where(
                and(
                    eq(emailConfigs.id, id),
                    eq(emailConfigs.createdBy, user.id)
                )
            )
            .limit(1);

        if (!config) {
            return NextResponse.json(
                { error: "Email configuration not found" },
                { status: 404 }
            );
        }

        // Don't send the password back in the response
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { password, ...configWithoutPassword } = config;

        return NextResponse.json(configWithoutPassword);
    } catch (error) {
        console.error("Error fetching email config:", error);
        return NextResponse.json(
            { error: "Failed to fetch email configuration" },
            { status: 500 }
        );
    }
}

export async function DELETE(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession();
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const { id } = params;

        // First get the user's ID
        const [user] = await db
            .select()
            .from(users)
            .where(eq(users.email, session.user.email))
            .limit(1);

        if (!user) {
            return NextResponse.json({ error: "User not found" }, { status: 404 });
        }

        // Verify the email config belongs to the user before deleting
        const result = await db
            .delete(emailConfigs)
            .where(
                and(
                    eq(emailConfigs.id, id),
                    eq(emailConfigs.createdBy, user.id)
                )
            )
            .returning();

        if (!result.length) {
            return NextResponse.json(
                { error: "Email configuration not found" },
                { status: 404 }
            );
        }

        return NextResponse.json(
            { message: "Email configuration deleted successfully" },
            { status: 200 }
        );
    } catch (error) {
        console.error("Error deleting email config:", error);
        return NextResponse.json(
            { error: "Failed to delete email configuration" },
            { status: 500 }
        );
    }
}

export async function PUT(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession();
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // First get the user's ID
        const [user] = await db
            .select()
            .from(users)
            .where(eq(users.email, session.user.email))
            .limit(1);

        if (!user) {
            return NextResponse.json({ error: "User not found" }, { status: 404 });
        }

        const { id } = params;
        const body = await request.json();
        const { smtp, port, email, password } = body;

        // Validate required fields
        if (!smtp || !port || !email) {
            return NextResponse.json(
                { error: "SMTP, port, and email are required" },
                { status: 400 }
            );
        }

        const updateData: any = {
            smtp,
            port: Number(port),
            email,
            updatedAt: new Date(),
        };

        // Only update password if provided
        if (password) {
            updateData.password = password;
        }

        // Verify the email config belongs to the user before updating
        const result = await db
            .update(emailConfigs)
            .set(updateData)
            .where(
                and(
                    eq(emailConfigs.id, id),
                    eq(emailConfigs.createdBy, user.id)
                )
            )
            .returning();

        if (!result.length) {
            return NextResponse.json(
                { error: "Email configuration not found" },
                { status: 404 }
            );
        }

        return NextResponse.json(
            { message: "Email configuration updated successfully" },
            { status: 200 }
        );
    } catch (error) {
        console.error("Error updating email config:", error);
        return NextResponse.json(
            { error: "Failed to update email configuration" },
            { status: 500 }
        );
    }
}

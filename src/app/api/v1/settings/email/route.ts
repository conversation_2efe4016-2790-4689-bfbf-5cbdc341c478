import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { emailConfigs, users } from "@/db/schema";
import { getServerSession } from "next-auth";
import { v4 as uuidv4 } from "uuid";
import { eq } from "drizzle-orm";

export async function GET() {
    try {
        const session = await getServerSession();
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // First get the user id
        const user = await db.query.users.findFirst({
            where: eq(users.email, session.user.email),
        });

        if (!user) {
            return NextResponse.json({ error: "User not found" }, { status: 404 });
        }

        const configs = await db.query.emailConfigs.findMany({
            where: eq(emailConfigs.createdBy, user.id),
            orderBy: (emailConfigs, { desc }) => [desc(emailConfigs.createdAt)],
        });

        // Don't send passwords back to the client
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const configsWithoutPasswords = configs.map(({ password, ...config }) => config);
        return NextResponse.json(configsWithoutPasswords);
    } catch (error) {
        console.error("Error fetching email configs:", error);
        return NextResponse.json(
            { error: "Failed to fetch email configurations" },
            { status: 500 }
        );
    }
}

export async function POST(request: NextRequest) {
    try {
        const session = await getServerSession();
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // First get the user id
        const user = await db.query.users.findFirst({
            where: eq(users.email, session.user.email),
        });

        if (!user) {
            return NextResponse.json({ error: "User not found" }, { status: 404 });
        }

        const body = await request.json();
        const { smtp, port, email, password } = body;

        // Validate required fields
        if (!smtp || !port || !email || !password) {
            return NextResponse.json(
                { error: "All fields are required" },
                { status: 400 }
            );
        }

        // Create new config
        await db.insert(emailConfigs).values({
            id: uuidv4(),
            smtp,
            port: Number(port),
            email,
            password,
            createdBy: user.id,
            createdAt: new Date(),
            updatedAt: new Date(),
        });

        return NextResponse.json(
            { message: "Email configuration saved" },
            { status: 201 }
        );
    } catch (error) {
        console.error("Error saving email config:", error);
        return NextResponse.json(
            { error: "Failed to save email configuration" },
            { status: 500 }
        );
    }
}

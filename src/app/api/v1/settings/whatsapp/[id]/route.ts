import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/db";
import { whatsappConfigs } from "@/db/schema";
import { eq, and } from "drizzle-orm";

export async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const session = await auth();
        if (!session?.user?.id) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const userId = parseInt(session.user.id, 10);
        if (isNaN(userId)) {
            return NextResponse.json({ error: "Invalid user ID" }, { status: 401 });
        }

        const { id } = params;

        const [config] = await db
            .select()
            .from(whatsappConfigs)
            .where(
                and(
                    eq(whatsappConfigs.id, id),
                    eq(whatsappConfigs.createdBy, userId)
                )
            )
            .limit(1);

        if (!config) {
            return NextResponse.json(
                { error: "WhatsApp configuration not found" },
                { status: 404 }
            );
        }

        return NextResponse.json(config);
    } catch (error) {
        console.error("Error fetching WhatsApp config:", error);
        return NextResponse.json(
            { error: "Failed to fetch WhatsApp configuration" },
            { status: 500 }
        );
    }
}

export async function PUT(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const session = await auth();
        if (!session?.user?.id) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const userId = parseInt(session.user.id, 10);
        if (isNaN(userId)) {
            return NextResponse.json({ error: "Invalid user ID" }, { status: 401 });
        }

        const { id } = params;
        const body = await request.json();
        const { apiUrl, profileId, token } = body;

        // Validate required fields
        if (!apiUrl || !profileId) {
            return NextResponse.json(
                { error: "API URL and Profile ID are required" },
                { status: 400 }
            );
        }

        // Validate URL format
        try {
            new URL(apiUrl);
        } catch {
            return NextResponse.json(
                { error: "Invalid API URL format" },
                { status: 400 }
            );
        }

        // Prepare update data
        const updateData: any = {
            apiUrl,
            profileId,
            updatedAt: new Date(),
        };

        // Only update token if provided
        if (token) {
            updateData.token = token;
        }

        const [updatedConfig] = await db
            .update(whatsappConfigs)
            .set(updateData)
            .where(
                and(
                    eq(whatsappConfigs.id, id),
                    eq(whatsappConfigs.createdBy, userId)
                )
            )
            .returning();

        if (!updatedConfig) {
            return NextResponse.json(
                { error: "WhatsApp configuration not found" },
                { status: 404 }
            );
        }

        return NextResponse.json(updatedConfig);
    } catch (error) {
        console.error("Error updating WhatsApp config:", error);
        return NextResponse.json(
            { error: "Failed to update WhatsApp configuration" },
            { status: 500 }
        );
    }
}

export async function DELETE(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const session = await auth();
        if (!session?.user?.id) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const userId = parseInt(session.user.id, 10);
        if (isNaN(userId)) {
            return NextResponse.json({ error: "Invalid user ID" }, { status: 401 });
        }

        const { id } = params;

        const [deletedConfig] = await db
            .delete(whatsappConfigs)
            .where(
                and(
                    eq(whatsappConfigs.id, id),
                    eq(whatsappConfigs.createdBy, userId)
                )
            )
            .returning();

        if (!deletedConfig) {
            return NextResponse.json(
                { error: "WhatsApp configuration not found" },
                { status: 404 }
            );
        }

        return NextResponse.json({ message: "WhatsApp configuration deleted successfully" });
    } catch (error) {
        console.error("Error deleting WhatsApp config:", error);
        return NextResponse.json(
            { error: "Failed to delete WhatsApp configuration" },
            { status: 500 }
        );
    }
}

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/db";
import { whatsappConfigs } from "@/db/schema";
import { eq } from "drizzle-orm";
import { nanoid } from "nanoid";

export async function GET(request: NextRequest) {
    try {
        console.log("WhatsApp API GET: Starting authentication check");

        // Log cookies for debugging
        const cookies = request.headers.get('cookie');
        console.log("WhatsApp API GET: Cookies received:", cookies ? 'present' : 'none');

        const session = await auth();
        console.log("WhatsApp API GET: Session data:", {
            hasSession: !!session,
            hasUser: !!session?.user,
            hasEmail: !!session?.user?.email,
            email: session?.user?.email,
            fullSession: session
        });

        if (!session?.user) {
            console.log("WhatsApp API GET: Authentication failed - no session or user");
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Get user ID from session
        if (!session.user.id) {
            console.log("WhatsApp API GET: No user ID in session");
            return NextResponse.json({ error: "User ID not found" }, { status: 401 });
        }

        const userId = parseInt(session.user.id, 10);
        if (isNaN(userId)) {
            console.log("WhatsApp API GET: Invalid user ID format");
            return NextResponse.json({ error: "Invalid user ID" }, { status: 401 });
        }

        console.log("WhatsApp API GET: Authentication successful for user ID:", userId);

        const configs = await db
            .select()
            .from(whatsappConfigs)
            .where(eq(whatsappConfigs.createdBy, userId));

        return NextResponse.json(configs);
    } catch (error) {
        console.error("Error fetching WhatsApp configs:", error);
        return NextResponse.json(
            { error: "Failed to fetch WhatsApp configurations" },
            { status: 500 }
        );
    }
}

export async function POST(request: NextRequest) {
    try {
        console.log("WhatsApp API POST: Starting authentication check");

        // Log cookies for debugging
        const cookies = request.headers.get('cookie');
        console.log("WhatsApp API POST: Cookies received:", cookies ? 'present' : 'none');

        const session = await auth();
        console.log("WhatsApp API POST: Session data:", {
            hasSession: !!session,
            hasUser: !!session?.user,
            hasEmail: !!session?.user?.email,
            email: session?.user?.email,
            fullSession: session
        });

        if (!session?.user) {
            console.log("WhatsApp API POST: Authentication failed - no session or user");
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Get user ID from session
        if (!session.user.id) {
            console.log("WhatsApp API POST: No user ID in session");
            return NextResponse.json({ error: "User ID not found" }, { status: 401 });
        }

        const userId = parseInt(session.user.id, 10);
        if (isNaN(userId)) {
            console.log("WhatsApp API POST: Invalid user ID format");
            return NextResponse.json({ error: "Invalid user ID" }, { status: 401 });
        }

        console.log("WhatsApp API POST: Authentication successful for user ID:", userId);

        const body = await request.json();
        const { apiUrl, profileId, token } = body;

        // Validate required fields
        if (!apiUrl || !profileId || !token) {
            return NextResponse.json(
                { error: "API URL, Profile ID, and Token are required" },
                { status: 400 }
            );
        }

        // Validate URL format
        try {
            new URL(apiUrl);
        } catch {
            return NextResponse.json(
                { error: "Invalid API URL format" },
                { status: 400 }
            );
        }

        const configId = nanoid();

        const [newConfig] = await db
            .insert(whatsappConfigs)
            .values({
                id: configId,
                apiUrl,
                profileId,
                token,
                createdBy: userId,
            })
            .returning();

        return NextResponse.json(newConfig, { status: 201 });
    } catch (error) {
        console.error("Error creating WhatsApp config:", error);
        return NextResponse.json(
            { error: "Failed to create WhatsApp configuration" },
            { status: 500 }
        );
    }
}

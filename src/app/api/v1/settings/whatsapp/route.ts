import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/db";
import { whatsappConfigs, users } from "@/db/schema";
import { eq } from "drizzle-orm";
import { nanoid } from "nanoid";

export async function GET(request: NextRequest) {
    try {
        console.log("WhatsApp API GET: Starting authentication check");

        // Log cookies for debugging
        const cookies = request.headers.get('cookie');
        console.log("WhatsApp API GET: Cookies received:", cookies ? 'present' : 'none');

        const session = await getServerSession(authOptions);
        console.log("WhatsApp API GET: Session data:", {
            hasSession: !!session,
            hasUser: !!session?.user,
            hasEmail: !!session?.user?.email,
            email: session?.user?.email
        });

        if (!session?.user?.email) {
            console.log("WhatsApp API GET: Authentication failed - no session or email");
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        console.log("WhatsApp API GET: Authentication successful for:", session.user.email);

        const configs = await db
            .select()
            .from(whatsappConfigs)
            .where(eq(whatsappConfigs.createdBy, session.user.email));

        return NextResponse.json(configs);
    } catch (error) {
        console.error("Error fetching WhatsApp configs:", error);
        return NextResponse.json(
            { error: "Failed to fetch WhatsApp configurations" },
            { status: 500 }
        );
    }
}

export async function POST(request: NextRequest) {
    try {
        console.log("WhatsApp API POST: Starting authentication check");

        // Log cookies for debugging
        const cookies = request.headers.get('cookie');
        console.log("WhatsApp API POST: Cookies received:", cookies ? 'present' : 'none');

        const session = await getServerSession(authOptions);
        console.log("WhatsApp API POST: Session data:", {
            hasSession: !!session,
            hasUser: !!session?.user,
            hasEmail: !!session?.user?.email,
            email: session?.user?.email
        });

        if (!session?.user?.email) {
            console.log("WhatsApp API POST: Authentication failed - no session or email");
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        console.log("WhatsApp API POST: Authentication successful for:", session.user.email);

        const body = await request.json();
        const { apiUrl, profileId, token } = body;

        // Validate required fields
        if (!apiUrl || !profileId || !token) {
            return NextResponse.json(
                { error: "API URL, Profile ID, and Token are required" },
                { status: 400 }
            );
        }

        // Validate URL format
        try {
            new URL(apiUrl);
        } catch {
            return NextResponse.json(
                { error: "Invalid API URL format" },
                { status: 400 }
            );
        }

        const configId = nanoid();

        const [newConfig] = await db
            .insert(whatsappConfigs)
            .values({
                id: configId,
                apiUrl,
                profileId,
                token,
                createdBy: session.user.email,
            })
            .returning();

        return NextResponse.json(newConfig, { status: 201 });
    } catch (error) {
        console.error("Error creating WhatsApp config:", error);
        return NextResponse.json(
            { error: "Failed to create WhatsApp configuration" },
            { status: 500 }
        );
    }
}

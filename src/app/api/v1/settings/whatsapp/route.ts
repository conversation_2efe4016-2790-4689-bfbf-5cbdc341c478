import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/db";
import { whatsappConfigs, users } from "@/db/schema";
import { eq } from "drizzle-orm";
import { nanoid } from "nanoid";

export async function GET(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const configs = await db
            .select()
            .from(whatsappConfigs)
            .where(eq(whatsappConfigs.createdBy, session.user.email));

        return NextResponse.json(configs);
    } catch (error) {
        console.error("Error fetching WhatsApp configs:", error);
        return NextResponse.json(
            { error: "Failed to fetch WhatsApp configurations" },
            { status: 500 }
        );
    }
}

export async function POST(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const body = await request.json();
        const { apiUrl, profileId, token } = body;

        // Validate required fields
        if (!apiUrl || !profileId || !token) {
            return NextResponse.json(
                { error: "API URL, Profile ID, and Token are required" },
                { status: 400 }
            );
        }

        // Validate URL format
        try {
            new URL(apiUrl);
        } catch {
            return NextResponse.json(
                { error: "Invalid API URL format" },
                { status: 400 }
            );
        }

        const configId = nanoid();

        const [newConfig] = await db
            .insert(whatsappConfigs)
            .values({
                id: configId,
                apiUrl,
                profileId,
                token,
                createdBy: session.user.email,
            })
            .returning();

        return NextResponse.json(newConfig, { status: 201 });
    } catch (error) {
        console.error("Error creating WhatsApp config:", error);
        return NextResponse.json(
            { error: "Failed to create WhatsApp configuration" },
            { status: 500 }
        );
    }
}

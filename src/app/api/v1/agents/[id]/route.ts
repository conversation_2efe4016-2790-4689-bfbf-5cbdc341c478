import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { db } from '@/db';
import { eq } from 'drizzle-orm';
import { UpdateAgentInput } from '@/types/agent';
import { agents } from '@/db/schema';
import { authOptions } from '@/lib/auth';

export async function GET(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const agent = await db
            .select()
            .from(agents)
            .where(eq(agents.id, parseInt(params.id)))
            .then((res) => res[0]);

        if (!agent) {
            return NextResponse.json(
                { error: 'Agent not found' },
                { status: 404 }
            );
        }

        return NextResponse.json(agent);
    } catch (error) {
        console.error('Error fetching agent:', error);
        return NextResponse.json(
            { error: 'Failed to fetch agent' },
            { status: 500 }
        );
    }
}

export async function PUT(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const data: UpdateAgentInput = await request.json();
        const agent = await db
            .select()
            .from(agents)
            .where(eq(agents.id, parseInt(params.id)))
            .then((res) => res[0]);

        if (!agent) {
            return NextResponse.json(
                { error: 'Agent not found' },
                { status: 404 }
            );
        }

        if (agent.createdBy !== session.user.id) {
            return NextResponse.json(
                { error: 'Unauthorized to modify this agent' },
                { status: 403 }
            );
        }

        const updateData = {
            ...data,
            version: agent.version + 1,
            metadata: {
                ...agent.metadata as Object,
                lastUpdate: {
                    timestamp: new Date().toISOString(),
                    user: session.user.id
                }
            }
        };

        const [updatedAgent] = await db
            .update(agents)
            .set(updateData)
            .where(eq(agents.id, parseInt(params.id)))
            .returning();

        return NextResponse.json(updatedAgent);
    } catch (error) {
        console.error('Error updating agent:', error);
        return NextResponse.json(
            { error: 'Failed to update agent' },
            { status: 500 }
        );
    }
}

export async function DELETE(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const agent = await db
            .select()
            .from(agents)
            .where(eq(agents.id, parseInt(params.id)))
            .then((res) => res[0]);

        if (!agent) {
            return NextResponse.json(
                { error: 'Agent not found' },
                { status: 404 }
            );
        }

        if (agent.createdBy !== session.user.id) {
            return NextResponse.json(
                { error: 'Unauthorized to delete this agent' },
                { status: 403 }
            );
        }

        await db
            .delete(agents)
            .where(eq(agents.id, parseInt(params.id)));

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error('Error deleting agent:', error);
        return NextResponse.json(
            { error: 'Failed to delete agent' },
            { status: 500 }
        );
    }
}

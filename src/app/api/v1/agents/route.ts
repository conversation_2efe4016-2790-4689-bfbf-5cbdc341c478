import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { CreateAgentInput } from '@/types/agent';
import { db } from '@/db';
import { eq, sql, desc } from 'drizzle-orm';
import { agents } from '@/db/schema';
import { authOptions } from '@/lib/auth';

export async function GET(req: Request) {
    try {

        const session = await getServerSession(authOptions);

        if (!session?.user?.id) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const { searchParams } = new URL(req.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '10');
        const offset = (page - 1) * limit;

        const [agentsList, totalCount] = await Promise.all([
            db.select()
                .from(agents)
                .where(eq(agents.createdBy, session.user.id))
                .limit(limit)
                .offset(offset)
                .orderBy(desc(agents.updatedAt)),
            db.select({ count: sql<number>`count(*)` })
                .from(agents)
                .where(eq(agents.createdBy, session.user.id))
                .then(res => Number(res[0].count))
        ]);

        return NextResponse.json({
            agents: agentsList,
            pagination: {
                total: totalCount,
                page,
                limit,
                pages: Math.ceil(totalCount / limit)
            }
        });
    } catch (error) {
        console.error('Error in GET /api/v1/agents:', error);
        return NextResponse.json(
            { error: 'Failed to fetch agents' },
            { status: 500 }
        );
    }
}

export async function POST(req: Request) {
    try {

        const session = await getServerSession(authOptions);

        if (!session?.user?.id) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const data: CreateAgentInput = await req.json();

        const [agent] = await db.insert(agents).values({
            name: data.name,
            description: data.description,
            prompt: data.prompt,
            promptId: data.promptId || null,
            settings: data.settings || {},
            metadata: {},
            version: 1,
            isActive: data.isActive ?? true,
            createdBy: session.user.id
        }).returning();


        return NextResponse.json(agent);
    } catch (error) {
        console.error('Error in POST /api/v1/agents:', error);
        return NextResponse.json(
            { error: 'Failed to create agent' },
            { status: 500 }
        );
    }
}

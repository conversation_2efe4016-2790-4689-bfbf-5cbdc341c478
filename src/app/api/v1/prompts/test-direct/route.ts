import { NextResponse } from 'next/server';
import { anthropic } from '@/services/anthropic';
import { formatPrompt } from '@/services/prompts';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Прямой эндпоинт для тестирования промптов в режиме разработки
export async function POST(request: Request) {
  try {
    // Пропускаем проверку аутентификации в режиме разработки
    if (process.env.NODE_ENV !== 'development') {
      const session = await getServerSession(authOptions);
      if (!session?.user) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }
    } else {
      console.log('[DIRECT] Development mode - skipping authentication check');
    }

    // Получаем данные запроса
    const body = await request.json();
    const { content, variables = {}, settings, testId } = body;

    console.log(`[DIRECT] Starting prompt test generation for test ID: ${testId}`);
    console.log(`[DIRECT] Using model: ${settings.model}, temperature: ${settings.temperature}`);
    console.time("Prompt test generation time");

    if (!content) {
      return NextResponse.json(
        { error: 'Email content template is required' },
        { status: 400 }
      );
    }

    try {
      // Проверяем тестовый режим
      if (process.env.DEBUG_PROMPT_TEST === 'true' || process.env.NODE_ENV === 'development') {
        console.log(`[DIRECT] Using DEBUG mode for test ID: ${testId}`);
      }
      
      // Форматируем промпт с переменными
      const formattedPrompt = formatPrompt(content, variables);
      console.log(`[DIRECT] Prompt formatted, length: ${formattedPrompt.length} chars`);

      // Генерируем email используя Anthropic с отформатированным промптом
      const response = await anthropic.generateEmail(formattedPrompt, {
        model: settings.model,
        max_tokens: settings.maxTokens,
        temperature: settings.temperature,
      });

      // Форматируем тему и тело с переменными
      const formattedResponse = {
        ...response,
        subject: formatPrompt(response.subject, variables),
        body: formatPrompt(response.body, variables),
        formattedPrompt
      };

      console.timeEnd("Prompt test generation time");
      console.log(`[DIRECT] Prompt test generated successfully for test ID: ${testId}`);

      // В режиме разработки возвращаем результат напрямую
      return NextResponse.json({
        success: true,
        result: formattedResponse,
        timestamp: new Date().toISOString()
      });
    } catch (aiError) {
      console.error(`[DIRECT] AI generation error for test ID: ${testId}:`, aiError);
      
      return NextResponse.json(
        { 
          success: false,
          error: aiError instanceof Error ? aiError.message : 'Failed to generate email'
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('[DIRECT] Error in prompt test:', error);
    
    // Более подробное логирование для отладки
    if (error instanceof Error) {
      console.error('[DIRECT] Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
    }
    
    // В режиме разработки возвращаем полный текст ошибки для отладки
    return NextResponse.json(
      { 
        success: false,
        error: process.env.NODE_ENV === 'development' 
          ? `Error: ${error instanceof Error ? error.message : String(error)}` 
          : 'Failed to generate email'
      },
      { status: 500 }
    );
  }
} 
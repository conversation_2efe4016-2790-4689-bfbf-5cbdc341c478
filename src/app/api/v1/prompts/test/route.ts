import { NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { v4 as uuidv4 } from 'uuid';
import { tasks } from "@trigger.dev/sdk/v3";
import { testPromptTask } from "@/trigger/test-prompt";
import { z } from 'zod';

// interface PromptVariables {
//     leadFirstName?: string;
//     leadLastName?: string;
//     leadCompany?: string;
//     leadEmail?: string;
//     lead_details?: string;
//     lead_history?: string;
//     company_info?: string;
//     senderName?: string;
// }

// Схема для валидации входных данных
const inputSchema = z.object({
  content: z.string().min(1, "Шаблон электронного письма обязателен"),
  variables: z.record(z.any()).optional().default({}),
  settings: z.object({
    model: z.string().default("anthropic/claude-3.5-haiku"),
    temperature: z.number().min(0).max(1).default(0.7),
    maxTokens: z.number().min(1).max(4096).default(1024)
  }).default({
    model: "anthropic/claude-3.5-haiku",
    temperature: 0.7,
    maxTokens: 1024
  })
});

export async function POST(request: Request) {
  try {
    // Проверка авторизации
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Парсинг и валидация тела запроса
    const body = await request.json();
    const validationResult = inputSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Ошибка валидации', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { content, variables, settings } = validationResult.data;

    // Добавляем имя отправителя из сессии
    const enrichedVariables = {
      ...variables,
      senderName: session?.user?.name || 'Имя по умолчанию'
    };

    // Генерируем уникальный ID для теста
    const testId = uuidv4();
    
    console.log(`[API] Запуск теста промпта с ID: ${testId}`);

    // Запускаем задачу через trigger.dev и получаем хендл
    const handle = await tasks.trigger<typeof testPromptTask>(
      "test-prompt", 
      {
        content,
        variables: enrichedVariables,
        settings,
        testId
      }
    );

    console.log(`[API] Задача запущена с хендлом ${handle.id}`);

    // Возвращаем данные о задаче клиенту
    return NextResponse.json({
      success: true,
      status: 'processing',
      testId,
      message: 'Тестирование промпта запущено',
      taskId: handle.id,
      // Удаляем taskStatus, так как status не доступен на handle
    });
  } catch (error) {
    console.error('Ошибка в тесте промпта:', error);
    
    return NextResponse.json(
      { 
        success: false,
        status: 'error',
        error: error instanceof Error ? error.message : 'Не удалось запустить тест промпта'
      },
      { status: 500 }
    );
  }
}

// API-маршрут для проверки статуса задачи (получение результатов из БД)
export async function GET(request: Request) {
  try {
    // Проверка авторизации
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Получаем testId из URL
    const { searchParams } = new URL(request.url);
    const testId = searchParams.get('testId');

    if (!testId) {
      return NextResponse.json(
        { error: 'ID теста не указан' },
        { status: 400 }
      );
    }

    // Перенаправляем на обобщенный эндпоинт для получения результатов
    const callbackUrl = new URL('/api/v1/prompts/test-callback', request.url);
    callbackUrl.searchParams.set('id', testId);
    
    const response = await fetch(callbackUrl.toString(), {
      headers: {
        'Cookie': request.headers.get('cookie') || '' // Передаем куки для авторизации
      }
    });
    
    const result = await response.json();
    return NextResponse.json(result, { status: response.status });
    
  } catch (error) {
    console.error('Ошибка при получении статуса теста:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Не удалось получить статус теста'
      },
      { status: 500 }
    );
  }
}

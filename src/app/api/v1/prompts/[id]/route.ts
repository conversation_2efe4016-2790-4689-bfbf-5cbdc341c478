import { NextResponse } from 'next/server';
import { db } from '@/db';
import { prompts } from '@/db/schema/prompts';
import { eq, and } from 'drizzle-orm';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json({ error: 'Не авторизован' }, { status: 401 });
        }

        const prompt = await db
            .select()
            .from(prompts)
            .where(and(
                eq(prompts.id, parseInt(params.id)),
                eq(prompts.createdBy, session.user.id)
            ))
            .limit(1)
            .then(res => res[0]);

        if (!prompt) {
            return NextResponse.json(
                { error: 'Промпт не найден или нет доступа' },
                { status: 404 }
            );
        }

        return NextResponse.json({ prompt });
    } catch (error) {
        console.error('Ошибка при получении промпта:', error);
        return NextResponse.json(
            { error: 'Не удалось получить промпт' },
            { status: 500 }
        );
    }
}

export async function DELETE(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json({ error: 'Не авторизован' }, { status: 401 });
        }

        // Проверяем, существует ли промпт и принадлежит ли он пользователю
        const prompt = await db
            .select()
            .from(prompts)
            .where(and(
                eq(prompts.id, parseInt(params.id)),
                eq(prompts.createdBy, session.user.id)
            ))
            .limit(1);

        if (!prompt.length) {
            return NextResponse.json(
                { error: 'Промпт не найден или нет доступа' },
                { status: 404 }
            );
        }

        await db
            .delete(prompts)
            .where(and(
                eq(prompts.id, parseInt(params.id)),
                eq(prompts.createdBy, session.user.id)
            ));

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error('Ошибка при удалении промпта:', error);
        return NextResponse.json(
            { error: 'Не удалось удалить промпт' },
            { status: 500 }
        );
    }
}

export async function PATCH(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json({ error: 'Не авторизован' }, { status: 401 });
        }

        const body = await request.json();
        const { isActive } = body;

        if (typeof isActive !== 'boolean') {
            return NextResponse.json(
                { error: 'Неверный формат данных' },
                { status: 400 }
            );
        }

        // Проверяем, существует ли промпт и принадлежит ли он пользователю
        const prompt = await db
            .select()
            .from(prompts)
            .where(and(
                eq(prompts.id, parseInt(params.id)),
                eq(prompts.createdBy, session.user.id)
            ))
            .limit(1);

        if (!prompt.length) {
            return NextResponse.json(
                { error: 'Промпт не найден или нет доступа' },
                { status: 404 }
            );
        }

        const result = await db
            .update(prompts)
            .set({
                isActive,
                updatedAt: new Date()
            })
            .where(and(
                eq(prompts.id, parseInt(params.id)),
                eq(prompts.createdBy, session.user.id)
            ))
            .returning();

        return NextResponse.json({ prompt: result[0] });
    } catch (error) {
        console.error('Ошибка при обновлении статуса промпта:', error);
        return NextResponse.json(
            { error: 'Не удалось обновить статус промпта' },
            { status: 500 }
        );
    }
}

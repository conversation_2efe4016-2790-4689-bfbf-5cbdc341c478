import { NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/db';
import { promptTests } from '@/db/schema/prompt-tests';
import { eq } from 'drizzle-orm';

export async function GET(request: Request) {
  try {
    // Проверка авторизации
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    // Получаем ID теста из URL
    const url = new URL(request.url);
    const testId = url.searchParams.get('id');
    
    if (!testId) {
      return NextResponse.json(
        { error: 'Требуется ID теста' },
        { status: 400 }
      );
    }
    
    // Запрашиваем результат из БД по ID теста
    const testResults = await db.select()
      .from(promptTests)
      .where(eq(promptTests.id, testId));
    
    // Если тест не найден
    if (!testResults || testResults.length === 0) {
      return NextResponse.json(
        { status: 'pending', message: 'Тест не найден' },
        { status: 202 }
      );
    }
    
    const testResult = testResults[0];
    
    // Если тест все еще выполняется
    if (testResult.status === 'PENDING' || testResult.status === 'PROCESSING') {
      return NextResponse.json(
        { status: 'pending', message: 'Тест выполняется' },
        { status: 202 }
      );
    }
    
    // Если тест завершился с ошибкой
    if (testResult.status === 'ERROR') {
      return NextResponse.json({
        success: false,
        status: 'error',
        error: testResult.error || 'Произошла ошибка при выполнении теста',
        testId: testResult.id
      });
    }
    
    // Если тест успешно завершен
    return NextResponse.json({
      success: true,
      status: 'completed',
      result: testResult.result,
      testId: testResult.id
    });
    
  } catch (error) {
    console.error('Ошибка при получении результата теста:', error);
    return NextResponse.json(
      { error: 'Не удалось получить результат теста' },
      { status: 500 }
    );
  }
} 
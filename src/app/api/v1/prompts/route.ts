import { NextResponse } from 'next/server';
import { db } from '@/db';
import { prompts } from '@/db/schema/prompts';
import { eq, and, desc, sql, asc } from 'drizzle-orm';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Определяем допустимые поля для сортировки
type SortableFields = keyof typeof prompts._.columns;

export async function GET(request: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json({ error: 'Не авторизован' }, { status: 401 });
        }

        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '10');
        const search = searchParams.get('search') || '';
        const type = searchParams.get('type') || '';
        const sortBy = (searchParams.get('sortBy') || 'updatedAt') as SortableFields;
        const sortOrder = searchParams.get('sortOrder') || 'desc';

        const offset = (page - 1) * limit;

        // Формируем условия поиска
        const searchConditions = [];
        searchConditions.push(eq(prompts.createdBy, session.user.id));

        if (search) {
            searchConditions.push(
                sql`(${prompts.title} ILIKE ${`%${search}%`} OR ${prompts.content} ILIKE ${`%${search}%`} OR ${prompts.description} ILIKE ${`%${search}%`})`
            );
        }

        if (type) {
            searchConditions.push(eq(prompts.type, type));
        }

        // Получаем промпты
        const userPrompts = await db
            .select()
            .from(prompts)
            .where(and(...searchConditions))
            .orderBy(sortOrder === 'desc' ? desc(prompts[sortBy]) : asc(prompts[sortBy]))
            .limit(limit)
            .offset(offset);

        // Получаем общее количество
        const totalCount = await db
            .select({ count: sql`count(*)` })
            .from(prompts)
            .where(and(...searchConditions));

        return NextResponse.json({
            prompts: userPrompts,
            pagination: {
                total: Number(totalCount[0].count),
                page,
                limit,
                pages: Math.ceil(Number(totalCount[0].count) / limit)
            }
        });
    } catch (error) {
        console.error('Ошибка при получении промптов:', error);
        return NextResponse.json(
            { error: 'Не удалось получить промпты' },
            { status: 500 }
        );
    }
}

export async function POST(request: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json({ error: 'Не авторизован' }, { status: 401 });
        }

        const body = await request.json();
        const { title, content, type = 'system', description = '', settings = {}, metadata = {} } = body;

        if (!title || !content) {
            return NextResponse.json(
                { error: 'Заголовок и содержание обязательны' },
                { status: 400 }
            );
        }

        const result = await db.insert(prompts).values({
            title,
            content,
            type,
            description,
            settings,
            metadata,
            createdBy: session.user.id,
            version: 1,
            isActive: true
        }).returning();

        return NextResponse.json(result[0]);
    } catch (error) {
        console.error('Ошибка при создании промпта:', error);
        return NextResponse.json(
            { error: 'Не удалось создать промпт' },
            { status: 500 }
        );
    }
}

export async function PUT(request: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json({ error: 'Не авторизован' }, { status: 401 });
        }

        const body = await request.json();
        const { id, ...updateData } = body;

        if (!id) {
            return NextResponse.json(
                { error: 'ID обязателен' },
                { status: 400 }
            );
        }

        // Проверяем, принадлежит ли промпт пользователю
        const prompt = await db.select()
            .from(prompts)
            .where(and(
                eq(prompts.id, id),
                eq(prompts.createdBy, session.user.id)
            ))
            .limit(1);

        if (!prompt.length) {
            return NextResponse.json(
                { error: 'Промпт не найден или нет доступа' },
                { status: 404 }
            );
        }

        const result = await db.update(prompts)
            .set({
                ...updateData,
                version: prompt[0].version + 1,
                updatedAt: new Date()
            })
            .where(eq(prompts.id, id))
            .returning();

        return NextResponse.json({ prompt: result[0] });
    } catch (error) {
        console.error('Ошибка при обновлении промпта:', error);
        return NextResponse.json(
            { error: 'Не удалось обновить промпт' },
            { status: 500 }
        );
    }
}

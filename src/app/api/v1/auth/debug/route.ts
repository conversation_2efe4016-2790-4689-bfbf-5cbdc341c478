import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

export async function GET(request: NextRequest) {
    try {
        console.log("Auth Debug: Starting authentication check");
        
        // Log all headers for debugging
        const headers = Object.fromEntries(request.headers.entries());
        console.log("Auth Debug: Request headers:", headers);
        
        // Log cookies specifically
        const cookies = request.headers.get('cookie');
        console.log("Auth Debug: Raw cookies:", cookies);
        
        // Parse cookies manually
        const cookieObj: Record<string, string> = {};
        if (cookies) {
            cookies.split(';').forEach(cookie => {
                const [name, value] = cookie.trim().split('=');
                if (name && value) {
                    cookieObj[name] = value;
                }
            });
        }
        console.log("Auth Debug: Parsed cookies:", cookieObj);
        
        // Check for NextAuth session token
        const sessionTokenNames = [
            'next-auth.session-token',
            '__Secure-next-auth.session-token',
            '__Host-next-auth.session-token'
        ];
        
        const foundTokens = sessionTokenNames.filter(name => cookieObj[name]);
        console.log("Auth Debug: Found session tokens:", foundTokens);
        
        // Try to get session
        const session = await getServerSession(authOptions);
        console.log("Auth Debug: Session result:", {
            hasSession: !!session,
            hasUser: !!session?.user,
            hasEmail: !!session?.user?.email,
            email: session?.user?.email,
            userId: session?.user?.id,
            userName: session?.user?.name,
            accessToken: session?.accessToken ? 'present' : 'none',
            refreshToken: session?.refreshToken ? 'present' : 'none',
            instanceUrl: session?.instanceUrl,
            fullSessionData: session
        });
        
        // Check environment variables
        console.log("Auth Debug: Environment check:", {
            hasNextAuthUrl: !!process.env.NEXTAUTH_URL,
            hasNextAuthSecret: !!process.env.NEXTAUTH_SECRET,
            nodeEnv: process.env.NODE_ENV,
            nextAuthUrl: process.env.NEXTAUTH_URL
        });
        
        return NextResponse.json({
            success: true,
            debug: {
                hasSession: !!session,
                hasUser: !!session?.user,
                hasEmail: !!session?.user?.email,
                email: session?.user?.email,
                userId: session?.user?.id,
                userName: session?.user?.name,
                foundTokens,
                sessionData: session,
                environment: {
                    nodeEnv: process.env.NODE_ENV,
                    nextAuthUrl: process.env.NEXTAUTH_URL,
                    hasSecret: !!process.env.NEXTAUTH_SECRET
                }
            }
        });
    } catch (error) {
        console.error("Auth Debug: Error:", error);
        return NextResponse.json(
            { 
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
                stack: error instanceof Error ? error.stack : undefined
            },
            { status: 500 }
        );
    }
}

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { apiTokens } from "@/db/schema/api-tokens";
import { eq } from "drizzle-orm";
import { auth } from "@/lib/auth";
import { generateApiToken } from "@/utils/api-tokens";
import { z } from "zod";

// Схема для создания API-токена
const createTokenSchema = z.object({
  name: z.string().min(1, "Имя токена обязательно"),
  description: z.string().optional(),
  token: z.string().optional(),
});

// Схема для обновления API-токена
const updateTokenSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
  expiresAt: z.date().optional().nullable(),
});

// Получение списка токенов пользователя
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Требуется авторизация" },
        { status: 401 }
      );
    }
    
    // Используем ID напрямую из сессии
    const userId = session.user.id;
    
    const tokens = await db
      .select()
      .from(apiTokens)
      .where(eq(apiTokens.userId, Number(userId)))
      .orderBy(apiTokens.createdAt);
    
    return NextResponse.json({
      success: true,
      tokens: tokens.map(token => ({
        ...token,
        token: `${token.token.substring(0, 10)}...`, // Отправляем только часть токена в целях безопасности
      })),
    });
  } catch (error: any) {
    console.error("Ошибка получения API-токенов:", error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: "Ошибка получения API-токенов",
        message: error.message
      },
      { status: 500 }
    );
  }
}

// Создание нового API-токена
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Требуется авторизация" },
        { status: 401 }
      );
    }
    
    const body = await req.json();
    
    // Валидация данных
    const validationResult = createTokenSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: "Ошибка валидации данных", 
          details: validationResult.error.format() 
        },
        { status: 400 }
      );
    }
    
    const data = validationResult.data;
    
    // Получаем userId из сессии
    const userId = session.user.id;
    
    // Создаем новый токен, если он не был передан
    const token = data.token || generateApiToken();
    
    // Сохраняем токен в базе данных
    const [result] = await db
      .insert(apiTokens)
      .values({
        name: data.name,
        description: data.description,
        token,
        userId: Number(userId), // Используем ID из сессии
      })
      .returning();
    
    return NextResponse.json({
      success: true,
      token: result,
    });
  } catch (error: any) {
    console.error("Ошибка создания API-токена:", error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: "Ошибка создания API-токена",
        message: error.message
      },
      { status: 500 }
    );
  }
} 
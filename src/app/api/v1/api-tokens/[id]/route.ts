import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { apiTokens } from "@/db/schema/api-tokens";
import { eq, and } from "drizzle-orm";
import { auth } from "@/lib/auth";
import { z } from "zod";

// Схема для обновления токена
const updateTokenSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
  expiresAt: z.string().optional().nullable(),
});

// Обновление токена
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Требуется авторизация" },
        { status: 401 }
      );
    }
    
    const tokenId = Number(params.id);
    
    if (isNaN(tokenId)) {
      return NextResponse.json(
        { error: "Некорректный ID токена" },
        { status: 400 }
      );
    }
    
    // Проверяем, что токен принадлежит текущему пользователю
    const token = await db.query.apiTokens.findFirst({
      where: eq(apiTokens.id, tokenId),
    });
    
    if (!token) {
      return NextResponse.json(
        { error: "Токен не найден" },
        { status: 404 }
      );
    }
    
    if (String(token.userId) !== String(session.user.id)) {
      console.log("Token Update - User IDs:", { 
        tokenUserId: token.userId, 
        sessionUserId: session.user.id,
        typesInfo: {
          tokenUserIdType: typeof token.userId,
          sessionUserIdType: typeof session.user.id
        } 
      });
      
      return NextResponse.json(
        { error: "У вас нет прав для обновления этого токена" },
        { status: 403 }
      );
    }
    
    const body = await req.json();
    
    // Валидация данных
    const validationResult = updateTokenSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: "Ошибка валидации данных", 
          details: validationResult.error.format() 
        },
        { status: 400 }
      );
    }
    
    const data = validationResult.data;
    
    // Преобразуем строку даты в объект Date, если она указана
    const expiresAt = data.expiresAt ? new Date(data.expiresAt) : undefined;
    
    // Обновляем токен
    const [updatedToken] = await db
      .update(apiTokens)
      .set({
        ...data,
        expiresAt,
        updatedAt: new Date(),
      })
      .where(eq(apiTokens.id, tokenId))
      .returning();
    
    return NextResponse.json({
      success: true,
      token: {
        ...updatedToken,
        token: `${updatedToken.token.substring(0, 10)}...`,
      },
    });
  } catch (error: any) {
    console.error("Ошибка обновления API-токена:", error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: "Ошибка обновления API-токена",
        message: error.message
      },
      { status: 500 }
    );
  }
}

// Удаление токена
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Требуется авторизация" },
        { status: 401 }
      );
    }
    
    const tokenId = Number(params.id);
    
    if (isNaN(tokenId)) {
      return NextResponse.json(
        { error: "Некорректный ID токена" },
        { status: 400 }
      );
    }
    
    // Проверяем, что токен принадлежит текущему пользователю
    const token = await db.query.apiTokens.findFirst({
      where: eq(apiTokens.id, tokenId),
    });
    
    if (!token) {
      return NextResponse.json(
        { error: "Токен не найден" },
        { status: 404 }
      );
    }
    
    if (String(token.userId) !== String(session.user.id)) {
      console.log("Token Delete - User IDs:", { 
        tokenUserId: token.userId, 
        sessionUserId: session.user.id,
        typesInfo: {
          tokenUserIdType: typeof token.userId,
          sessionUserIdType: typeof session.user.id
        } 
      });
      
      return NextResponse.json(
        { error: "У вас нет прав для удаления этого токена" },
        { status: 403 }
      );
    }
    
    // Удаляем токен
    await db
      .delete(apiTokens)
      .where(eq(apiTokens.id, tokenId));
    
    return NextResponse.json({
      success: true,
      message: "Токен успешно удален",
    });
  } catch (error: any) {
    console.error("Ошибка удаления API-токена:", error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: "Ошибка удаления API-токена",
        message: error.message
      },
      { status: 500 }
    );
  }
} 
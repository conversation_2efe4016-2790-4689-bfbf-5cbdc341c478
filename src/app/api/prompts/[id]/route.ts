import { NextResponse } from "next/server";
import { db } from "@/db";
import { prompts } from "@/db/schema/prompts";
import { eq } from "drizzle-orm";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

export async function GET(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const prompt = await db
            .select()
            .from(prompts)
            .where(eq(prompts.id, parseInt(params.id)))
            .then((res) => res[0]);

        if (!prompt) {
            return NextResponse.json({ error: "Prompt not found" }, { status: 404 });
        }

        return NextResponse.json(prompt);
    } catch (error) {
        console.error("Error fetching prompt:", error);
        return NextResponse.json(
            { error: "Failed to fetch prompt" },
            { status: 500 }
        );
    }
}

export async function PUT(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const data = await request.json();
        const prompt = await db
            .select()
            .from(prompts)
            .where(eq(prompts.id, parseInt(params.id)))
            .then((res) => res[0]);

        if (!prompt) {
            return NextResponse.json({ error: "Prompt not found" }, { status: 404 });
        }

        if (prompt.createdBy !== session.user.id) {
            return NextResponse.json(
                { error: "Unauthorized to modify this prompt" },
                { status: 403 }
            );
        }

        const updateData = {
            ...data,
            version: prompt.version + 1,
            isActive: data.isActive ?? prompt.isActive,
            settings: data.settings || prompt.settings,
            metadata: {
                ...prompt.metadata as Object,
                ...data.metadata,
                lastUpdate: {
                    timestamp: new Date().toISOString(),
                    user: session.user.id
                }
            }
        };

        const updatedPrompt = await db
            .update(prompts)
            .set(updateData)
            .where(eq(prompts.id, parseInt(params.id)))
            .returning();

        return NextResponse.json(updatedPrompt[0]);
    } catch (error) {
        console.error("Error updating prompt:", error);
        return NextResponse.json(
            { error: "Failed to update prompt" },
            { status: 500 }
        );
    }
}

export async function DELETE(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const prompt = await db
            .select()
            .from(prompts)
            .where(eq(prompts.id, parseInt(params.id)))
            .then((res) => res[0]);

        if (!prompt) {
            return NextResponse.json({ error: "Prompt not found" }, { status: 404 });
        }

        if (prompt.createdBy !== session.user.email && prompt.createdBy !== session.user.id) {
            return NextResponse.json(
                { error: "Unauthorized to delete this prompt" },
                { status: 403 }
            );
        }

        await db.delete(prompts).where(eq(prompts.id, parseInt(params.id)));
        return NextResponse.json({ success: true });
    } catch (error) {
        console.error("Error deleting prompt:", error);
        return NextResponse.json(
            { error: "Failed to delete prompt" },
            { status: 500 }
        );
    }
}

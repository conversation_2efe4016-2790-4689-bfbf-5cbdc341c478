import { NextResponse } from "next/server";
import { db } from "@/db";
import { prompts } from "@/db/schema/prompts";
import { eq, desc } from "drizzle-orm";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

export async function GET() {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }
        const allPrompts = await db.select().from(prompts).where(eq(prompts.createdBy, session.user.id)).orderBy(desc(prompts.createdAt));
        return NextResponse.json(allPrompts);
    } catch (error) {
        console.error("Error fetching prompts:", error);
        return NextResponse.json(
            { error: "Failed to fetch prompts" },
            { status: 500 }
        );
    }
}

export async function POST(request: Request) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const data = await request.json();
        const newPrompt = await db.insert(prompts).values({
            ...data,
            createdBy: session.user.id,
            version: 1,
            isActive: true,
            settings: data.settings || {},
            metadata: data.metadata || {}
        }).returning();

        return NextResponse.json(newPrompt[0]);
    } catch (error) {
        console.error("Error creating prompt:", error);
        return NextResponse.json(
            { error: "Failed to create prompt" },
            { status: 500 }
        );
    }
}

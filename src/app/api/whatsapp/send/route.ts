import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { WhatsAppService } from "@/services/whatsapp";

export async function POST(request: NextRequest) {
    try {
        const session = await auth();
        
        if (!session?.user?.email) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const { messageId, whatsappConfigId } = await request.json();

        if (!messageId) {
            return NextResponse.json(
                { error: "Message ID is required" },
                { status: 400 }
            );
        }

        const [result, tokenUsage] = await WhatsAppService.sendMessage(
            messageId,
            whatsappConfigId
        );

        return NextResponse.json({ 
            success: true,
            message: result,
            tokenUsage 
        });
    } catch (error) {
        console.error("Error sending WhatsApp message:", error);
        return NextResponse.json(
            { 
                success: false,
                error: error instanceof Error ? error.message : "An error occurred" 
            },
            { status: 500 }
        );
    }
}

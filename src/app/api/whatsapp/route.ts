import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/db";
import { leads, whatsappMessages } from "@/db/schema";
import { eq } from "drizzle-orm";
import { WhatsAppService } from "@/services/whatsapp";

export async function POST(request: NextRequest) {
    try {
        const session = await auth();
        
        if (!session?.user?.email) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const { leadId, agentId } = await request.json();

        // Get lead details using id
        const lead = await db
            .select()
            .from(leads)
            .where(eq(leads.id, leadId))
            .limit(1);

        if (!lead[0]) {
            return NextResponse.json({ error: 'Lead not found' }, { status: 404 });
        }

        // Check if lead has phone number
        if (!lead[0].phone) {
            return NextResponse.json(
                { error: 'Lead does not have a phone number' },
                { status: 400 }
            );
        }

        // Generate WhatsApp message using AI
        const messageData = await WhatsAppService.generatePersonalizedMessage(
            lead[0].id,
            undefined, // customPrompt
            agentId ? parseInt(agentId) : undefined
        );

        // Get the saved message from database
        const [savedMessage] = await db
            .select()
            .from(whatsappMessages)
            .where(eq(whatsappMessages.id, messageData.message_id))
            .limit(1);

        return NextResponse.json({
            ...savedMessage,
            phoneNumber: messageData.phone_number,
        });
    } catch (error) {
        console.error('Error generating WhatsApp message:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Failed to generate WhatsApp message' },
            { status: 500 }
        );
    }
}

export async function GET(request: NextRequest) {
    try {
        const session = await auth();

        if (!session?.user) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const { searchParams } = new URL(request.url);
        const leadId = searchParams.get('leadId');

        if (!leadId) {
            return NextResponse.json(
                { error: 'Lead ID is required' },
                { status: 400 }
            );
        }

        // Get WhatsApp messages for the lead
        const messages = await db
            .select({
                message: whatsappMessages,
                lead: {
                    firstName: leads.firstName,
                    lastName: leads.lastName,
                    company: leads.company,
                }
            })
            .from(whatsappMessages)
            .innerJoin(leads, eq(whatsappMessages.leadId, leads.id))
            .where(eq(whatsappMessages.leadId, parseInt(leadId)))
            .orderBy(whatsappMessages.createdAt);

        return NextResponse.json(messages);
    } catch (error) {
        console.error('Error fetching WhatsApp messages:', error);
        return NextResponse.json(
            { error: 'Failed to fetch WhatsApp messages' },
            { status: 500 }
        );
    }
}

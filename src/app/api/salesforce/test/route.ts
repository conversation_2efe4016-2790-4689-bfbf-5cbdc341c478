import { NextResponse } from 'next/server';
import { salesforce } from '@/services/salesforce';

export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    // Get some test data
    const testData = {
      connection: 'Success',
      timestamp: new Date().toISOString(),
      leads: await salesforce.getLeads()
    };
    
    return NextResponse.json(testData);
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Failed to connect to Salesforce' },
      { status: 500 }
    );
  }
}

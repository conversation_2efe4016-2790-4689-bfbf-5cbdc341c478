import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { db } from '@/db';
import { leads, emails } from '@/db/schema';
import { emailGenerator } from '@/services/openai';
import { eq, desc, and } from 'drizzle-orm';
import { auth } from '@/lib/auth';

export async function POST(request: NextRequest) {
    try {
        const session = await auth();
        
        if (!session?.user?.email) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const { leadId } = await request.json();

        // Get lead details using id
        const lead = await db
            .select()
            .from(leads)
            .where(eq(leads.id, leadId))
            .limit(1);

        if (!lead[0]) {
            return NextResponse.json({ error: 'Lead not found' }, { status: 404 });
        }

        // Generate email using OpenAI
        const { subject, content, prompt } = await emailGenerator.generateEmail({
            firstName: lead[0].firstName,
            lastName: lead[0].lastName,
            company: lead[0].company ?? 'Unknown Company',
            title: lead[0].title,
            status: lead[0].status,
            salesforceId: lead[0].salesforceId ?? '',
            email: lead[0].email
        });

        // Save email to database
        const [savedEmail] = await db
            .insert(emails)
            .values({
                leadId: lead[0].id,
                subject,
                content,
                prompt,
                sent: false,
                createdBy: `${session.user.id}`,
            })
            .returning();

        return NextResponse.json(savedEmail);
    } catch (error) {
        console.error('Error generating email:', error);
        return NextResponse.json(
            { error: 'Failed to generate email' },
            { status: 500 }
        );
    }
}

export async function GET(request: NextRequest) {
    try {
        const session = await auth();
        console.log('session email', session)

        if (!session?.user) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const searchParams = request.nextUrl.searchParams;
        const leadId = searchParams.get('leadId');

        const baseQuery = db
            .select({
                id: emails.id,
                subject: emails.subject,
                content: emails.content,
                prompt: emails.prompt,
                leadId: emails.leadId,
                sent: emails.sent,
                sentAt: emails.sentAt,
                createdAt: emails.createdAt,
                createdBy: emails.createdBy,
                leadFirstName: leads.firstName,
                leadLastName: leads.lastName,
                leadCompany: leads.company,
            })
            .from(emails)
            .leftJoin(leads, eq(emails.leadId, leads.id))
            .where(
                leadId 
                    ? and(
                        eq(emails.createdBy, `${session.user.id}`),
                        eq(emails.leadId, parseInt(leadId, 10))
                      )
                    : eq(emails.createdBy, `${session.user.id}`)
            )
            .orderBy(desc(emails.createdAt));

        const allEmails = await baseQuery;
        return NextResponse.json(allEmails);
    } catch (error) {
        console.error('Error fetching emails:', error);
        return NextResponse.json(
            { error: 'Failed to fetch emails' },
            { status: 500 }
        );
    }
}

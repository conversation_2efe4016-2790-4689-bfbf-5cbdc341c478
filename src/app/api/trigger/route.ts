// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import type { helloWorldTask } from "@/trigger/example";
import { tasks } from "@trigger.dev/sdk/v3";
import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";

//tasks.trigger also works with the edge runtime
//export const runtime = "edge";

export async function GET() {
  const handle = await tasks.trigger<typeof helloWorldTask>(
    "hello-world",
    "James"
  );

  return NextResponse.json(handle);
}

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const body = await req.json();
    const { leadId, emailConfigId } = body;

    if (!leadId || !emailConfigId) {
      return new NextResponse('Missing required parameters', { status: 400 });
    }

    return NextResponse.json({});

  } catch (error: any) {
    console.error('Error in email trigger:', error);
    return new NextResponse(error.message || 'Internal Server Error', {
      status: error.status || 500
    });
  }
}

import { salesforce } from "@/services/salesforce";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const salesforceId = request.nextUrl.searchParams.get("salesforceId");

    if (!salesforceId) {
      return NextResponse.json(
        { error: "Salesforce ID is required" },
        { status: 400 }
      );
    }

    const history = await salesforce.getLeadComprehensiveHistory(salesforceId);
    return NextResponse.json(history);
  } catch (error) {
    console.error("Error in lead history route:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An error occurred" },
      { status: 500 }
    );
  }
}

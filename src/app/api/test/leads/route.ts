import { salesforce } from "@/services/salesforce";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const salesforceId = request.nextUrl.searchParams.get("salesforceId");

    if (salesforceId) {
      const lead = await salesforce.getLeadById(salesforceId);
      return NextResponse.json(lead);
    } else {
      const leads = await salesforce.getLeads();
      return NextResponse.json(leads);
    }
  } catch (error) {
    console.error("Error in leads route:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An error occurred" },
      { status: 500 }
    );
  }
}

import { EmailService } from "@/services/email";
import { getServerSession } from "next-auth";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  try {
    // Get the salesforce ID from query parameters
    const { searchParams } = new URL(request.url);
    const salesforceId = searchParams.get("salesforceId");

    if (!salesforceId) {
      return NextResponse.json(
        { error: "Salesforce ID is required" },
        { status: 400 }
      );
    }

    const [result, tokenUsage] = await EmailService.sendEmail(
      undefined,
      salesforceId
    );
    return NextResponse.json({ result, tokenUsage });
  } catch (error) {
    console.error("Error sending email:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An error occurred" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession()
    const body = await request.json();
    const { emailId, emailConfigId } = body;

    if (session?.user?.email) {
        if (!emailId) {
        return NextResponse.json(
            { error: "emailId is required" },
            { status: 400 }
        );
        }

        if (!emailConfigId) {
        return NextResponse.json(
            { error: "emailConfigId is required" },
            { status: 400 }
        );
        }

        const [result, tokenUsage] = await EmailService.sendEmail(emailId, undefined, emailConfigId);
        return NextResponse.json({ result, tokenUsage });
    } else {
        if (!emailId) {
        return NextResponse.json(
            { error: "emailId is required" },
            { status: 400 }
        );
        }

        const [result, tokenUsage] = await EmailService.sendEmail(emailId);
        return NextResponse.json({ result, tokenUsage });
    }
  } catch (error) {
    console.error("Error sending email:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An error occurred" },
      { status: 500 }
    );
  }
}

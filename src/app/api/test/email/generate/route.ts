import { EmailService } from "@/services/email";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  try {
    // Get the salesforce ID from query parameters
    const { searchParams } = new URL(request.url);
    const salesforceId = searchParams.get("salesforceId");

    if (!salesforceId) {
      return NextResponse.json(
        { error: "Salesforce ID is required" },
        { status: 400 }
      );
    }

    const email = await EmailService.generatePersonalizedEmail(salesforceId);
    return NextResponse.json(email);
  } catch (error) {
    console.error("Error generating email:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An error occurred" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    // Get salesforceId and prompt from request body
    const { salesforceId, prompt } = await request.json();

    // Validate required parameters
    if (!salesforceId) {
      return NextResponse.json(
        { error: "Salesforce ID is required" },
        { status: 400 }
      );
    }

    if (!prompt) {
      return NextResponse.json(
        { error: "Prompt is required" },
        { status: 400 }
      );
    }

    // Generate email using EmailService with custom prompt
    const email = await EmailService.generatePersonalizedEmail(
      salesforceId,
      prompt
    );

    return NextResponse.json(email);
  } catch (error) {
    console.error("Error generating email:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An error occurred" },
      { status: 500 }
    );
  }
}

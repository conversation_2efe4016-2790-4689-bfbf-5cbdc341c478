import { NextResponse } from "next/server";
import { WhatsAppService } from "@/services/whatsapp";

export async function POST(request: Request) {
  try {
    // Get salesforceId and prompt from request body
    const { salesforceId, prompt } = await request.json();

    // Validate required parameters
    if (!salesforceId) {
      return NextResponse.json(
        { error: "Salesforce ID is required" },
        { status: 400 }
      );
    }

    // Generate WhatsApp message using WhatsAppService with custom prompt
    const message = await WhatsAppService.generatePersonalizedMessageBySalesforceId(
      salesforceId,
      prompt
    );

    return NextResponse.json(message);
  } catch (error) {
    console.error("Error generating WhatsApp message:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An error occurred" },
      { status: 500 }
    );
  }
}

import { NextResponse } from "next/server";
import { WhatsAppService } from "@/services/whatsapp";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { messageId, whatsappConfigId } = body;

    if (!messageId) {
      return NextResponse.json(
        { error: "Message ID is required" },
        { status: 400 }
      );
    }

    const [result, tokenUsage] = await WhatsAppService.sendMessage(
      messageId,
      whatsappConfigId
    );
    
    return NextResponse.json({ result, tokenUsage });
  } catch (error) {
    console.error("Error sending WhatsApp message:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An error occurred" },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    // Get the salesforce ID from query parameters
    const { searchParams } = new URL(request.url);
    const salesforceId = searchParams.get("salesforceId");

    if (!salesforceId) {
      return NextResponse.json(
        { error: "Salesforce ID is required" },
        { status: 400 }
      );
    }

    const messageData = await WhatsAppService.generatePersonalizedMessageBySalesforceId(
      salesforceId
    );
    
    return NextResponse.json({ result: messageData, tokenUsage: null });
  } catch (error) {
    console.error("Error generating WhatsApp message:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An error occurred" },
      { status: 500 }
    );
  }
}

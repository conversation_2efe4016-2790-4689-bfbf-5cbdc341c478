import { NextResponse } from "next/server";
import { db } from "@/db";
import { eq } from "drizzle-orm";
import { getServerSession } from "next-auth";
import { salesforce } from "@/services/salesforce";
import { leads } from "@/db/schema";
import { authOptions } from "@/lib/auth";

async function getUserId(session: any) {
    // If session has Salesforce tokens, use Salesforce authentication
    if (session.accessToken && session.instanceUrl) {
        return await salesforce.getCurrentUserId();
    }
    // Otherwise use email authentication user id
    return session.user.id;
}

export async function GET(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json(
                { error: "Not authenticated" },
                { status: 401 }
            );
        }

        const currentUserId = await getUserId(session);
        const lead = await db
            .select()
            .from(leads)
            .where(eq(leads.id, parseInt(params.id)))
            .limit(1);

        if (!lead || lead.length === 0) {
            return NextResponse.json({ error: "Lead not found" }, { status: 404 });
        }

        if (lead[0].createdBy !== currentUserId) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
        }

        return NextResponse.json(lead[0]);
    } catch (error) {
        console.error("Error fetching lead:", error);
        return NextResponse.json(
            { error: "Failed to fetch lead" },
            { status: 500 }
        );
    }
}

export async function PUT(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json(
                { error: "Not authenticated" },
                { status: 401 }
            );
        }

        const currentUserId = await getUserId(session);
        const body = await request.json();

        // Generate name from firstName and lastName
        const name = body.name || [body.firstName, body.lastName]
            .filter(Boolean)
            .join(' ') || 'Unknown';

        const existingLead = await db
            .select()
            .from(leads)
            .where(eq(leads.id, parseInt(params.id)))
            .limit(1);

        if (!existingLead || existingLead.length === 0) {
            return NextResponse.json({ error: "Lead not found" }, { status: 404 });
        }

        if (existingLead[0].createdBy !== currentUserId) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
        }

        // Prepare update data
        const updateData = {
            firstName: body.firstName || undefined,
            lastName: body.lastName || undefined,
            name: name,
            email: body.email || undefined,
            company: body.company || 'Unknown Company',
            title: body.title || undefined,
            phone: body.phone || undefined,
            status: body.status || undefined,
            industry: body.industry || undefined,
            rating: body.rating || undefined,
            leadSource: body.leadSource || undefined,
            description: body.description || undefined,
            website: body.website || undefined,
            numberOfEmployees: body.numberOfEmployees || undefined,
            updatedAt: new Date(),
        };

        const [updatedLead] = await db
            .update(leads)
            .set(updateData)
            .where(eq(leads.id, parseInt(params.id)))
            .returning();

        return NextResponse.json(updatedLead);
    } catch (error) {
        console.error("Error updating lead:", error);
        return NextResponse.json(
            { error: "Failed to update lead" },
            { status: 500 }
        );
    }
}

export async function DELETE(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json(
                { error: "Not authenticated" },
                { status: 401 }
            );
        }

        const currentUserId = await getUserId(session);
        const existingLead = await db
            .select()
            .from(leads)
            .where(eq(leads.id, parseInt(params.id)))
            .limit(1);

        if (!existingLead || existingLead.length === 0) {
            return NextResponse.json({ error: "Lead not found" }, { status: 404 });
        }

        if (existingLead[0].createdBy !== currentUserId) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
        }

        await db
            .delete(leads)
            .where(eq(leads.id, parseInt(params.id)));

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error("Error deleting lead:", error);
        return NextResponse.json(
            { error: "Failed to delete lead" },
            { status: 500 }
        );
    }
}

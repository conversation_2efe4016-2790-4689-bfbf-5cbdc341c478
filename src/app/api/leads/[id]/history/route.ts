import { salesforce } from "@/services/salesforce";
import { NextResponse } from "next/server";

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const history = await salesforce.getLeadComprehensiveHistory(params.id);
    return NextResponse.json(history);
  } catch (error) {
    console.error("Error fetching lead history:", error);
    return NextResponse.json(
      { error: "Failed to fetch lead history" },
      { status: 500 }
    );
  }
}

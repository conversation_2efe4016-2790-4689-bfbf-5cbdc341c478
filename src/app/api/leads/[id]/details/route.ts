import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { db } from "@/db";
import { leads } from "@/db/schema/leads";
import { eq } from "drizzle-orm";
import { authOptions } from "@/lib/auth";

export async function GET(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.accessToken) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Получаем данные лида из нашей БД
        const [lead] = await db
            .select()
            .from(leads)
            .where(eq(leads.id, parseInt(params.id)))
            .limit(1);

        if (!lead) {
            return NextResponse.json({ error: "Lead not found" }, { status: 404 });
        }

        if (!lead.salesforceId) {
            return NextResponse.json({
                lead,
                history: [],
                activities: [],
                opportunities: []
            });
        }

        // Получаем данные из Salesforce
        const sfResponse = await fetch(
            `${session.instanceUrl}/services/data/v59.0/sobjects/Lead/${lead.salesforceId}`,
            {
                headers: {
                    Authorization: `Bearer ${session.accessToken}`,
                },
            }
        );

        if (!sfResponse.ok) {
            console.error("Salesforce API error:", await sfResponse.text());
            // Если ошибка в Salesforce, возвращаем хотя бы локальные данные
            return NextResponse.json({
                lead,
                history: [],
                activities: [],
                opportunities: []
            });
        }

        const sfLead = await sfResponse.json();

        // Получаем историю из Salesforce
        const historyResponse = await fetch(
            `${session.instanceUrl}/services/data/v59.0/query/?q=SELECT+Field,OldValue,NewValue,CreatedDate+FROM+LeadHistory+WHERE+LeadId='${lead.salesforceId}'`,
            {
                headers: {
                    Authorization: `Bearer ${session.accessToken}`,
                },
            }
        );

        let history = [];
        if (historyResponse.ok) {
            const historyData = await historyResponse.json();
            history = historyData.records?.map((record: any) => ({
                type: record.Field,
                date: record.CreatedDate,
                description: `Changed from ${record.OldValue || 'empty'} to ${record.NewValue || 'empty'}`,
            })) || [];
        } else {
            const error = await historyResponse.text();
            console.error("Failed to fetch history:", error);
        }

        // Получаем активности из Salesforce
        const activitiesResponse = await fetch(
            `${session.instanceUrl}/services/data/v59.0/query/?q=SELECT+Id,Subject,ActivityDate,Description,Type+FROM+Task+WHERE+WhoId='${lead.salesforceId}'+ORDER+BY+ActivityDate+DESC+LIMIT+1000`,
            {
                headers: {
                    Authorization: `Bearer ${session.accessToken}`,
                },
            }
        );

        let activities = [];
        if (activitiesResponse.ok) {
            const activitiesData = await activitiesResponse.json();
            activities = activitiesData.records?.map((record: any) => ({
                id: record.Id,
                type: record.Type || 'Task',
                subject: record.Subject,
                date: record.ActivityDate,
                description: record.Description,
            })) || [];
        } else {
            const error = await activitiesResponse.text();
            console.error("Failed to fetch activities:", error);
        }

        // Получаем связанные возможности
        const opportunitiesResponse = await fetch(
            `${session.instanceUrl}/services/data/v59.0/query/?q=SELECT+Id,Name,StageName,Amount,CloseDate+FROM+Opportunity+WHERE+LeadId='${lead.salesforceId}'+ORDER+BY+CloseDate+DESC+LIMIT+1000`,
            {
                headers: {
                    Authorization: `Bearer ${session.accessToken}`,
                },
            }
        );

        let opportunities = [];
        if (opportunitiesResponse.ok) {
            const opportunitiesData = await opportunitiesResponse.json();
            opportunities = opportunitiesData.records?.map((record: any) => ({
                id: record.Id,
                name: record.Name,
                stage: record.StageName,
                amount: record.Amount,
                closeDate: record.CloseDate,
            })) || [];
        } else {
            const error = await opportunitiesResponse.text();
            console.error("Failed to fetch opportunities:", error);
        }

        // Объединяем данные из локальной БД и Salesforce
        const enrichedLead = {
            ...lead,
            ...sfLead,
        };

        return NextResponse.json({
            lead: enrichedLead,
            history,
            activities,
            opportunities,
        });
    } catch (error) {
        console.error("Error fetching lead details:", error);
        return NextResponse.json(
            { error: "Failed to fetch lead details" },
            { status: 500 }
        );
    }
}

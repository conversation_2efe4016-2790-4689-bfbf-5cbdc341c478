import { NextResponse, NextRequest } from "next/server";
import { salesforce } from "@/services/salesforce";
import { db } from "@/db";
import { eq, desc, asc, and, or, ilike } from "drizzle-orm";
import { getServerSession } from "next-auth";
import { sql } from "drizzle-orm";
import { leads, users } from "@/db/schema";
import { authOptions } from "@/lib/auth";

// Определяем допустимые поля для сортировки
const validSortFields = ['firstName', 'lastName', 'email', 'phone', 'company', 'status', 'createdAt'] as const;
type SortField = typeof validSortFields[number];

const isSortField = (field: string): field is SortField => {
    return validSortFields.includes(field as SortField);
};

export async function GET(request: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json(
                {
                    error: "Not authenticated",
                    redirectTo: "/login",
                },
                { status: 401 }
            );
        }

        let currentUserId: string
        if (session.user?.email) {
            const userWithEmailId = await db.select({ id: users.id }).from(users)
                .where(eq(users.email, session.user?.email))
                .limit(1)
            currentUserId = userWithEmailId[0].id as any
        } else {
            currentUserId = await salesforce.getCurrentUserId();
        }

        // Get query parameters
        const url = new URL(request.url);
        const page = url.searchParams.get("page");
        const limit = url.searchParams.get("limit");
        const sortBy = url.searchParams.get("sortBy") || "createdAt";
        const sortOrder = (url.searchParams.get("sortOrder") || "desc") as "asc" | "desc";
        const search = url.searchParams.get("search");
        const ids = url.searchParams.get("ids");

        // Validate sort field
        if (!isSortField(sortBy)) {
            return NextResponse.json(
                { error: "Invalid sort field" },
                { status: 400 }
            );
        }

        // Base conditions
        let conditions = [eq(leads.createdBy, currentUserId)];

        // Add search condition if search parameter is present
        if (search) {
            conditions.push(
                or(
                    ilike(leads.firstName, `%${search}%`),
                    ilike(leads.lastName, `%${search}%`),
                    ilike(leads.email, `%${search}%`),
                    ilike(leads.company, `%${search}%`),
                )!
            );
        }

        // Add IDs filter if ids parameter is present
        if (ids) {
            const idArray = ids.split(",").map(id => parseInt(id)).filter(id => !isNaN(id));
            if (idArray.length > 0) {
                conditions.push(sql`${leads.id} IN (${sql.join(idArray, sql`, `)})`);
            }
        }

        // If we're fetching by IDs, skip pagination
        if (ids) {
            const filteredLeads = await db
                .select()
                .from(leads)
                .where(and(...conditions))
                .orderBy(sortOrder === "desc" ? desc(leads[sortBy]) : asc(leads[sortBy]));

            return NextResponse.json({
                leads: filteredLeads,
                pagination: {
                    total: filteredLeads.length,
                    page: 1,
                    limit: filteredLeads.length,
                    totalPages: 1
                }
            });
        }

        // Get total count for pagination (only for non-ID requests)
        const [{ count }] = await db
            .select({
                count: sql<number>`cast(count(*) as integer)`,
            })
            .from(leads)
            .where(and(...conditions));

        // Calculate pagination parameters
        const pageNum = parseInt(page || "1");
        const limitNum = parseInt(limit || "50");
        const offset = (pageNum - 1) * limitNum;

        // Get paginated and filtered leads
        const filteredLeads = await db
            .select()
            .from(leads)
            .where(and(...conditions))
            .orderBy(sortOrder === "desc" ? desc(leads[sortBy]) : asc(leads[sortBy]))
            .limit(limitNum)
            .offset(offset);

        // Return leads with pagination metadata
        return NextResponse.json({
            leads: filteredLeads,
            pagination: {
                total: count,
                page: pageNum,
                limit: limitNum,
                totalPages: Math.ceil(count / limitNum)
            }
        });
    } catch (error: any) {
        console.error("Error fetching leads:", error);
        return NextResponse.json(
            { error: "Failed to fetch leads" },
            { status: 500 }
        );
    }
}

export async function POST(request: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json(
                { error: "Not authenticated" },
                { status: 401 }
            );
        }

        const body = await request.json();

        let currentUserId: string;

        if (session.user?.email) {
            const emailUserId = await db
                .select({ id: users.id })
                .from(users)
                .where(eq(users.email, session.user.email))
                .limit(1)
            currentUserId = emailUserId[0].id as any
        } else {
            currentUserId = await salesforce.getCurrentUserId();
        }

        // Check if this is a sync request or a create request
        if (body.action === 'sync') {
            // Get leads from Salesforce
            const sfLeads = await salesforce.getLeads();

            // Process each lead
            for (const sfLead of sfLeads) {
                // Check if lead already exists
                const existingLead = await db
                    .select()
                    .from(leads)
                    .where(eq(leads.salesforceId, sfLead.Id))
                    .limit(1);

                if (existingLead.length > 0) {
                    // Update existing lead
                    await db
                        .update(leads)
                        .set({
                            firstName: sfLead.FirstName || undefined,
                            lastName: sfLead.LastName || undefined,
                            email: sfLead.Email || undefined,
                            company: sfLead.Company || 'Unknown Company',
                            title: sfLead.Title || undefined,
                            phone: sfLead.Phone || undefined,
                            status: sfLead.Status || undefined,
                            rating: sfLead.Rating || undefined,
                            industry: sfLead.Industry || undefined,
                            leadSource: sfLead.LeadSource || undefined,
                            description: sfLead.Description || undefined,
                            website: sfLead.Website || undefined,
                            numberOfEmployees: sfLead.NumberOfEmployees || undefined,
                            lastModifiedDate: sfLead.LastModifiedDate
                                ? new Date(sfLead.LastModifiedDate)
                                : undefined,
                            createdBy: currentUserId,
                            updatedAt: new Date(),
                        })
                        .where(eq(leads.id, existingLead[0].id));
                } else {
                    // Create new lead
                    await db.insert(leads).values({
                        salesforceId: sfLead.Id,
                        firstName: sfLead.FirstName || undefined,
                        lastName: sfLead.LastName || undefined,
                        email: sfLead.Email || undefined,
                        company: sfLead.Company || 'Unknown Company',
                        title: sfLead.Title || undefined,
                        phone: sfLead.Phone || undefined,
                        status: sfLead.Status || undefined,
                        rating: sfLead.Rating || undefined,
                        industry: sfLead.Industry || undefined,
                        leadSource: sfLead.LeadSource || undefined,
                        description: sfLead.Description || undefined,
                        website: sfLead.Website || undefined,
                        numberOfEmployees: sfLead.NumberOfEmployees || undefined,
                        lastModifiedDate: sfLead.LastModifiedDate
                            ? new Date(sfLead.LastModifiedDate)
                            : undefined,
                        createdBy: currentUserId,
                        updatedAt: new Date(),
                    });
                }
            }

            return NextResponse.json({ success: true });
        } else {
            // Validate required fields
            if (!body.firstName && !body.lastName) {
                return NextResponse.json(
                    { error: "firstName or lastName is required" },
                    { status: 400 }
                );
            }

            if (!body.company) {
                return NextResponse.json(
                    { error: "Company is required" },
                    { status: 400 }
                );
            }

            // Prepare lead data
            const leadData = {
                firstName: body.firstName || null,
                lastName: body.lastName || null,
                email: body.email || null,
                company: body.company || 'Unknown Company',
                title: body.title || null,
                phone: body.phone || null,
                status: body.status || null,
                industry: body.industry || null,
                rating: body.rating || null,
                leadSource: body.leadSource || null,
                description: body.description || null,
                website: body.website || null,
                numberOfEmployees: body.numberOfEmployees || null,
                createdBy: currentUserId,
                updatedAt: new Date(),
            };

            // Create a new lead
            const [newLead] = await db
                .insert(leads)
                .values(leadData)
                .returning();

            // Return the lead with its ID
            return NextResponse.json(newLead);
        }
    } catch (error: any) {
        console.error("Error in POST /api/leads:", error);

        if (error.redirectTo) {
            return NextResponse.json(
                { error: "Authentication required", redirectTo: error.redirectTo },
                { status: 401 }
            );
        }

        return NextResponse.json(
            { error: error.message || "Operation failed" },
            { status: 500 }
        );
    }
}

export async function PUT(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const data = await request.json();
        const { id, ...updateData } = data;

        const [updatedLead] = await db
            .update(leads)
            .set(updateData)
            .where(and(
                eq(leads.id, id),
                eq(leads.createdBy, session.user.id)
            ))
            .returning();

        return NextResponse.json(updatedLead);
    } catch (error) {
        console.error('Error updating lead:', error);
        return NextResponse.json(
            { error: 'Failed to update lead' },
            { status: 500 }
        );
    }
}

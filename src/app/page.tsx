"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Mail,
  Send,
  Trash2,
  FileEdit,
  RefreshCw,
  Loader2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardHeader, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSession } from "next-auth/react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { AgentSelect } from "@/components/agents/AgentSelect";
import { useToast } from "@/components/ui/use-toast";

interface Lead {
  id: number;
  salesforceId: string;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  company: string | null;
  title: string | null;
  phone: string | null;
  status: string | null;
  industry: string | null;
  rating: string | null;
  leadSource: string | null;
  description: string | null;
  website: string | null;
  numberOfEmployees: number | null;
  lastModifiedDate: string | null;
  communications: any | null;
  details?: {
    commented?: string;
    contact_type?: string;
    role?: string;
    products?: string;
    linkedin_info?: {
      raw_content?: string;
      content?: string;
    };
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

interface Email {
  id: number;
  subject: string;
  content: string;
  sent: boolean;
  createdAt: string;
}

interface Agent {
  id: number;
  name: string;
  description: string;
  type: string;
  settings: Record<string, any>;
}

interface EmailConfig {
  id: string;
  smtp: string;
  port: number;
  email: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export default function Home() {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);
  const [emails, setEmails] = useState<Email[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [emailConfigs, setEmailConfigs] = useState<EmailConfig[]>([]);
  const [selectedEmailConfig, setSelectedEmailConfig] = useState<string>("");
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const [emailToSend, setEmailToSend] = useState<number | null>(null);
  const [editingEmail, setEditingEmail] = useState<number | null>(null);
  const [editedSubject, setEditedSubject] = useState("");
  const [editedContent, setEditedContent] = useState("");
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [extraPrompt, setExtraPrompt] = useState<string>("");
  const session = useSession()
  const itemsPerPage = 10;
  const { toast } = useToast();
  const [isGenerating, setIsGenerating] = useState(false);

  const fetchEmailConfigs = useCallback(async () => {
    try {
      if (session.data?.user?.email) {
        const response = await fetch("/api/v1/settings/email");
        const data = await response.json();
        setEmailConfigs(Array.isArray(data) ? data : []);
      } else {
        setEmailConfigs([])
      }
    } catch (error) {
      console.error("Error fetching email configs:", error);
      setEmailConfigs([]);
    }
  }, [session.data?.user?.email]);

  useEffect(() => {
    fetchLeads();
    fetchEmailConfigs();
  }, [fetchEmailConfigs]);

  useEffect(() => {
    if (selectedLead) {
      fetchEmails(selectedLead.id.toString());
    }
  }, [selectedLead]);



  const fetchLeads = async () => {
    try {
      const response = await fetch("/api/leads");
      const data = await response.json();
      setLeads(Array.isArray(data.leads) ? data.leads : []);
    } catch (error) {
      console.error("Error fetching leads:", error);
      setLeads([]);
    }
  };

  const syncLeads = async () => {
    setLoading(true);
    try {
      await fetch("/api/leads", {
        method: "POST",
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'sync' }),
      });
      await fetchLeads();
    } catch (error) {
      console.error("Error syncing leads:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchEmails = async (leadId: string) => {
    try {
      const response = await fetch(`/api/emails?leadId=${leadId}`);
      const data = await response.json();
      // Сортируем письма по дате создания в обратном порядке (новые сверху)
      const sortedEmails = Array.isArray(data) ?
        data.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()) :
        [];
      setEmails(sortedEmails);
    } catch (error) {
      console.error("Error fetching emails:", error);
      setEmails([]);
    }
  };

  const handleSendClick = (emailId: number) => {
    setEmailToSend(emailId);
    setShowEmailDialog(true);
  };

  const sendEmail = async (emailId?: number) => {
    setLoading(true);
    try {
      if (session.data?.user?.email) {
        const response = await fetch("/api/test/email/send", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            emailId: emailToSend,
            emailConfigId: selectedEmailConfig,
          }),
        });

        if (!response.ok) throw new Error("Failed to send email");

        setEmails((prev) =>
          prev.map((email) =>
            email.id === emailToSend ? { ...email, sent: true } : email
          )
        );

        setShowEmailDialog(false);
        setSelectedEmailConfig("");
        setEmailToSend(null);
      } else {
        const response = await fetch("/api/test/email/send", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            emailId,
          }),
        });

        if (!response.ok) throw new Error("Failed to send email");

        setEmails((prev) =>
          prev.map((email) =>
            email.id === emailId ? { ...email, sent: true } : email
          )
        );
      }
    } catch (error) {
      console.error("Error sending email:", error);
    } finally {
      setLoading(false);
    }
  };

//   const updateLead = async (leadId: number, data: Partial<Lead>) => {
//     setLoading(true);
//     try {
//       const response = await fetch(`/api/leads/${leadId}`, {
//         method: 'PUT',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify(data),
//       });

//       if (!response.ok) {
//         throw new Error('Failed to update lead');
//       }

//       const updatedLead = await response.json();
//       setLeads(leads.map(lead => lead.id === leadId ? updatedLead : lead));
//       setSelectedLead(updatedLead);
//     } catch (error) {
//       console.error('Error updating lead:', error);
//     } finally {
//       setLoading(false);
//     }
//   };

  const handleEditClick = (email: Email) => {
    setEditingEmail(email.id);
    setEditedSubject(email.subject);
    setEditedContent(email.content);
  };

  const handleSaveEdit = async (emailId: number) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/emails/${emailId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subject: editedSubject,
          content: editedContent,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update email');
      }

      const updatedEmail = await response.json();
      setEmails(emails.map(email =>
        email.id === emailId ? updatedEmail : email
      ).sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));
      setEditingEmail(null);
    } catch (error) {
      console.error('Error updating email:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingEmail(null);
    setEditedSubject("");
    setEditedContent("");
  };

  const handleDeleteEmail = async (emailId: number) => {
    if (!confirm("Вы уверены, что хотите удалить это письмо?")) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/emails/${emailId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete email');
      }

      setEmails(emails.filter(email => email.id !== emailId));
      toast({
        title: "Успех",
        description: "Письмо успешно удалено",
      });
    } catch (error) {
      console.error('Error deleting email:', error);
      toast({
        title: "Ошибка",
        description: "Не удалось удалить письмо",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const totalPages = Math.ceil(leads.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentLeads = leads.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setSelectedLead(null);
  };

  const handleGenerateEmail = async () => {
    if (!selectedLead || !selectedAgent) {
      toast({
        title: "Ошибка",
        description: "Пожалуйста, выберите лид и агента для генерации",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsGenerating(true);
      const response = await fetch("/api/v1/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          leadId: selectedLead.id,
          agentId: selectedAgent.id,
          extraPrompt,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Ошибка при генерации email");
      }

      const generatedEmail = await response.json();
      setEmails((prevEmails) => [...prevEmails, generatedEmail]);

      toast({
        title: "Успех",
        description: "Email успешно сгенерирован",
      });
    } catch (error) {
      console.error("Error generating email:", error);
      toast({
        title: "Ошибка",
        description: error instanceof Error ? error.message : "Произошла ошибка при генерации email",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Salesforce Lead Email Generator</h1>
        <Button onClick={syncLeads} disabled={loading} className="gap-2">
          <RefreshCw
            className={`h-5 w-5 ${loading ? "animate-spin" : ""}`}
          />
          Sync Leads
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Leads List */}
        <Card>
          <CardHeader>
            <CardTitle>Leads</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {currentLeads.map((lead) => (
              <div
                key={lead.id}
                onClick={() => setSelectedLead(lead)}
                className={`p-3 rounded-lg cursor-pointer transition-colors ${
                  selectedLead?.id === lead.id
                    ? "bg-accent"
                    : "hover:bg-accent/50"
                }`}
              >
                <h3 className="font-medium">
                  {lead.firstName} {lead.lastName}
                </h3>
                <p className="text-sm text-muted-foreground">{lead.company}</p>
                <p className="text-sm text-muted-foreground">{lead.email}</p>
              </div>
            ))}
            {/* Pagination Controls */}
            {leads.length > itemsPerPage && (
              <div className="flex items-center justify-between mt-4 pt-4 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="text-sm text-muted-foreground">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            )}
            {leads.length === 0 && (
              <p className="text-muted-foreground">
                No leads found (please sync)
              </p>
            )}
          </CardContent>
        </Card>

        {/* Lead Details & Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Lead Details</CardTitle>
          </CardHeader>
          <CardContent>
            {selectedLead ? (
              <div>
                <div className="space-y-4">
                  {/* Existing fields */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">First Name</Label>
                      <Input
                        id="firstName"
                        value={selectedLead.firstName || ''}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          setSelectedLead({...selectedLead, firstName: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input
                        id="lastName"
                        value={selectedLead.lastName || ''}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          setSelectedLead({...selectedLead, lastName: e.target.value})}
                      />
                    </div>
                  </div>

                  {/* Basic Info */}
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      value={selectedLead.email || ''}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        setSelectedLead({...selectedLead, email: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="company">Company</Label>
                    <Input
                      id="company"
                      value={selectedLead.company || ''}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        setSelectedLead({...selectedLead, company: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      value={selectedLead.title || ''}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        setSelectedLead({...selectedLead, title: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="status">Status</Label>
                    <Input
                      id="status"
                      value={selectedLead.status || ''}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        setSelectedLead({...selectedLead, status: e.target.value})}
                    />
                  </div>

                  {/* Additional Basic Information */}
                  <div className="border p-4 rounded-md mt-4 mb-4">
                    <h3 className="font-medium mb-3">Additional Information</h3>
                    <div className="grid grid-cols-2 gap-4">
                      {/* Description */}
                      <div className="col-span-2">
                        <Label htmlFor="description">Description</Label>
                        <Textarea
                          id="description"
                          value={selectedLead.description || ''}
                          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                            setSelectedLead({...selectedLead, description: e.target.value})}
                          className="min-h-[100px]"
                        />
                      </div>

                      {/* Phone */}
                      <div>
                        <Label htmlFor="phone">Phone</Label>
                        <Input
                          id="phone"
                          value={selectedLead.phone || ''}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            setSelectedLead({...selectedLead, phone: e.target.value})}
                        />
                      </div>

                      {/* Industry */}
                      <div>
                        <Label htmlFor="industry">Industry</Label>
                        <Input
                          id="industry"
                          value={selectedLead.industry || ''}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            setSelectedLead({...selectedLead, industry: e.target.value})}
                        />
                      </div>

                      {/* Rating */}
                      <div>
                        <Label htmlFor="rating">Rating</Label>
                        <Input
                          id="rating"
                          value={selectedLead.rating || ''}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            setSelectedLead({...selectedLead, rating: e.target.value})}
                        />
                      </div>

                      {/* Lead Source */}
                      <div>
                        <Label htmlFor="leadSource">Lead Source</Label>
                        <Input
                          id="leadSource"
                          value={selectedLead.leadSource || ''}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            setSelectedLead({...selectedLead, leadSource: e.target.value})}
                        />
                      </div>

                      {/* Website */}
                      <div>
                        <Label htmlFor="website">Website</Label>
                        <Input
                          id="website"
                          value={selectedLead.website || ''}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            setSelectedLead({...selectedLead, website: e.target.value})}
                        />
                      </div>

                      {/* Number of Employees */}
                      <div>
                        <Label htmlFor="numberOfEmployees">Number of Employees</Label>
                        <Input
                          id="numberOfEmployees"
                          value={selectedLead.numberOfEmployees?.toString() || ''}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            setSelectedLead({...selectedLead, numberOfEmployees: parseInt(e.target.value) || null})}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Details Section */}
                  <div className="border p-4 rounded-md">
                    <h3 className="font-medium mb-4">Additional Details</h3>
                    <div className="grid grid-cols-2 gap-4">
                      {selectedLead.details?.commented && (
                        <div>
                          <Label>Status Comment</Label>
                          <Input value={selectedLead.details.commented} readOnly />
                        </div>
                      )}
                      {selectedLead.details?.contact_type && (
                        <div>
                          <Label>Contact Type</Label>
                          <Input value={selectedLead.details.contact_type} readOnly />
                        </div>
                      )}
                      {selectedLead.details?.role && (
                        <div>
                          <Label>Role</Label>
                          <Input value={selectedLead.details.role} readOnly />
                        </div>
                      )}
                      {selectedLead.details?.products && (
                        <div>
                          <Label>Products</Label>
                          <Input value={selectedLead.details.products} readOnly />
                        </div>
                      )}
                    </div>

                    {/* LinkedIn Info */}
                    <div className="mt-4">
                      <Label>LinkedIn Information</Label>
                      <Textarea
                        value={selectedLead.details?.linkedin_info?.content || selectedLead.details?.linkedin_info?.raw_content || ''}
                        readOnly
                        className="mt-2 min-h-[200px]"
                      />
                    </div>
                  </div>

                  {/* Communications Section */}
                  {selectedLead.communications && Object.entries(selectedLead.communications).some(([_, value]) => value) && (
                    <div className="border p-4 rounded-md">
                      <h3 className="font-medium mb-4">Communications</h3>
                      <div className="grid grid-cols-2 gap-4">
                        {selectedLead.communications.linkedin && (
                          <div>
                            <Label>LinkedIn</Label>
                            <Input value={selectedLead.communications.linkedin} readOnly />
                          </div>
                        )}
                        {selectedLead.communications.facebook && (
                          <div>
                            <Label>Facebook</Label>
                            <Input value={selectedLead.communications.facebook} readOnly />
                          </div>
                        )}
                        {selectedLead.communications.telegram && (
                          <div>
                            <Label>Telegram</Label>
                            <Input value={selectedLead.communications.telegram} readOnly />
                          </div>
                        )}
                        {selectedLead.communications.other_contact && (
                          <div>
                            <Label>Other Contact</Label>
                            <Input value={selectedLead.communications.other_contact} readOnly />
                          </div>
                        )}
                        {selectedLead.communications.last_activity && (
                          <div>
                            <Label>Last Activity</Label>
                            <Input value={selectedLead.communications.last_activity} readOnly />
                          </div>
                        )}
                        {selectedLead.communications.contact_history && (
                          <div className="col-span-2">
                            <Label>Contact History</Label>
                            <Input value={selectedLead.communications.contact_history} readOnly />
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Agent Select and Extra Prompt */}
                  <div>
                    <AgentSelect
                      selectedId={selectedAgent?.id}
                      onSelect={(agent) => setSelectedAgent(agent)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="extraPrompt">Extra Prompt</Label>
                    <Textarea
                      id="extraPrompt"
                      value={extraPrompt || ''}
                      onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                        setExtraPrompt(e.target.value)}
                      className="min-h-[200px]"
                    />
                  </div>
                </div>
                <div className="mt-6 flex justify-between">
                  <Button
                    onClick={handleGenerateEmail}
                    disabled={isGenerating || !selectedAgent}
                    className="gap-2"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="h-5 w-5 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Mail className="h-5 w-5" />
                        Generate Email
                      </>
                    )}
                  </Button>
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">
                Select a lead to view details
              </p>
            )}
          </CardContent>
        </Card>

        {/* Generated Emails */}
        <Card>
          <CardHeader>
            <CardTitle>Generated Emails</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {emails.map((email) => (
              <Card key={email.id}>
                <CardContent className="pt-6">
                  {editingEmail === email.id ? (
                    // Edit mode
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="subject">Subject</Label>
                        <Input
                          id="subject"
                          value={editedSubject}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            setEditedSubject(e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="content">Content</Label>
                        <Textarea
                          id="content"
                          value={editedContent}
                          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                            setEditedContent(e.target.value)}
                          className="min-h-[200px]"
                        />
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCancelEdit()}
                          disabled={loading}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Cancel
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleSaveEdit(email.id)}
                          disabled={loading}
                        >
                          <FileEdit className="h-4 w-4 mr-1" />
                          Save
                        </Button>
                      </div>
                    </div>
                  ) : (
                    // View mode
                    <>
                      <h3 className="font-medium">{email.subject}</h3>
                      <p className="text-sm text-muted-foreground mt-2 whitespace-pre-wrap">
                        {email.content}
                      </p>
                      <div className="mt-2 flex justify-between items-center text-sm text-muted-foreground">
                        <span>{new Date(email.createdAt).toLocaleDateString()}</span>
                        <div className="flex items-start gap-2">
                          <Badge variant={email.sent ? "default" : "secondary"}>
                            {email.sent ? "Sent" : "Draft"}
                          </Badge>
                          </div>
                          </div>
                        <div className="mt-2 flex justify-between items-center text-sm text-muted-foreground">
                        <div className="flex items-center gap-2">
                          {!email.sent && (
                            <>
                            <div className="flex flex-wrap gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEditClick(email)}
                                disabled={loading}
                                className="flex items-center gap-1 sm:w-auto w-full"
                              >
                                <FileEdit className="h-4 w-4" />
                                <span className="sm:inline hidden">Edit</span>
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  session.data?.user?.email
                                    ? handleSendClick(email.id)
                                    : sendEmail(email.id)
                                }
                                disabled={loading}
                                className="flex items-center gap-1 sm:w-auto w-full"
                              >
                                <Send className="h-4 w-4" />
                                <span className="sm:inline hidden">Send</span>
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteEmail(email.id)}
                                disabled={loading}
                                className="flex items-center gap-1 sm:w-auto w-full"
                              >
                                <Trash2 className="h-4 w-4" />
                                <span className="sm:inline hidden">Delete</span>
                              </Button>
                            </div>
                            </>
                          )}
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            ))}
            {emails.length === 0 && selectedLead && (
              <p className="text-muted-foreground">No emails generated yet</p>
            )}
            {!selectedLead && (
              <p className="text-muted-foreground">Select a lead to view emails</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Email Selection Dialog */}
      <Dialog open={showEmailDialog} onOpenChange={setShowEmailDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Select Email to Send From</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Select value={selectedEmailConfig} onValueChange={setSelectedEmailConfig}>
              <SelectTrigger>
                <SelectValue placeholder="Select an email" />
              </SelectTrigger>
              <SelectContent>
                {session.data?.user?.email && (
                  <SelectItem value="logged_in">
                    {session.data.user.email} (Logged In)
                  </SelectItem>
                )}
                {emailConfigs.map((config) => (
                  <SelectItem key={config.id} value={config.id}>
                    {config.email}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="mt-4 flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowEmailDialog(false)}>
                Cancel
              </Button>
              <Button onClick={() => sendEmail()} disabled={!selectedEmailConfig || loading}>
                Send Email
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

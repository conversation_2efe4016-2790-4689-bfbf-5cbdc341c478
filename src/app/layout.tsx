import type { <PERSON>ada<PERSON> } from "next";
import { GeistSans } from "geist/font/sans";
import { GeistMono } from "geist/font/mono";
import "./globals.css";
import Navigation from "@/components/navigation";
import { Toaster } from "@/components/ui/toaster";
import { getServerSession } from "next-auth";
import { Providers } from "@/providers/providers";
import { cn } from "@/lib/utils";

export const metadata: Metadata = {
  title: "Email Generator",
  description: "AI-powered email generation for Salesforce leads",
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerSession();

  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${GeistSans.variable} ${GeistMono.variable} min-h-screen bg-background font-sans antialiased`}
      >
        <Providers session={session}>
          <Navigation />
          <main className={cn(
            "container",
            "mx-auto pt-16"
          )}>{children}</main>
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}

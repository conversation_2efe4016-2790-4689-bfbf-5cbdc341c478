"use client";

import { useEffect } from "react";
import { redirect } from "next/navigation";

export default function ApiTokensRedirectPage() {
  useEffect(() => {
    redirect("/settings?tab=api-tokens");
  }, []);
  
  // Этот текст будет видимым только кратковременно во время перенаправления
  return (
    <div className="flex items-center justify-center h-screen">
      <p>Перенаправление на страницу API-токенов...</p>
    </div>
  );
} 
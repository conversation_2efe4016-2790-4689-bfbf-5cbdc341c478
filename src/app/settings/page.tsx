"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/useToast";
import { Loader2, Plus, Pencil, Trash2, KeyIcon } from "lucide-react";
import { useSession } from "next-auth/react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

type Tab = "profile" | "email" | "api-tokens";

interface EmailConfig {
  id: string;
  smtp: string;
  port: number;
  email: string;
  createdAt: string;
}

interface ApiToken {
  id: number;
  name: string;
  description: string | null;
  token: string;
  isActive: boolean;
  expiresAt: string | null;
  lastUsedAt: string | null;
  createdAt: string;
}

const fetchProfile = async () => {
  const response = await fetch("/api/v1/settings/profile");
  if (!response.ok) throw new Error("Failed to fetch profile");
  return response.json();
};

const fetchEmailConfigs = async () => {
  const response = await fetch("/api/v1/settings/email");
  if (!response.ok) throw new Error("Failed to fetch email configs");
  const data = await response.json();
  return Array.isArray(data) ? data : data ? [data] : [];
};

export default function SettingsPage() {
  const { data: session } = useSession();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  const [activeTab, setActiveTab] = useState<Tab>("profile");
  const [isEditing, setIsEditing] = useState(false);
  const [selectedConfig, setSelectedConfig] = useState<EmailConfig | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Form States
  const [profileFormData, setProfileFormData] = useState({
    fullName: "",
    company: "",
    email: "",
    password: "",
  });

  const [emailFormData, setEmailFormData] = useState({
    smtp: "",
    port: "",
    email: "",
    password: "",
  });

  // New states for API tokens
  const [apiTokenFormData, setApiTokenFormData] = useState({
    name: "",
    description: "",
  });
  const [isCreatingToken, setIsCreatingToken] = useState(false);
  const [newToken, setNewToken] = useState<string | null>(null);

  // Перемещаем функцию fetchApiTokens внутрь компонента
  const fetchApiTokens = async () => {
    const response = await fetch(`/api/v1/api-tokens?userId=${session?.user?.id}`);
    if (!response.ok) throw new Error("Не удалось загрузить API-токены");
    const data = await response.json();
    return data.success ? data.tokens : [];
  };

  // Queries
  const { data: profile, isLoading: profileLoading } = useQuery({
    queryKey: ["profile"],
    queryFn: fetchProfile,
    placeholderData: session?.user ? {
      fullName: session.user.name || "",
      company: session.user.company || "",
      email: session.user.email || "",
    } : undefined,
  });

  const { data: emailConfigs = [], isLoading: emailConfigsLoading } = useQuery({
    queryKey: ["emailConfigs"],
    queryFn: fetchEmailConfigs,
    enabled: activeTab === "email",
  });

  const { data: apiTokens = [], isLoading: apiTokensLoading } = useQuery({
    queryKey: ["apiTokens"],
    queryFn: fetchApiTokens,
    enabled: activeTab === "api-tokens",
  });

  // Mutations
  const { mutate: updateProfile, isPending: isProfileUpdating } = useMutation({
    mutationFn: async (data: typeof profileFormData) => {
      const response = await fetch("/api/v1/settings/profile", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error("Failed to update profile");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["profile"] });
      setLastUpdated(new Date());
      toast({
        title: "Success",
        description: "Profile settings updated successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to save profile settings",
        variant: "destructive",
      });
    },
  });

  const { mutate: saveEmailConfig, isPending: isEmailSaving } = useMutation({
    mutationFn: async (data: typeof emailFormData) => {
      const response = await fetch("/api/v1/settings/email", {
        method: selectedConfig ? "PUT" : "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...data,
          port: parseInt(data.port),
          id: selectedConfig?.id,
        }),
      });
      if (!response.ok) throw new Error("Failed to save email configuration");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["emailConfigs"] });
      toast({
        title: "Success",
        description: "Email configuration saved successfully",
      });
      resetEmailForm();
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to save email configuration",
        variant: "destructive",
      });
    },
  });

  const { mutate: deleteEmailConfig } = useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/v1/settings/email/${id}`, {
        method: "DELETE",
      });
      if (!response.ok) throw new Error("Failed to delete email configuration");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["emailConfigs"] });
      toast({
        title: "Success",
        description: "Email configuration deleted successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to delete email configuration",
        variant: "destructive",
      });
    },
  });

  const { mutate: createApiToken, isPending: isTokenCreating } = useMutation({
    mutationFn: async (data: typeof apiTokenFormData) => {
      const response = await fetch("/api/v1/api-tokens", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...data,
          userId: Number(session?.user?.id),
        }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Не удалось создать API-токен");
      }
      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["apiTokens"] });
      setNewToken(data.token.token);
      setApiTokenFormData({ name: "", description: "" });
      setIsCreatingToken(false);
      toast({
        title: "Успех",
        description: "API-токен успешно создан",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Ошибка",
        description: error.message || "Не удалось создать API-токен",
        variant: "destructive",
      });
    },
  });

  const { mutate: deactivateApiToken } = useMutation({
    mutationFn: async (tokenId: number) => {
      const response = await fetch(`/api/v1/api-tokens/${tokenId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ isActive: false }),
      });
      if (!response.ok) throw new Error("Не удалось деактивировать токен");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["apiTokens"] });
      toast({
        title: "Успех",
        description: "API-токен деактивирован",
      });
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось деактивировать токен",
        variant: "destructive",
      });
    },
  });

  const { mutate: deleteApiToken } = useMutation({
    mutationFn: async (tokenId: number) => {
      const response = await fetch(`/api/v1/api-tokens/${tokenId}`, {
        method: "DELETE",
      });
      if (!response.ok) throw new Error("Не удалось удалить токен");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["apiTokens"] });
      toast({
        title: "Успех",
        description: "API-токен удален",
      });
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось удалить токен",
        variant: "destructive",
      });
    },
  });

  // Effects
  useEffect(() => {
    if (profile) {
      setProfileFormData({
        fullName: profile.fullName || "",
        company: profile.company || "",
        email: profile.email || "",
        password: "",
      });
    }
  }, [profile]);

  // Добавляем эффект для проверки параметра tab в URL
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      const tabParam = params.get('tab');
      
      if (tabParam === 'api-tokens') {
        setActiveTab('api-tokens');
      } else if (tabParam === 'email') {
        setActiveTab('email');
      } else if (tabParam === 'profile') {
        setActiveTab('profile');
      }
    }
  }, []);

  // Handlers
  const handleProfileSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateProfile(profileFormData);
  };

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    saveEmailConfig(emailFormData);
  };

  const handleEdit = (config: EmailConfig) => {
    setSelectedConfig(config);
    setEmailFormData({
      smtp: config.smtp,
      port: config.port.toString(),
      email: config.email,
      password: "",
    });
    setIsEditing(true);
  };

  const resetEmailForm = () => {
    setEmailFormData({
      smtp: "",
      port: "",
      email: "",
      password: "",
    });
    setIsEditing(false);
    setSelectedConfig(null);
  };

  const isOAuthUser = session?.user?.provider === "oauth";

  // New handler for API token submission
  const handleApiTokenSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createApiToken(apiTokenFormData);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Настройки</h1>
      
      <div className="mb-6 border-b">
        <div className="flex space-x-4">
          <button
            onClick={() => setActiveTab("profile")}
            className={`pb-2 font-medium ${
              activeTab === "profile"
                ? "border-b-2 border-blue-600 text-blue-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            Профиль
          </button>
          <button
            onClick={() => setActiveTab("email")}
            className={`pb-2 font-medium ${
              activeTab === "email"
                ? "border-b-2 border-blue-600 text-blue-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            Email
          </button>
          <button
            onClick={() => setActiveTab("api-tokens")}
            className={`pb-2 font-medium ${
              activeTab === "api-tokens"
                ? "border-b-2 border-blue-600 text-blue-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            API-токены
          </button>
        </div>
      </div>

      <div className="space-y-6">
        {activeTab === "profile" && (
          <Card>
            <CardHeader>
              <CardTitle>Profile Settings</CardTitle>
              {lastUpdated && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <div className="flex h-2 w-2 mr-2 relative">
                    <div className="animate-ping absolute inline-flex h-2 w-2 rounded-full bg-green-400 opacity-75"></div>
                    <div className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></div>
                  </div>
                  Last updated: {lastUpdated.toLocaleTimeString()}
                </div>
              )}
            </CardHeader>
            <CardContent>
              <form onSubmit={handleProfileSubmit} className="space-y-4">
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="fullName">Full Name</Label>
                    <Input
                      id="fullName"
                      placeholder="Enter Full Name"
                      value={profileFormData.fullName}
                      onChange={(e) =>
                        setProfileFormData((prev) => ({
                          ...prev,
                          fullName: e.target.value,
                        }))
                      }
                      disabled={profileLoading || isProfileUpdating}
                      className={profileLoading ? "bg-muted" : ""}
                      required
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="company">Company</Label>
                    <Input
                      id="company"
                      placeholder="Enter Company Name"
                      value={profileFormData.company}
                      onChange={(e) =>
                        setProfileFormData((prev) => ({
                          ...prev,
                          company: e.target.value,
                        }))
                      }
                      disabled={profileLoading || isProfileUpdating}
                      className={profileLoading ? "bg-muted" : ""}
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="profileEmail">Email</Label>
                    <Input
                      id="profileEmail"
                      type="email"
                      placeholder="Enter Email"
                      value={profileFormData.email}
                      onChange={(e) =>
                        setProfileFormData((prev) => ({
                          ...prev,
                          email: e.target.value,
                        }))
                      }
                      disabled={profileLoading || isProfileUpdating}
                      className={profileLoading ? "bg-muted" : ""}
                      required
                    />
                  </div>

                  {!isOAuthUser && (
                    <div className="grid gap-2">
                      <Label htmlFor="profilePassword">Change Password</Label>
                      <Input
                        id="profilePassword"
                        type="password"
                        placeholder="Enter New Password"
                        value={profileFormData.password}
                        onChange={(e) =>
                          setProfileFormData((prev) => ({
                            ...prev,
                            password: e.target.value,
                          }))
                        }
                        disabled={profileLoading || isProfileUpdating}
                        className={profileLoading ? "bg-muted" : ""}
                      />
                    </div>
                  )}
                </div>

                <Button 
                  type="submit" 
                  disabled={profileLoading || isProfileUpdating}
                  className="w-full sm:w-auto"
                >
                  {profileLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Loading...
                    </>
                  ) : isProfileUpdating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        )}

        {activeTab === "email" && (
          <>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Email Configurations</CardTitle>
                <Button onClick={() => setIsEditing(true)} className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add New
                </Button>
              </CardHeader>
              <CardContent>
                {emailConfigsLoading ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : emailConfigs.length === 0 ? (
                  <p className="text-muted-foreground text-center py-4">
                    No email configurations found. Add one to get started.
                  </p>
                ) : (
                  <div className="space-y-4">
                    {emailConfigs.map((config) => (
                      <div
                        key={config.id}
                        className="flex items-center justify-between p-4 border rounded-lg"
                      >
                        <div>
                          <p className="font-medium">{config.email}</p>
                          <p className="text-sm text-muted-foreground">
                            {config.smtp}:{config.port}
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(config)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => deleteEmailConfig(config.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {isEditing && (
              <Card>
                <CardHeader>
                  <CardTitle>
                    {selectedConfig ? "Edit Email Configuration" : "Add Email Configuration"}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleEmailSubmit} className="space-y-4">
                    <div className="grid gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="smtp">SMTP Server</Label>
                        <Input
                          id="smtp"
                          placeholder="smtp.example.com"
                          value={emailFormData.smtp}
                          onChange={(e) =>
                            setEmailFormData((prev) => ({
                              ...prev,
                              smtp: e.target.value,
                            }))
                          }
                          required
                        />
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="port">Port</Label>
                        <Input
                          id="port"
                          type="number"
                          placeholder="587"
                          value={emailFormData.port}
                          onChange={(e) =>
                            setEmailFormData((prev) => ({
                              ...prev,
                              port: e.target.value,
                            }))
                          }
                          required
                        />
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={emailFormData.email}
                          onChange={(e) =>
                            setEmailFormData((prev) => ({
                              ...prev,
                              email: e.target.value,
                            }))
                          }
                          required
                        />
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="password">Password</Label>
                        <Input
                          id="password"
                          type="password"
                          value={emailFormData.password}
                          onChange={(e) =>
                            setEmailFormData((prev) => ({
                              ...prev,
                              password: e.target.value,
                            }))
                          }
                          required={!selectedConfig}
                          placeholder={
                            selectedConfig ? "Enter new password to update" : "Enter password"
                          }
                        />
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button type="submit" className="flex-1" disabled={isEmailSaving}>
                        {isEmailSaving ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          "Save Configuration"
                        )}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={resetEmailForm}
                      >
                        Cancel
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}
          </>
        )}

        {activeTab === "api-tokens" && (
          <div>
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex justify-between items-center">
                  <span>API-токены</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsCreatingToken(!isCreatingToken)}
                  >
                    {isCreatingToken ? "Отмена" : "Новый токен"}
                    {!isCreatingToken && <Plus className="ml-2 h-4 w-4" />}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isCreatingToken && (
                  <form onSubmit={handleApiTokenSubmit} className="mb-6 space-y-4">
                    <div className="grid gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="token-name">Название токена</Label>
                        <Input
                          id="token-name"
                          value={apiTokenFormData.name}
                          onChange={(e) =>
                            setApiTokenFormData({
                              ...apiTokenFormData,
                              name: e.target.value,
                            })
                          }
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="token-description">Описание (опционально)</Label>
                        <Input
                          id="token-description"
                          value={apiTokenFormData.description}
                          onChange={(e) =>
                            setApiTokenFormData({
                              ...apiTokenFormData,
                              description: e.target.value,
                            })
                          }
                        />
                      </div>
                    </div>
                    <Button type="submit" disabled={isTokenCreating}>
                      {isTokenCreating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Создать токен
                    </Button>
                  </form>
                )}

                {newToken && (
                  <div className="mb-6 p-4 border rounded-md bg-green-50">
                    <h3 className="text-sm font-medium text-green-800 mb-2">
                      Новый токен создан!
                    </h3>
                    <p className="text-sm text-green-700 mb-2">
                      Сохраните этот токен сейчас. Он больше не будет показан.
                    </p>
                    <div className="flex items-center justify-between p-2 bg-white rounded border mb-2">
                      <code className="text-sm font-mono break-all">{newToken}</code>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => {
                          navigator.clipboard.writeText(newToken);
                          toast({
                            title: "Скопировано",
                            description: "Токен скопирован в буфер обмена",
                          });
                        }}
                      >
                        Копировать
                      </Button>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setNewToken(null)}
                    >
                      Закрыть
                    </Button>
                  </div>
                )}

                {apiTokensLoading ? (
                  <div className="flex justify-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                  </div>
                ) : apiTokens.length === 0 ? (
                  <p className="text-sm text-gray-500 py-4">
                    У вас нет API-токенов. Создайте новый токен для интеграции с внешними сервисами.
                  </p>
                ) : (
                  <div className="space-y-4">
                    {apiTokens.map((token: ApiToken) => (
                      <div
                        key={token.id}
                        className="p-4 border rounded-md flex flex-col sm:flex-row sm:items-center justify-between"
                      >
                        <div className="mb-2 sm:mb-0">
                          <h3 className="font-medium">{token.name}</h3>
                          {token.description && (
                            <p className="text-sm text-gray-500">{token.description}</p>
                          )}
                          <p className="text-xs text-gray-500 font-mono mt-1">
                            {token.token}
                          </p>
                          <div className="text-xs text-gray-500 mt-1 flex items-center">
                            <span
                              className={`inline-block w-2 h-2 rounded-full mr-2 ${
                                token.isActive ? "bg-green-500" : "bg-red-500"
                              }`}
                            ></span>
                            {token.isActive ? "Активен" : "Неактивен"}
                            <span className="mx-2">•</span>
                            Создан: {new Date(token.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          {token.isActive && (
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-amber-600 border-amber-600 hover:bg-amber-50"
                              onClick={() => {
                                if (
                                  window.confirm(
                                    "Вы уверены, что хотите деактивировать этот токен? Это действие нельзя отменить."
                                  )
                                ) {
                                  deactivateApiToken(token.id);
                                }
                              }}
                            >
                              Деактивировать
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-red-600 border-red-600 hover:bg-red-50"
                            onClick={() => {
                              if (
                                window.confirm(
                                  "Вы уверены, что хотите удалить этот токен? Это действие нельзя отменить."
                                )
                              ) {
                                deleteApiToken(token.id);
                              }
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}

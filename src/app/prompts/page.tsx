"use client";

import { useState, useMemo } from "react";
import { usePrompts, useCreatePrompt, useDeletePrompt, useTestPrompt, useUpdatePrompt } from "@/hooks/use-prompts";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { type Prompt, type PromptType } from "@/db/schema/prompts";
import { PromptTester } from "@/components/prompts/PromptTester";
import { useToast } from "@/components/ui/use-toast";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { HighlightVariables } from "@/components/prompts/PromptVariableHighlighter";
import { Switch } from "@/components/ui/switch";

const promptTypes: PromptType[] = ["user", "system", "assistant"];

export default function PromptsPage() {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [content, setContent] = useState("");
  const [type, setType] = useState<PromptType>("system");
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [search, setSearch] = useState("");
  const [promptToDelete, setPromptToDelete] = useState<number | null>(null);
  const [previewMode, setPreviewMode] = useState(false);
  const { toast } = useToast();

  const { data: prompts, isLoading } = usePrompts();
  const createPrompt = useCreatePrompt();
  const deletePrompt = useDeletePrompt();
  const testPrompt = useTestPrompt();
  const updatePrompt = useUpdatePrompt();

  const filteredPrompts = useMemo(() => {
    if (!prompts) return [];
    if (!search) return prompts;

    const searchLower = search.toLowerCase();
    return prompts.filter(
      (prompt) =>
        prompt.title.toLowerCase().includes(searchLower) ||
        (prompt.description?.toLowerCase() || "").includes(searchLower) ||
        prompt.content.toLowerCase().includes(searchLower)
    );
  }, [prompts, search]);

  const handleCreate = async () => {
    try {
      await createPrompt.mutateAsync({
        title,
        description,
        content,
        type,
      });
      setTitle("");
      setDescription("");
      setContent("");
      toast({
        title: "Success",
        description: "Prompt created successfully",
      });
    } catch (_error) {
      toast({
        title: "Error",
        description: "Failed to create prompt",
        variant: "destructive",
      });
    }
  };

  const handlePromptClick = (prompt: Partial<Prompt>) => {
    const promptWithCreatedBy = {
      ...prompt,
      createdBy: prompt.createdBy || ''
    } as Prompt;
    setIsEditing(true);
    setSelectedPrompt(promptWithCreatedBy);
    setTitle(prompt.title || '');
    setDescription(prompt.description || '');
    setContent(prompt.content || '');
    setType((prompt.type || 'system') as PromptType);
    setSearch('');
  };

  const handleUpdate = async () => {
    if (!selectedPrompt) return;

    try {
      const updatedPrompt = await updatePrompt.mutateAsync({
        id: selectedPrompt.id,
        title,
        description,
        content,
        type,
      });
      setSelectedPrompt(updatedPrompt);
      toast({
        title: "Success",
        description: "Prompt updated successfully",
      });
    } catch (_error) {
      toast({
        title: "Error",
        description: "Failed to update prompt",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (promptId: number) => {
    try {
      await deletePrompt.mutateAsync(promptId);
      if (selectedPrompt?.id === promptId) {
        setSelectedPrompt(null);
      }
      toast({
        title: "Success",
        description: "Prompt deleted successfully",
      });
    } catch (_error) {
      toast({
        title: "Error",
        description: "Failed to delete prompt",
        variant: "destructive",
      });
    }
  };

  const handleTest = async (content: string, variables: Record<string, string>, settings: { model: string; temperature: number; maxTokens: number }) => {
    try {
      const result = await testPrompt.mutateAsync({
        content,
        settings,
        variables
      });
      
      toast({
        title: "Success",
        description: "Prompt tested successfully",
      });

      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to test prompt",
        variant: "destructive",
      });
      throw error;
    }
  };

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setContent(e.target.value);
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto py-10">
      <div className="grid grid-cols-12 gap-6">
        {/* Left Column - Prompts List */}
        <div className="col-span-4">
          <Card className="h-full">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
              <CardTitle>Prompts</CardTitle>
              <Button
                onClick={() => {
                  setIsEditing(true);
                  setSelectedPrompt(null);
                  setTitle("");
                  setDescription("");
                  setContent("");
                  setType("system");
                }}
              >
                Create New
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="rounded-lg border shadow-md">
                  <Input
                    placeholder="Search prompts..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="border-0 focus-visible:ring-0"
                  />
                  <div className="max-h-[300px] overflow-y-auto">
                    {filteredPrompts.length === 0 ? (
                      <div className="p-4 text-sm text-muted-foreground">
                        No prompts found.
                      </div>
                    ) : (
                      filteredPrompts.map((prompt) => (
                        <div
                          key={prompt.id}
                          className="flex flex-1 flex-col p-2 hover:bg-accent cursor-pointer border-t"
                          onClick={() => handlePromptClick(prompt)}
                          role="button"
                          tabIndex={0}
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{prompt.title}</span>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                setPromptToDelete(prompt.id);
                              }}
                            >
                              Delete
                            </Button>
                          </div>
                          {prompt.description && (
                            <span className="text-sm text-muted-foreground">
                              {prompt.description}
                            </span>
                          )}
                          <span className="text-xs text-muted-foreground">
                            Type: {prompt.type}
                          </span>
                          {prompt.content && prompt.content.length > 0 && (
                            <div className="text-xs text-muted-foreground mt-1 p-1 border-t border-dashed">
                              <HighlightVariables>
                                {prompt.content.length > 100
                                  ? `${prompt.content.substring(0, 100)}...`
                                  : prompt.content}
                              </HighlightVariables>
                            </div>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Edit/Test Area */}
        <div className="col-span-8">
          {selectedPrompt ? (
            <Card>
              <CardHeader>
                <CardTitle>{selectedPrompt.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="edit" className="space-y-4">
                  <TabsList>
                    <TabsTrigger value="edit">Edit</TabsTrigger>
                    <TabsTrigger value="test">Test</TabsTrigger>
                  </TabsList>

                  <TabsContent value="edit" className="space-y-4">
                    <div className="space-y-4">
                      <div>
                        <Label>Title</Label>
                        <Input
                          value={title}
                          onChange={(e) => setTitle(e.target.value)}
                          placeholder="Enter prompt title"
                        />
                      </div>
                      <div>
                        <Label>Description</Label>
                        <Input
                          value={description}
                          onChange={(e) => setDescription(e.target.value)}
                          placeholder="Enter prompt description"
                        />
                      </div>
                      <div>
                        <Label>Type</Label>
                        <select
                          className="w-full p-2 border rounded"
                          value={type}
                          onChange={(e) => setType(e.target.value as PromptType)}
                        >
                          {promptTypes.map((type) => (
                            <option key={type} value={type}>
                              {type}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="placeholder-info border p-4 rounded-md bg-muted/30 my-4">
                        <h3 className="font-medium text-base mb-2">Использование переменных в шаблоне</h3>
                        <p className="text-sm text-muted-foreground mb-2">Вы можете использовать следующие переменные для динамической вставки информации о лиде:</p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                          <div>
                            <h4 className="font-medium text-sm mb-1">Основные переменные:</h4>
                            <ul className="list-disc pl-4 text-sm space-y-1">
                              <li><code className="bg-muted px-1 rounded">{'{{leadFirstName}}'}</code> - имя лида</li>
                              <li><code className="bg-muted px-1 rounded">{'{{leadLastName}}'}</code> - фамилия лида</li>
                              <li><code className="bg-muted px-1 rounded">{'{{leadEmail}}'}</code> - email лида</li>
                              <li><code className="bg-muted px-1 rounded">{'{{leadCompany}}'}</code> - компания лида</li>
                              <li><code className="bg-muted px-1 rounded">{'{{lead_details}}'}</code> - детали о лиде</li>
                              <li><code className="bg-muted px-1 rounded">{'{{lead_history}}'}</code> - история лида</li>
                              <li><code className="bg-muted px-1 rounded">{'{{company_info}}'}</code> - информация о компании</li>
                              <li><code className="bg-muted px-1 rounded">{'{{senderName}}'}</code> - имя отправителя</li>
                            </ul>
                          </div>
                          
                          <div>
                            <h4 className="font-medium text-sm mb-1">JSON-поля (динамические):</h4>
                            <ul className="list-disc pl-4 text-sm space-y-1">
                              <li><code className="bg-muted px-1 rounded">{'{{details_*}}'}</code> - поля из details 
                                <div className="text-xs text-muted-foreground ml-5 mt-1">
                                  Примеры: <code className="bg-muted px-1 rounded">details_city</code>, <code className="bg-muted px-1 rounded">details_role</code>
                                </div>
                              </li>
                              <li><code className="bg-muted px-1 rounded">{'{{communications_*}}'}</code> - поля из communications 
                                <div className="text-xs text-muted-foreground ml-5 mt-1">
                                  Примеры: <code className="bg-muted px-1 rounded">communications_linkedin</code>
                                </div>
                              </li>
                              <li><code className="bg-muted px-1 rounded">{'{{company_*}}'}</code> - поля из company_info 
                                <div className="text-xs text-muted-foreground ml-5 mt-1">
                                  Примеры: <code className="bg-muted px-1 rounded">company_name</code>, <code className="bg-muted px-1 rounded">company_website</code>
                                </div>
                              </li>
                            </ul>
                          </div>
                        </div>
                        
                        <h4 className="font-medium text-sm mt-4 mb-2">Примеры использования:</h4>
                        <div className="bg-muted p-3 rounded-md text-sm whitespace-pre-wrap">
                          <HighlightVariables>
                          {`Здравствуйте, {{leadFirstName}} {{leadLastName}}, 

Я заметил, что вы работаете в {{leadCompany}} и находитесь в городе {{details_city}}.

Мы нашли ваш профиль в {{communications_linkedin}} и хотели бы предложить вам наши услуги.

С уважением,
{{senderName}}`}
                          </HighlightVariables>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <Label>Content</Label>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="previewMode"
                              checked={previewMode}
                              onCheckedChange={setPreviewMode}
                            />
                            <Label htmlFor="previewMode" className="text-sm cursor-pointer">
                              {previewMode ? "Режим просмотра" : "Режим редактирования"}
                            </Label>
                          </div>
                        </div>
                        
                        {previewMode ? (
                          <div className="bg-white dark:bg-gray-900 border rounded-lg p-3 min-h-[200px] overflow-auto">
                            <HighlightVariables>{content}</HighlightVariables>
                          </div>
                        ) : (
                          <Textarea
                            value={content}
                            onChange={handleContentChange}
                            placeholder="Enter prompt content"
                            className="min-h-[200px]"
                          />
                        )}
                      </div>
                      <Button 
                        onClick={isEditing ? handleUpdate : handleCreate}
                        disabled={!title || !content || createPrompt.isPending || updatePrompt.isPending}
                      >
                        {isEditing ? (
                          updatePrompt.isPending ? "Updating..." : "Update Prompt"
                        ) : (
                          createPrompt.isPending ? "Creating..." : "Create Prompt"
                        )}
                      </Button>
                    </div>
                  </TabsContent>

                  <TabsContent value="test" className="space-y-4">
                    <PromptTester
                      content={selectedPrompt.content}
                      onTest={handleTest}
                    />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          ) : (
            isEditing && (
              <Card>
                <CardHeader>
                  <CardTitle>Create New Prompt</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Label>Title</Label>
                      <Input
                        value={title}
                        onChange={(e) => setTitle(e.target.value)}
                        placeholder="Enter prompt title"
                      />
                    </div>
                    <div>
                      <Label>Description</Label>
                      <Input
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        placeholder="Enter prompt description"
                      />
                    </div>
                    <div>
                      <Label>Type</Label>
                      <select
                        className="w-full p-2 border rounded"
                        value={type}
                        onChange={(e) => setType(e.target.value as PromptType)}
                      >
                        {promptTypes.map((type) => (
                          <option key={type} value={type}>
                            {type}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <Label>Content</Label>
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="previewModeNew"
                            checked={previewMode}
                            onCheckedChange={setPreviewMode}
                          />
                          <Label htmlFor="previewModeNew" className="text-sm cursor-pointer">
                            {previewMode ? "Режим просмотра" : "Режим редактирования"}
                          </Label>
                        </div>
                      </div>
                      
                      {previewMode ? (
                        <div className="bg-white dark:bg-gray-900 border rounded-lg p-3 min-h-[200px] overflow-auto">
                          <HighlightVariables>{content}</HighlightVariables>
                        </div>
                      ) : (
                        <Textarea
                          value={content}
                          onChange={handleContentChange}
                          placeholder="Enter prompt content"
                          className="min-h-[200px]"
                        />
                      )}
                    </div>
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setIsEditing(false);
                          setTitle("");
                          setDescription("");
                          setContent("");
                          setType("system");
                        }}
                      >
                        Cancel
                      </Button>
                      <Button 
                        onClick={handleCreate}
                        disabled={!title || !content || createPrompt.isPending}
                      >
                        {createPrompt.isPending ? "Creating..." : "Create Prompt"}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          )}
        </div>
      </div>
      <Dialog open={!!promptToDelete} onOpenChange={() => setPromptToDelete(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Prompt</DialogTitle>
            <DialogDescription>
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-3">
            <p>Are you sure you want to delete this prompt?</p>
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => setPromptToDelete(null)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                if (promptToDelete) {
                  handleDelete(promptToDelete);
                  setPromptToDelete(null);
                }
              }}
            >
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

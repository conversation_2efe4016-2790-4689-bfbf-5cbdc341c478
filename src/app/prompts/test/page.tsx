"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2Icon } from "lucide-react";

interface Lead {
  Id: string;
  FirstName: string;
  LastName: string;
  Email: string;
  Company: string;
  Title: string;
  Status: string;
  displayName: string;
}

interface Prompt {
  id: number;
  title: string;
  content: string;
  version: number;
}

export default function PromptsTestPage() {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [selectedLead, setSelectedLead] = useState<string>("");
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [promptTitle, setPromptTitle] = useState<string>("");
  const [promptTemplate, setPromptTemplate] = useState<string>(
    `You are writing a follow-up email to {{FirstName}} {{LastName}} who works at {{Company}} as {{Title}}.
Write a personalized email that references their company and role.

The email should be professional but friendly, and should aim to schedule a meeting.

Format the response as a proper email with subject line and body.`
  );
  const [generatedEmail, setGeneratedEmail] = useState<{
    subject: string;
    body: string;
    formattedPrompt: string;
  } | null>(null);
  const [generating, setGenerating] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch leads
        const leadsResponse = await fetch("/api/leads");
        const leadsData = await leadsResponse.json();

        if (leadsResponse.status === 401 && leadsData.redirectTo) {
          window.location.href = leadsData.redirectTo;
          return;
        }

        if (!leadsResponse.ok) {
          throw new Error(leadsData.error || "Failed to fetch leads");
        }

        // Fetch prompts
        const promptsResponse = await fetch("/api/v1/prompts");
        const promptsData = await promptsResponse.json();

        setLeads(leadsData.leads || []);
        setPrompts(promptsData.prompts || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const generateEmail = async () => {
    if (!selectedLead) {
      setError("Please select a lead first");
      return;
    }

    const lead = leads.find((l) => l.Id === selectedLead);
    if (!lead) return;

    try {
      setGenerating(true);
      setError(null);

      const response = await fetch("/api/v1/prompts/test", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: promptTemplate,
          variables: lead,
          settings: {
            model: "claude-3-5-haiku-20241022",
            temperature: 0.2,
            maxTokens: 4096
          }
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to generate email");
      }

      setGeneratedEmail({
        subject: data.subject,
        body: data.body,
        formattedPrompt: data.formattedPrompt
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to generate email");
    } finally {
      setGenerating(false);
    }
  };

  const savePrompt = async () => {
    if (!promptTitle || !promptTemplate) {
      setError("Title and prompt template are required");
      return;
    }

    try {
      setSaving(true);
      setError(null);

      const method = selectedPrompt ? "PUT" : "POST";
      const body = selectedPrompt
        ? { id: selectedPrompt.id, title: promptTitle, content: promptTemplate }
        : { title: promptTitle, content: promptTemplate };

      const response = await fetch("/api/v1/prompts", {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to save prompt");
      }

      // Refresh prompts list
      const promptsResponse = await fetch("/api/v1/prompts");
      const promptsData = await promptsResponse.json();
      setPrompts(promptsData.prompts || []);

      // Clear form if creating new prompt
      if (!selectedPrompt) {
        setPromptTitle("");
        setPromptTemplate("");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to save prompt");
    } finally {
      setSaving(false);
    }
  };

  const loadPrompt = (prompt: Prompt) => {
    setSelectedPrompt(prompt);
    setPromptTitle(prompt.title);
    setPromptTemplate(prompt.content);
  };

  if (loading) {
    return (
      <div className="p-4 max-w-4xl mx-auto space-y-4">
        <Skeleton className="h-8 w-48" />
        <div className="grid grid-cols-1 gap-4">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent className="space-y-2">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-10 w-32" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 max-w-4xl mx-auto">
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <p className="text-destructive">Error: {error}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-4 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Prompt Testing</h1>

      {/* Prompts List */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Saved Prompts</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 gap-2">
          {prompts.map((prompt) => (
            <Button
              key={`${prompt.id}-${prompt.version}`}
              onClick={() => loadPrompt(prompt)}
              variant="outline"
              className="h-auto py-2 justify-start"
            >
              <div className="text-left">
                <div className="font-medium">{prompt.title}</div>
                <div className="text-sm text-muted-foreground">
                  Version {prompt.version}
                </div>
              </div>
            </Button>
          ))}
        </CardContent>
      </Card>

      {/* Lead Selection */}
      <Card className="mb-4">
        <CardHeader>
          <CardTitle>Select Lead</CardTitle>
        </CardHeader>
        <CardContent>
          <Select value={selectedLead} onValueChange={setSelectedLead}>
            <SelectTrigger>
              <SelectValue placeholder="Select a lead..." />
            </SelectTrigger>
            <SelectContent>
              {leads.map((lead) => (
                <SelectItem key={lead.Id} value={lead.Id}>
                  {lead.displayName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Lead Details */}
          {selectedLead &&
            (() => {
              const lead = leads.find((l) => l.Id === selectedLead);
              return lead ? (
                <div className="mt-4 space-y-2">
                  <h3 className="font-medium">Lead Details</h3>
                  <dl className="grid grid-cols-2 gap-2 text-sm">
                    <dt className="text-muted-foreground">Name:</dt>
                    <dd>
                      {lead.FirstName} {lead.LastName}
                    </dd>
                    <dt className="text-muted-foreground">Company:</dt>
                    <dd>{lead.Company}</dd>
                    <dt className="text-muted-foreground">Title:</dt>
                    <dd>{lead.Title}</dd>
                    <dt className="text-muted-foreground">Email:</dt>
                    <dd>{lead.Email}</dd>
                    <dt className="text-muted-foreground">Status:</dt>
                    <dd>{lead.Status}</dd>
                  </dl>
                </div>
              ) : null;
            })()}
        </CardContent>
      </Card>

      {/* Prompt Form */}
      <Card className="mb-4">
        <CardHeader>
          <CardTitle>Prompt Template</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium leading-none">
              Prompt Title
            </label>
            <Input
              value={promptTitle}
              onChange={(e) => setPromptTitle(e.target.value)}
              placeholder="Enter a title for your prompt"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium leading-none">
              Prompt Template
            </label>
            <Textarea
              value={promptTemplate}
              onChange={(e) => setPromptTemplate(e.target.value)}
              className="min-h-[150px]"
            />
            <div className="p-3 border rounded-md bg-muted/30 mt-2">
              <h4 className="text-sm font-medium mb-2">Доступные переменные:</h4>
              <ul className="list-disc pl-4 grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                <li>{'{{FirstName}}'} - имя лида</li>
                <li>{'{{LastName}}'} - фамилия лида</li>
                <li>{'{{Email}}'} - email лида</li>
                <li>{'{{Company}}'} - компания лида</li>
                <li>{'{{Title}}'} - должность лида</li>
                <li>{'{{Status}}'} - статус лида</li>
                <li>{'{{Id}}'} - ID лида</li>
                <li>{'{{senderName}}'} - имя отправителя (подставляется автоматически)</li>
              </ul>
            </div>
          </div>

          <div className="flex space-x-4">
            <Button
              onClick={savePrompt}
              disabled={saving || !promptTitle || !promptTemplate}
              variant="secondary"
            >
              {saving && <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />}
              {saving
                ? "Saving..."
                : selectedPrompt
                ? "Save New Version"
                : "Save Prompt"}
            </Button>

            <Button
              onClick={generateEmail}
              disabled={generating || !selectedLead}
            >
              {generating && (
                <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
              )}
              {generating ? "Generating..." : "Generate Email"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Generated Email */}
      <Card className="mb-4">
        <CardHeader>
          <CardTitle>Generated Email</CardTitle>
        </CardHeader>
        <CardContent>
          {generating ? (
            <div className="flex justify-center p-8">
              <Loader2Icon className="h-8 w-8 animate-spin" />
            </div>
          ) : generatedEmail ? (
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Subject:</h3>
                <div className="bg-muted rounded-md p-4 whitespace-pre-wrap font-mono text-sm">
                  {generatedEmail.subject}
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium mb-2">Body:</h3>
                <div className="bg-muted rounded-md p-4 whitespace-pre-wrap font-mono text-sm">
                  {generatedEmail.body}
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium mb-2">Formatted Prompt:</h3>
                <p className="text-xs text-muted-foreground mb-2">Полный промпт с подставленными переменными, который был отправлен в API</p>
                <div className="bg-muted rounded-md p-4 whitespace-pre-wrap font-mono text-sm max-h-96 overflow-y-auto">
                  {generatedEmail.formattedPrompt}
                </div>
              </div>
            </div>
          ) : (
            <p className="text-center p-8 text-muted-foreground">
              No email generated yet
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

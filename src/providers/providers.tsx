"use client";

import { QueryProvider } from "./query-provider";
import { SessionProvider } from "next-auth/react";
import { Suspense } from "react";

function ErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <div>
      <Suspense fallback={<div>Loading...</div>}>
        {children}
      </Suspense>
    </div>
  );
}

export function Providers({
  children,
  session,
}: {
  children: React.ReactNode;
  session: any;
}) {
  return (
    <ErrorBoundary>
      <SessionProvider session={session}>
        <QueryProvider>
          {children}
        </QueryProvider>
      </SessionProvider>
    </ErrorBoundary>
  );
}

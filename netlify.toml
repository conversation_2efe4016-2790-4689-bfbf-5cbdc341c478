[build]
  command = "pnpm build"
  publish = ".next"

[[plugins]]
  package = "@netlify/plugin-nextjs"

[build.environment]
  NEXT_TELEMETRY_DISABLED = "1"
  NEXT_USE_NETLIFY_EDGE = "true"
  NODE_VERSION = "20"

# Настройки для перенаправления запросов к background функциям
[[redirects]]
  from = "/.netlify/functions/prompt-test-background"
  to = "/.netlify/functions/prompt-test-background"
  status = 200
  force = true

# Общие настройки для API запросов к Next.js
[[redirects]]
  from = "/api/*"
  to = "/api/:splat"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

[functions]
  # Устанавливаем максимальное время выполнения для background функций
  node_bundler = "esbuild"
  external_node_modules = ["pg-native"]
  
[functions."prompt-test-background"]
  # Увеличенный таймаут для background функции тестирования промптов
  included_files = ["**/*.env", "**/*.js", "**/*.ts"]
  background = true
  timeout = 120
# Module Not Found Fix: pg/fs Resolution

## Проблема

Возникала ошибка при сборке Next.js приложения:

```
Module not found: Can't resolve 'fs'
Import trace for requested module:
./src/services/whatsapp.ts
./src/components/leads/LeadForm.tsx
./src/app/leads/page.tsx
```

## Причина

Ошибка возникла из-за того, что:

1. **Клиентский компонент** `LeadForm.tsx` импортировал функцию `validatePhoneNumber` из `whatsapp.ts`
2. **Серверный сервис** `whatsapp.ts` импортировал модули базы данных (`pg`)
3. **PostgreSQL клиент** (`pg`) пытался использовать Node.js модуль `fs` в браузерном окружении
4. **Next.js** не может разрешить серверные модули в клиентских компонентах

## Решение

### 1. Создан утилитарный файл для валидации

**Файл:** `src/utils/phone-validation.ts`

```typescript
// Phone number validation utility - client-safe
export interface PhoneValidationResult {
  isValid: boolean;
  formatted: string;
  error?: string;
}

export function validatePhoneNumber(phone: string): PhoneValidationResult {
  // Валидация без зависимостей от серверных модулей
}

export function formatPhoneForDisplay(phone: string): string {
  // Форматирование для отображения
}

export function isValidForWhatsApp(phone: string): boolean {
  // Проверка валидности для WhatsApp
}
```

**Особенности:**
- ✅ Не содержит импортов серверных модулей
- ✅ Может использоваться в клиентских компонентах
- ✅ Содержит только чистые функции
- ✅ Полностью автономный модуль

### 2. Обновлены импорты

#### В клиентском компоненте (`LeadForm.tsx`):
```typescript
// Было:
import { validatePhoneNumber } from "@/services/whatsapp";

// Стало:
import { validatePhoneNumber } from "@/utils/phone-validation";
```

#### В серверном сервисе (`whatsapp.ts`):
```typescript
// Добавлено:
import { validatePhoneNumber } from "@/utils/phone-validation";

// Для обратной совместимости:
export { validatePhoneNumber } from "@/utils/phone-validation";
```

### 3. Обновлены тестовые скрипты

Все тестовые файлы теперь импортируют из `@/utils/phone-validation`:
- `src/scripts/test-whatsapp.ts`
- `src/scripts/test-phone-display.ts`

## Архитектурные принципы

### Разделение клиент/сервер

**Клиентские утилиты** (`src/utils/`):
- ✅ Чистые функции без побочных эффектов
- ✅ Без импортов серверных модулей
- ✅ Могут использоваться в браузере
- ✅ Легко тестируются

**Серверные сервисы** (`src/services/`):
- ✅ Работа с базой данных
- ✅ Внешние API вызовы
- ✅ Бизнес-логика
- ✅ Только серверное окружение

### Принцип единственной ответственности

**До исправления:**
- `whatsapp.ts` содержал и валидацию, и работу с БД
- Смешивание клиентской и серверной логики

**После исправления:**
- `phone-validation.ts` - только валидация (клиент-безопасно)
- `whatsapp.ts` - только серверная логика
- Четкое разделение ответственности

## Преимущества решения

### 1. Производительность
- ✅ Уменьшен размер клиентского бандла
- ✅ Исключены серверные зависимости из браузера
- ✅ Быстрая валидация в реальном времени

### 2. Переиспользование
- ✅ Утилиты можно использовать в любых компонентах
- ✅ Независимость от серверной логики
- ✅ Легкое тестирование

### 3. Масштабируемость
- ✅ Четкая архитектура клиент/сервер
- ✅ Простое добавление новых валидаций
- ✅ Возможность расширения функциональности

## Обратная совместимость

Для сохранения существующих импортов добавлен реэкспорт:

```typescript
// В whatsapp.ts
export { validatePhoneNumber } from "@/utils/phone-validation";
```

Это позволяет существующему коду продолжать работать без изменений.

## Рекомендации для будущего

### 1. Структура проекта
```
src/
├── utils/           # Клиент-безопасные утилиты
├── services/        # Серверные сервисы
├── components/      # React компоненты
└── app/            # Next.js страницы и API
```

### 2. Правила импортов
- **Клиентские компоненты** могут импортировать только из `utils/` и `components/`
- **Серверные компоненты** могут импортировать из любых директорий
- **Утилиты** не должны импортировать серверные модули

### 3. Валидация архитектуры
Рекомендуется добавить ESLint правила для предотвращения подобных проблем:

```javascript
// .eslintrc.js
rules: {
  'no-restricted-imports': [
    'error',
    {
      patterns: [
        {
          group: ['**/services/**'],
          message: 'Client components cannot import server services directly'
        }
      ]
    }
  ]
}
```

## Результат

✅ **Ошибка исправлена** - приложение собирается без ошибок  
✅ **Архитектура улучшена** - четкое разделение клиент/сервер  
✅ **Функциональность сохранена** - все возможности работают как прежде  
✅ **Производительность повышена** - меньший размер клиентского бандла

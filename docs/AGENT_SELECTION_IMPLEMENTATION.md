# Agent Selection Implementation for WhatsApp Messages

## Обзор реализации

Успешно добавлена функциональность выбора агентов (AI моделей) для генерации WhatsApp сообщений, аналогично существующей системе для email генерации.

## Анализ существующей системы

### Существующая архитектура агентов:

1. **База данных**: Таблица `agents` с полями:
   - `id`, `name`, `description`, `prompt`
   - `settings` (JSON) - настройки модели
   - `isActive`, `createdBy`, `createdAt`

2. **API endpoints**:
   - `GET /api/v1/agents` - получение списка агентов
   - Агенты фильтруются по `createdBy` (пользователь видит только свои)

3. **Компоненты**:
   - `AgentSelect` - переиспользуемый компонент выбора агента
   - `SendEmailModal` - уже имел выбор агента

## Внесенные изменения

### 1. SendWhatsAppModal (`src/components/leads/SendWhatsAppModal.tsx`)

#### Добавлено состояние для агента:
```typescript
const [selectedAgent, setSelectedAgent] = useState<string>("");
```

#### Добавлен запрос агентов:
```typescript
const { data: agents, isLoading: isLoadingAgents } = useQuery({
  queryKey: ["agents"],
  queryFn: async () => {
    const response = await fetch("/api/v1/agents");
    if (!response.ok) throw new Error("Failed to fetch agents");
    return response.json();
  },
  enabled: isOpen,
});
```

#### Добавлен useEffect для установки агента по умолчанию:
```typescript
useEffect(() => {
  if (isOpen && agents?.agents?.length > 0 && !selectedAgent) {
    setSelectedAgent(String(agents.agents[0].id));
  }
}, [isOpen, agents, selectedAgent]);
```

#### Добавлен UI для выбора агента:
```typescript
<div className="space-y-2">
  <label className="text-sm font-medium">Select Agent</label>
  <Select
    value={selectedAgent}
    onValueChange={setSelectedAgent}
    disabled={isGenerating || isLoadingAgents}
  >
    <SelectTrigger>
      <SelectValue placeholder="Select an agent" />
    </SelectTrigger>
    <SelectContent>
      {agents?.agents?.map((agent: any) => (
        <SelectItem key={agent.id} value={String(agent.id)}>
          <div className="flex flex-col">
            <span>{agent.name}</span>
            <span className="text-sm text-muted-foreground">
              {agent.description}
            </span>
          </div>
        </SelectItem>
      ))}
    </SelectContent>
  </Select>
</div>
```

#### Обновлена передача агента в API:
```typescript
body: JSON.stringify({ 
  leadId: lead.id,
  agentId: selectedAgent || undefined
})
```

### 2. WhatsApp API (`src/app/api/whatsapp/route.ts`)

#### Добавлен параметр agentId:
```typescript
const { leadId, agentId } = await request.json();
```

#### Передача агента в сервис:
```typescript
const messageData = await WhatsAppService.generatePersonalizedMessage(
  lead[0].id,
  undefined, // customPrompt
  agentId ? parseInt(agentId) : undefined
);
```

### 3. WhatsAppService (`src/services/whatsapp.ts`)

#### Обновлена сигнатура метода:
```typescript
static async generatePersonalizedMessage(
  leadId: number,
  customPrompt?: string,
  agentId?: number
): Promise<{...}>
```

#### Добавлена логика получения агента:
```typescript
// Get agent if specified
let agentPrompt = customPrompt;
if (agentId && !customPrompt) {
  const agent = await db
    .select()
    .from(agents)
    .where(eq(agents.id, agentId))
    .limit(1);

  if (agent && agent[0]) {
    agentPrompt = agent[0].prompt;
  }
}
```

#### Использование промпта агента:
```typescript
const basePrompt = agentPrompt || WHATSAPP_PROMPT;
```

### 4. Улучшение SendEmailModal

#### Добавлено описание агента в выпадающий список:
```typescript
<SelectItem key={agent.id} value={String(agent.id)}>
  <div className="flex flex-col">
    <span>{agent.name}</span>
    <span className="text-sm text-muted-foreground">
      {agent.description}
    </span>
  </div>
</SelectItem>
```

## Архитектурные принципы

### 1. Единообразие
- ✅ Одинаковый подход для email и WhatsApp
- ✅ Переиспользование существующих API endpoints
- ✅ Консистентный UI/UX

### 2. Обратная совместимость
- ✅ Агент является опциональным параметром
- ✅ Fallback на дефолтный промпт, если агент не выбран
- ✅ Существующая функциональность не нарушена

### 3. Безопасность
- ✅ Агенты фильтруются по пользователю
- ✅ Валидация входящих параметров
- ✅ Обработка ошибок

## Пользовательский опыт

### Workflow для пользователя:

1. **Открытие модального окна WhatsApp**
   - Автоматически загружаются доступные агенты
   - Первый агент выбирается по умолчанию

2. **Выбор агента**
   - Выпадающий список с названием и описанием
   - Визуальная обратная связь при загрузке

3. **Генерация сообщений**
   - Кнопка отключена, если агент не выбран
   - Используется промпт выбранного агента

4. **Результат**
   - Персонализированные сообщения на основе агента
   - Сохранение информации об использованном промпте

## Преимущества реализации

### 1. Гибкость
- Пользователи могут создавать разные агенты для разных сценариев
- Легкое переключение между стилями сообщений
- Возможность A/B тестирования промптов

### 2. Масштабируемость
- Единая система агентов для всех типов сообщений
- Простое добавление новых типов контента
- Централизованное управление промптами

### 3. Удобство использования
- Интуитивный интерфейс
- Автоматический выбор по умолчанию
- Описательные названия агентов

## Тестирование

### Сценарии для проверки:

1. **Выбор агента**
   - Загрузка списка агентов
   - Автоматический выбор первого агента
   - Переключение между агентами

2. **Генерация с агентом**
   - Использование промпта агента
   - Fallback на дефолтный промпт
   - Сохранение правильного промпта

3. **Обратная совместимость**
   - Работа без выбора агента
   - Работа с кастомным промптом
   - Работа тестовых endpoints

## Следующие шаги

### Возможные улучшения:

1. **Предварительный просмотр промпта**
   - Показ промпта выбранного агента
   - Возможность редактирования на лету

2. **Статистика использования**
   - Трекинг эффективности агентов
   - Аналитика по конверсии

3. **Шаблоны агентов**
   - Предустановленные агенты
   - Импорт/экспорт конфигураций

4. **Расширенные настройки**
   - Настройка температуры и других параметров
   - Выбор модели AI для каждого агента

## Результат

✅ **Функциональность реализована** - выбор агентов работает для WhatsApp  
✅ **Единообразие достигнуто** - одинаковый подход для email и WhatsApp  
✅ **Обратная совместимость** - существующая функциональность не нарушена  
✅ **Пользовательский опыт улучшен** - интуитивный выбор агентов  

Пользователи теперь могут выбирать AI агентов для генерации WhatsApp сообщений так же, как они это делают для email сообщений.

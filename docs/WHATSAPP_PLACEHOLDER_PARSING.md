# WhatsApp Placeholder Parsing Implementation

## Обзор реализации

Успешно реализован парсинг плейсхолдеров в WhatsApp сервисе, аналогично системе генерации email. Теперь WhatsApp сообщения генерируются с корректно замененными плейсхолдерами на реальные данные лидов.

## Проблема

**До реализации:** WhatsApp сообщения содержали непарсенные плейсхолдеры:
```
"Здравствуйте, {{leadFirstName}} {{leadLastName}}, 

Я заметил, что вы работаете в {{leadCompany}} и находитесь в городе {{details_city}}.

Мы нашли ваш профиль в {{communications_linkedin}} и хотели бы предложить вам наши услуги.

С уважением,
{{senderName}}"
```

**После реализации:** Плейсхолдеры корректно заменяются:
```
"Здравствуйте, Иван Петров, 

Я заметил, что вы работаете в TechCorp и находитесь в городе Москва.

Мы нашли ваш профиль в https://linkedin.com/in/ivanpetrov и хотели бы предложить вам наши услуги.

С уважением,
Анна Смирнова"
```

## Архитектура решения

### 1. Утилитарный модуль (`src/utils/whatsapp-placeholder-parser.ts`)

#### Основные функции:

**`parseWhatsAppPlaceholders(template, context)`**
- Заменяет плейсхолдеры в шаблоне на реальные данные
- Поддерживает вложенные JSON поля
- Обрабатывает отсутствующие данные

**`normalizeWhatsAppPromptVariables(prompt)`**
- Нормализует различные форматы плейсхолдеров к стандартному
- Обеспечивает совместимость с email системой

**`detectUnknownWhatsAppPlaceholders(template)`**
- Обнаруживает неизвестные плейсхолдеры
- Помогает в отладке шаблонов

**`validateWhatsAppTemplate(template)`**
- Валидирует шаблон WhatsApp сообщения
- Возвращает предупреждения и ошибки

### 2. Интеграция в WhatsApp сервис (`src/services/whatsapp.ts`)

#### Обновления:

**Импорты:**
```typescript
import { 
  parseWhatsAppPlaceholders, 
  normalizeWhatsAppPromptVariables,
  WhatsAppContext 
} from "@/utils/whatsapp-placeholder-parser";
```

**Контекст для парсинга:**
```typescript
const whatsappContext: WhatsAppContext = {
  lead: leadData,
  senderName: "SalesFlow Team",
  senderCompany: "SalesFlow", 
  senderPosition: "Sales Manager"
};
```

**Обработка промпта:**
```typescript
// Нормализация переменных
const normalizedPrompt = normalizeWhatsAppPromptVariables(basePrompt);

// Парсинг плейсхолдеров
const prompt = parseWhatsAppPlaceholders(normalizedPrompt, whatsappContext);
```

**Обработка сгенерированного контента:**
```typescript
// Парсинг плейсхолдеров в сгенерированном сообщении
messageContent = parseWhatsAppPlaceholders(messageContent, whatsappContext);
```

## Поддерживаемые плейсхолдеры

### Базовая информация о лиде:
- `{{leadFirstName}}` → Имя лида
- `{{leadLastName}}` → Фамилия лида
- `{{leadFullName}}` → Полное имя лида
- `{{leadCompany}}` → Компания лида
- `{{leadTitle}}` → Должность лида
- `{{leadEmail}}` → Email лида
- `{{leadPhone}}` → Телефон лида
- `{{leadIndustry}}` → Отрасль лида
- `{{leadWebsite}}` → Сайт лида
- `{{leadStatus}}` → Статус лида
- `{{leadDescription}}` → Описание лида

### Информация об отправителе:
- `{{senderName}}` → Имя отправителя
- `{{senderCompany}}` → Компания отправителя
- `{{senderPosition}}` → Должность отправителя

### JSON поля - Details:
- `{{details_city}}` → Город из JSON поля details
- `{{details_country}}` → Страна из JSON поля details
- `{{details_region}}` → Регион из JSON поля details
- `{{details_address}}` → Адрес из JSON поля details
- `{{details_linkedin}}` → LinkedIn из JSON поля details

### JSON поля - Communications:
- `{{communications_linkedin}}` → LinkedIn из JSON поля communications
- `{{communications_email}}` → Email из JSON поля communications
- `{{communications_phone}}` → Телефон из JSON поля communications
- `{{communications_website}}` → Сайт из JSON поля communications
- `{{communications_telegram}}` → Telegram из JSON поля communications
- `{{communications_whatsapp}}` → WhatsApp из JSON поля communications

### JSON поля - Company Info:
- `{{company_info_name}}` → Название компании
- `{{company_info_description}}` → Описание компании
- `{{company_info_industry}}` → Отрасль компании
- `{{company_info_size}}` → Размер компании
- `{{company_info_website}}` → Сайт компании
- `{{company_info_location}}` → Местоположение компании

### Legacy плейсхолдеры (обратная совместимость):
- `{{lead_details}}` → JSON строка с деталями лида
- `{{lead_history}}` → JSON строка с историей лида
- `{{company_info}}` → JSON строка с информацией о компании

## Нормализация плейсхолдеров

Система автоматически нормализует различные форматы:

```typescript
// Эти форматы автоматически преобразуются:
"{{lead.firstName}}" → "{{leadFirstName}}"
"{{lead.company}}" → "{{leadCompany}}"
"{{manager.name}}" → "{{senderName}}"
"{{leadName}}" → "{{leadFirstName}}"
"{{companyName}}" → "{{leadCompany}}"
```

## Обработка отсутствующих данных

### Fallback значения:
- Пустые строки для отсутствующих базовых полей
- Пустые строки для отсутствующих JSON полей
- Дефолтные значения для отправителя

### Пример:
```typescript
// Если у лида нет города:
"Вы из {{details_city}}" → "Вы из " (пустая строка)

// Если у лида нет имени:
"Привет {{leadFirstName}}" → "Привет " (пустая строка)
```

## Валидация и отладка

### Обнаружение неизвестных плейсхолдеров:
```typescript
const unknownPlaceholders = detectUnknownWhatsAppPlaceholders(template);
// Возвращает: ["unknownField", "anotherUnknown"]
```

### Валидация шаблона:
```typescript
const validation = validateWhatsAppTemplate(template);
// Возвращает: { isValid: boolean, unknownPlaceholders: string[], warnings: string[] }
```

## Тестирование

### Тестовый скрипт: `src/scripts/test-whatsapp-placeholders.ts`

Проверяет:
- ✅ Базовые плейсхолдеры
- ✅ JSON поля
- ✅ Нормализацию
- ✅ Обработку отсутствующих данных
- ✅ Обнаружение неизвестных плейсхолдеров
- ✅ Валидацию шаблонов

### Запуск тестов:
```bash
npx tsx src/scripts/test-whatsapp-placeholders.ts
```

## Обратная совместимость

### Сохранена поддержка:
- ✅ Существующих промптов агентов
- ✅ Legacy плейсхолдеров (`{{lead_details}}`, etc.)
- ✅ Старых форматов переменных (`{{lead.firstName}}`, etc.)
- ✅ Кастомных промптов

### Автоматическая миграция:
- Старые форматы автоматически нормализуются
- Новые плейсхолдеры работают сразу
- Никаких breaking changes

## Примеры использования

### 1. Простой шаблон:
```
Привет {{leadFirstName}}! 👋

Заметил, что вы работаете в {{leadCompany}}. 
Хотел бы обсудить возможности сотрудничества.

{{senderName}}
```

### 2. Шаблон с JSON полями:
```
{{leadFirstName}}, привет!

Нашел ваш профиль: {{communications_linkedin}}
Вы из {{details_city}}? Отличный город!

Ваша компания {{company_info_name}} в сфере {{company_info_industry}} - это интересно!

Давайте обсудим, как {{senderCompany}} может помочь.

{{senderName}}
```

### 3. Шаблон с fallback:
```
Здравствуйте{{#if leadFirstName}}, {{leadFirstName}}{{/if}}!

{{#if leadCompany}}Работаете в {{leadCompany}}? {{/if}}
{{#if details_city}}Из {{details_city}}? {{/if}}

Хотели бы предложить наши услуги.

С уважением,
{{senderName}}
```

## Результат

✅ **Плейсхолдеры парсятся корректно** - все переменные заменяются на реальные данные  
✅ **Поддержка JSON полей** - доступ к вложенным данным лидов  
✅ **Обратная совместимость** - существующие промпты продолжают работать  
✅ **Обработка отсутствующих данных** - graceful fallback для пустых полей  
✅ **Валидация и отладка** - инструменты для проверки шаблонов  

WhatsApp сообщения теперь генерируются с полностью персонализированным контентом!

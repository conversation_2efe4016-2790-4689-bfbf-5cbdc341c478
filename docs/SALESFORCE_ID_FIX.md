# Salesforce ID Fix for WhatsApp Service

## Проблема

При попытке отправить WhatsApp сообщение возникала ошибка:

```
Error: No lead found with Salesforce ID: 
```

## Причина

1. **У лидов может отсутствовать Salesforce ID** - лиды могут быть созданы напрямую в системе без синхронизации с Salesforce
2. **Неправильная логика поиска** - сервис WhatsApp искал лидов по `salesforceId`, который может быть `null` или пустой строкой
3. **API передавал пустую строку** - `lead[0].salesforceId ?? ''` передавал пустую строку, если `salesforceId` был `null`

## Анализ проблемы

### Исходная логика:
```typescript
// API передавал:
const messageData = await WhatsAppService.generatePersonalizedMessage(
    lead[0].salesforceId ?? ''  // Пустая строка, если нет salesforceId
);

// Сервис искал:
const lead = await db
    .select()
    .from(leads)
    .where(eq(leads.salesforceId, salesforceId))  // Поиск по пустой строке
    .limit(1);
```

### Проблемы:
- Поиск по пустой строке не находит лидов
- Лиды без Salesforce ID не могли использовать WhatsApp функциональность
- Ошибка возникала для всех лидов, созданных вручную

## Решение

### 1. Изменена основная логика сервиса

**Было:**
```typescript
static async generatePersonalizedMessage(
    salesforceId: string,
    customPrompt?: string
): Promise<{
    lead_id: string;
    // ...
}>
```

**Стало:**
```typescript
static async generatePersonalizedMessage(
    leadId: number,
    customPrompt?: string
): Promise<{
    lead_id: number;
    // ...
}>
```

### 2. Обновлен поиск лида

**Было:**
```typescript
const lead = await db
    .select()
    .from(leads)
    .where(eq(leads.salesforceId, salesforceId))
    .limit(1);
```

**Стало:**
```typescript
const lead = await db
    .select()
    .from(leads)
    .where(eq(leads.id, leadId))
    .limit(1);
```

### 3. Обновлен API endpoint

**Было:**
```typescript
const messageData = await WhatsAppService.generatePersonalizedMessage(
    lead[0].salesforceId ?? ''
);
```

**Стало:**
```typescript
const messageData = await WhatsAppService.generatePersonalizedMessage(
    lead[0].id
);
```

### 4. Добавлен метод для обратной совместимости

Для тестовых endpoints и случаев, когда нужно работать с Salesforce ID:

```typescript
static async generatePersonalizedMessageBySalesforceId(
    salesforceId: string,
    customPrompt?: string
): Promise<{
    lead_id: string;
    message_id: number;
    content: string;
    phone_number: string;
    token_usage: any;
}> {
    // Находит лида по Salesforce ID, затем использует основной метод
    const lead = await db
        .select()
        .from(leads)
        .where(eq(leads.salesforceId, salesforceId))
        .limit(1);

    if (!lead || !lead[0]) {
        throw new Error(`No lead found with Salesforce ID: ${salesforceId}`);
    }

    const result = await this.generatePersonalizedMessage(lead[0].id, customPrompt);
    
    return {
        ...result,
        lead_id: salesforceId,  // Возвращает Salesforce ID для совместимости
    };
}
```

## Преимущества решения

### 1. Универсальность
- ✅ Работает с лидами с Salesforce ID
- ✅ Работает с лидами без Salesforce ID
- ✅ Работает с лидами, созданными вручную

### 2. Надежность
- ✅ Поиск по внутреннему ID всегда уникален
- ✅ Нет проблем с пустыми или null значениями
- ✅ Четкие сообщения об ошибках

### 3. Обратная совместимость
- ✅ Тестовые endpoints продолжают работать
- ✅ Существующий код не сломан
- ✅ Плавный переход на новую логику

## Файлы изменены

- `src/services/whatsapp.ts` - основная логика сервиса
- `src/app/api/whatsapp/route.ts` - основной API endpoint
- `src/app/api/test/whatsapp/generate/route.ts` - тестовый endpoint
- `src/app/api/test/whatsapp/send/route.ts` - тестовый endpoint

## Тестирование

### Сценарии для проверки:

1. **Лид с Salesforce ID**
   - Должен работать как раньше
   - WhatsApp сообщения генерируются и отправляются

2. **Лид без Salesforce ID**
   - Теперь работает корректно
   - Использует внутренний ID для поиска

3. **Лид, созданный вручную**
   - Полная поддержка WhatsApp функциональности
   - Нет ошибок при генерации сообщений

4. **Тестовые endpoints**
   - Продолжают работать с Salesforce ID
   - Обратная совместимость сохранена

## Рекомендации

### 1. Для новых интеграций
Используйте основной метод с внутренним ID:
```typescript
await WhatsAppService.generatePersonalizedMessage(leadId);
```

### 2. Для legacy кода
Используйте метод с Salesforce ID:
```typescript
await WhatsAppService.generatePersonalizedMessageBySalesforceId(salesforceId);
```

### 3. Проверка данных
Всегда проверяйте наличие необходимых полей:
```typescript
if (!lead.phone) {
    throw new Error("Lead does not have a phone number");
}
```

## Результат

✅ **Ошибка исправлена** - WhatsApp работает для всех типов лидов  
✅ **Универсальность** - поддержка лидов с и без Salesforce ID  
✅ **Обратная совместимость** - существующий код продолжает работать  
✅ **Надежность** - четкая логика поиска и обработки ошибок

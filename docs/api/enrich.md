# Lead Enrichment API Documentation

This document describes the API endpoints for lead enrichment functionality.

## Lead Enrichment Flow

The lead enrichment process follows this flow:

1. **Create Lead & Start Enrichment** - `POST /api/v1/leads/enrich`
2. **Check Operation Status** - `GET /api/v1/operations/:uuid`
3. **Retrieve Enriched Lead Data** - `GET /api/v1/leads/:id`

## 1. Create Lead Enrichment API Endpoint

Initiates the lead enrichment process by creating or updating a lead and sending it to the enrichment service.

**URL**: `/api/v1/leads/enrich`

**Method**: `POST`

**Authentication**: Required (API token or session)

### Request Body

```json
{
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "company": "Example Corp",
  "communications": {
    "linkedin": "https://linkedin.com/in/johndoe",
    "facebook": "https://facebook.com/johndoe",
    "telegram": "@johndoe",
    "vkontakte": "https://vk.com/johndoe"
  },
  "description": "Met at conference",
  "details": {
    "linkedin_info": {
      "url": "https://linkedin.com/in/johndoe",
      "content": "LinkedIn profile content"
    }
  },
  "webhook_url": "https://example.com/webhook"
}
```

All fields are optional. If a lead with the same email already exists, it will be updated instead of creating a new one.

### Language Parameter

The `lang` field specifies the language for the enrichment process:
- **Supported values**: `"ru"` (Russian), `"en"` (English)
- **Default**: `"ru"` (Russian)
- **Usage**: This parameter is passed to the external enrichment service to determine the language for processing and generating enriched content.

### Complete Request Example with All Possible Parameters

```json
{
  "first_name": "John",                      // Lead_First_Name
  "last_name": "Doe",                        // Lead_Last_Name
  "email": "<EMAIL>",           // Lead_Email
  "company": "Example Corp",                 // Lead_Company
  "description": "Met at conference. Interested in our enterprise solution.",
  "communications": {
    // Social media and contact information
    "facebook": "https://facebook.com/johndoe",  // Lead_Facebook
    "telegram": "@johndoe",                      // Lead telegram
    "vkontakte": "https://vk.com/johndoe"        // Lead_VK
  },
  "details": {
    "linkedin_info": {
      "url": "https://linkedin.com/in/johndoe",  // Lead_LinkedIn
      "content": "John Doe is the CEO of Example Corp, a technology company specializing in AI solutions. He has over 15 years of experience in the tech industry."  // Lead_Description
    }
  },
  "lang": "ru",                              // Language for enrichment (default: "ru")
  "webhook_url": "https://example.com/webhook"
}
```

> **Note**: The fields in this example correspond directly to the fields used in the enrichment process:
> - `first_name` → Lead_First_Name
> - `last_name` → Lead_Last_Name
> - `email` → Lead_Email
> - `company` → Lead_Company
> - `communications.facebook` → Lead_Facebook
> - `communications.telegram` → Lead telegram
> - `communications.vkontakte` → Lead_VK
> - `details.linkedin_info.url` → Lead_LinkedIn
> - `details.linkedin_info.content` → Lead_Description
> - `lang` → Language for enrichment processing (supported: "ru", "en", default: "ru")

### Response

```json
{
  "success": true,
  "uuid": "123e4567-e89b-12d3-a456-************",
  "operation": {
    "uuid": "123e4567-e89b-12d3-a456-************",
    "type": "LEAD_ENRICHMENT",
    "status": "PENDING",
    "createdAt": "2023-01-01T00:00:00.000Z"
  },
  "lead": {
    "id": 123
  }
}
```

### cURL Example

```bash
curl -X POST \
  https://app.salesflow.team/api/v1/leads/enrich \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer YOUR_API_TOKEN' \
  -d '{
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "company": "Example Corp",
    "communications": {
      "linkedin": "https://linkedin.com/in/johndoe"
    }
  }'
```

### Node.js Example

```javascript
const response = await fetch('https://app.salesflow.team/api/v1/leads/enrich', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_API_TOKEN'
  },
  body: JSON.stringify({
    first_name: 'John',
    last_name: 'Doe',
    email: '<EMAIL>',
    company: 'Example Corp',
    communications: {
      linkedin: 'https://linkedin.com/in/johndoe'
    }
  })
});

const data = await response.json();
console.log(data);
```

### PHP Example

```php
$curl = curl_init();

curl_setopt_array($curl, [
  CURLOPT_URL => "https://app.salesflow.team/api/v1/leads/enrich",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 30,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "POST",
  CURLOPT_POSTFIELDS => json_encode([
    "first_name" => "John",
    "last_name" => "Doe",
    "email" => "<EMAIL>",
    "company" => "Example Corp",
    "communications" => [
      "linkedin" => "https://linkedin.com/in/johndoe"
    ]
  ]),
  CURLOPT_HTTPHEADER => [
    "Authorization: Bearer YOUR_API_TOKEN",
    "Content-Type: application/json"
  ],
]);

$response = curl_exec($curl);
$err = curl_error($curl);

curl_close($curl);

if ($err) {
  echo "cURL Error #:" . $err;
} else {
  echo $response;
}
```

### Python Example

```python
import requests
import json

url = "https://app.salesflow.team/api/v1/leads/enrich"

payload = json.dumps({
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "company": "Example Corp",
  "communications": {
    "linkedin": "https://linkedin.com/in/johndoe"
  }
})
headers = {
  'Content-Type': 'application/json',
  'Authorization': 'Bearer YOUR_API_TOKEN'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)
```

## 2. Enrichment Status API Endpoint

Checks the status of a lead enrichment operation.

**URL**: `/api/v1/operations/:uuid`

**Method**: `GET`

**Authentication**: Required (API token or session)

### Response

```json
{
  "success": true,
  "operation": {
    "uuid": "123e4567-e89b-12d3-a456-************",
    "type": "LEAD_ENRICHMENT",
    "status": "completed",
    "details": "...",
    "error": null,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:01:00.000Z",
    "completedAt": "2023-01-01T00:01:00.000Z"
  }
}
```

The `status` field can be one of: `"PENDING"`, `"PROCESSING"`, `"completed"` (for success), `"ERROR"`, or `"CANCELLED"`.

### cURL Example

```bash
curl -X GET \
  https://app.salesflow.team/api/v1/operations/123e4567-e89b-12d3-a456-************ \
  -H 'Authorization: Bearer YOUR_API_TOKEN'
```

### Node.js Example

```javascript
const response = await fetch('https://app.salesflow.team/api/v1/operations/123e4567-e89b-12d3-a456-************', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer YOUR_API_TOKEN'
  }
});

const data = await response.json();
console.log(data);
```

## 3. Lead Data Retrieval API Endpoint

Retrieves complete lead data by lead ID, including any enriched information.

**URL**: `/api/v1/leads/:id`

**Method**: `GET`

**Authentication**: Required (API token or session)

### Response

```json
{
  "success": true,
  "lead": {
    "id": 123,
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "company": "Example Corp",
    "title": "CEO",
    "phone": "+1234567890",
    "industry": "Technology",
    "rating": "Hot",
    "leadSource": "Website",
    "description": "John Doe is the CEO of Example Corp, a technology company...",
    "website": "https://example.com",
    "numberOfEmployees": 100,
    "status": "New",
    "details": {
      "linkedin_info": {
        "url": "https://linkedin.com/in/johndoe",
        "content": "LinkedIn profile content"
      }
    },
    "communications": {
      "linkedin": "https://linkedin.com/in/johndoe",
      "facebook": "https://facebook.com/johndoe"
    },
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:01:00.000Z"
  }
}
```

### cURL Example

```bash
curl -X GET \
  https://app.salesflow.team/api/v1/leads/123 \
  -H 'Authorization: Bearer YOUR_API_TOKEN'
```

### Node.js Example

```javascript
const response = await fetch('https://app.salesflow.team/api/v1/leads/123', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer YOUR_API_TOKEN'
  }
});

const data = await response.json();
console.log(data);
```

## Polling for Operation Status

To implement polling for lead enrichment status, check the operation status every 15 seconds until it becomes 'completed':

```javascript
async function pollOperationStatus(operationUuid) {
  const maxAttempts = 20; // Poll for up to 5 minutes (15s * 20)
  let attempts = 0;

  while (attempts < maxAttempts) {
    const response = await fetch(`https://app.salesflow.team/api/v1/operations/${operationUuid}`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer YOUR_API_TOKEN'
      }
    });

    const data = await response.json();

    if (data.operation.status === 'completed' ||
        data.operation.status === 'ERROR' ||
        data.operation.status === 'CANCELLED') {
      return data;
    }

    // Wait for 15 seconds before next poll
    await new Promise(resolve => setTimeout(resolve, 15000));
    attempts++;
  }

  throw new Error('Polling timed out');
}
```

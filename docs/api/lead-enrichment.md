# Lead Enrichment API Documentation

This document describes the API endpoints for lead management and enrichment.

## 1. Create Lead Enrichment API Endpoint

Initiates the lead enrichment process for a given lead.

**URL**: `/api/v1/leads/enrich`

**Method**: `POST`

**Authentication**: Required (API token or session)

### Request Body

```json
{
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "company": "Example Corp",
  "communications": {
    "linkedin": "https://linkedin.com/in/johndoe",
    "facebook": "https://facebook.com/johndoe",
    "telegram": "@johndoe",
    "vkontakte": "https://vk.com/johndoe"
  },
  "description": "Met at conference",
  "details": {
    "linkedin_info": {
      "url": "https://linkedin.com/in/johndoe",
      "content": "LinkedIn profile content"
    }
  },
  "webhook_url": "https://example.com/webhook"
}
```

All fields are optional. If a lead with the same email already exists, it will be updated.

### Response

```json
{
  "success": true,
  "operation": {
    "uuid": "123e4567-e89b-12d3-a456-************",
    "type": "LEAD_ENRICHMENT",
    "status": "PENDING",
    "createdAt": "2023-01-01T00:00:00.000Z"
  },
  "lead": {
    "id": 123
  }
}
```

### cURL Example

```bash
curl -X POST \
  https://your-domain.com/api/v1/leads/enrich \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer YOUR_API_TOKEN' \
  -d '{
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "company": "Example Corp",
    "communications": {
      "linkedin": "https://linkedin.com/in/johndoe"
    }
  }'
```

### Node.js Example

```javascript
const response = await fetch('https://app.salesflow.team/api/v1/leads/enrich', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_API_TOKEN'
  },
  body: JSON.stringify({
    first_name: 'John',
    last_name: 'Doe',
    email: '<EMAIL>',
    company: 'Example Corp',
    communications: {
      linkedin: 'https://linkedin.com/in/johndoe'
    }
  })
});

const data = await response.json();
console.log(data);
```

## 2. Enrichment Status API Endpoint

Checks the status of a lead enrichment operation.

**URL**: `/api/v1/operations/:uuid`

**Method**: `GET`

**Authentication**: Required (API token or session)

### Response

```json
{
  "success": true,
  "operation": {
    "uuid": "123e4567-e89b-12d3-a456-************",
    "type": "LEAD_ENRICHMENT",
    "status": "SUCCESS",
    "details": "...",
    "error": null,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:01:00.000Z",
    "completedAt": "2023-01-01T00:01:00.000Z"
  }
}
```

### cURL Example

```bash
curl -X GET \
  https://your-domain.com/api/v1/operations/123e4567-e89b-12d3-a456-************ \
  -H 'Authorization: Bearer YOUR_API_TOKEN'
```

### Node.js Example

```javascript
const response = await fetch('https://your-domain.com/api/v1/operations/123e4567-e89b-12d3-a456-************', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer YOUR_API_TOKEN'
  }
});

const data = await response.json();
console.log(data);
```

## 3. Lead Data Retrieval API Endpoint

Retrieves complete lead data by lead ID.

**URL**: `/api/v1/leads/:id`

**Method**: `GET`

**Authentication**: Required (API token or session)

### Response

```json
{
  "success": true,
  "lead": {
    "id": 123,
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "company": "Example Corp",
    "title": "CEO",
    "phone": "+1234567890",
    "industry": "Technology",
    "rating": "Hot",
    "leadSource": "Website",
    "description": "Met at conference",
    "website": "https://example.com",
    "numberOfEmployees": 100,
    "status": "New",
    "details": {
      "linkedin_info": {
        "url": "https://linkedin.com/in/johndoe",
        "content": "LinkedIn profile content"
      }
    },
    "communications": {
      "linkedin": "https://linkedin.com/in/johndoe",
      "facebook": "https://facebook.com/johndoe"
    },
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:01:00.000Z"
  }
}
```

### cURL Example

```bash
curl -X GET \
  https://your-domain.com/api/v1/leads/123 \
  -H 'Authorization: Bearer YOUR_API_TOKEN'
```

### Node.js Example

```javascript
const response = await fetch('https://your-domain.com/api/v1/leads/123', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer YOUR_API_TOKEN'
  }
});

const data = await response.json();
console.log(data);
```

## Polling for Operation Status

To implement polling for lead enrichment status, check the operation status every 15 seconds until it becomes 'completed':

```javascript
async function pollOperationStatus(operationUuid) {
  const maxAttempts = 20; // Poll for up to 5 minutes (15s * 20)
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    const response = await fetch(`https://your-domain.com/api/v1/operations/${operationUuid}`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer YOUR_API_TOKEN'
      }
    });
    
    const data = await response.json();
    
    if (data.operation.status === 'SUCCESS' || 
        data.operation.status === 'ERROR' || 
        data.operation.status === 'CANCELLED') {
      return data;
    }
    
    // Wait for 15 seconds before next poll
    await new Promise(resolve => setTimeout(resolve, 15000));
    attempts++;
  }
  
  throw new Error('Polling timed out');
}
```

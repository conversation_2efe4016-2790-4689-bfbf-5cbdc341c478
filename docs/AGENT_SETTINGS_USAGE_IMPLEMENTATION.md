# Agent Settings Usage in WhatsApp Service

## Проблема решена

WhatsApp сервис теперь использует параметры модели из выбранного агента вместо захардкоженных значений.

### До исправления:
```typescript
// Захардкоженные параметры
const llmParams = {
  model: "claude-3-sonnet-20240229",
  maxTokens: 1000,
  temperature: 0.2,
  promptLength: prompt.length
};
```

### После исправления:
```typescript
// Параметры из настроек агента с fallback на дефолтные
const agentSettings = agentData?.settings as {
  model?: string;
  maxTokens?: number;
  temperature?: number;
  llmProvider?: string;
} || {};

const llmParams = {
  model: agentSettings.model || "claude-3-sonnet-20240229",
  maxTokens: agentSettings.maxTokens || 1000,
  temperature: agentSettings.temperature || 0.2,
  promptLength: prompt.length
};
```

## Реализация

### 1. Обновленная логика извлечения параметров

```typescript
// Prepare LLM request parameters from agent settings or defaults
const agentSettings = agentData?.settings as {
  model?: string;
  maxTokens?: number;
  temperature?: number;
  llmProvider?: string;
} || {};

const llmParams = {
  model: agentSettings.model || "claude-3-sonnet-20240229",
  maxTokens: agentSettings.maxTokens || 1000,
  temperature: agentSettings.temperature || 0.2,
  promptLength: prompt.length
};
```

### 2. Типизация настроек агента

Добавлена правильная типизация для безопасного доступа к настройкам:
- `model?: string` - модель LLM
- `maxTokens?: number` - максимальное количество токенов
- `temperature?: number` - температура генерации
- `llmProvider?: string` - провайдер LLM

### 3. Fallback на дефолтные значения

Если настройки агента отсутствуют или неполные, используются дефолтные значения:
- **model**: `"claude-3-sonnet-20240229"`
- **maxTokens**: `1000`
- **temperature**: `0.2`

## Поддерживаемые модели

### Claude Models:
- **claude-3-haiku-20240307** - Быстрая, экономичная модель
- **claude-3-sonnet-20240229** - Сбалансированная модель (дефолт)
- **claude-3-opus-20240229** - Самая мощная модель
- **claude-3-5-sonnet-20241022** - Новейшая модель

### Характеристики моделей:

| Модель | Скорость | Стоимость | Качество | Применение |
|--------|----------|-----------|----------|------------|
| Haiku | Быстрая | Низкая | Хорошее | Массовые сообщения |
| Sonnet | Средняя | Средняя | Отличное | Универсальное |
| Opus | Медленная | Высокая | Превосходное | Сложные задачи |
| Sonnet 3.5 | Быстрая | Средняя | Превосходное | Новейшие возможности |

## Настройки температуры

### Рекомендации по температуре:

- **0.1-0.3**: Консервативные, последовательные ответы
- **0.4-0.6**: Сбалансированная креативность
- **0.7-0.9**: Креативные, разнообразные ответы

### Примеры использования:

```typescript
// Консервативный агент для формальных сообщений
{
  model: "claude-3-sonnet-20240229",
  maxTokens: 500,
  temperature: 0.1
}

// Креативный агент для маркетинговых сообщений
{
  model: "claude-3-5-sonnet-20241022", 
  maxTokens: 1500,
  temperature: 0.8
}

// Сбалансированный агент для общих целей
{
  model: "claude-3-sonnet-20240229",
  maxTokens: 1000,
  temperature: 0.5
}
```

## Настройки maxTokens

### Рекомендации по количеству токенов:

- **300-600**: Короткие WhatsApp сообщения
- **800-1200**: Средние сообщения с деталями
- **1500+**: Длинные сообщения или email

### Оценка длины:

```typescript
// Примерные соотношения:
// 1 токен ≈ 4 символа (для английского)
// 1 токен ≈ 2-3 символа (для русского)

// Короткое WhatsApp сообщение (150 символов) ≈ 50-75 токенов
// Среднее сообщение (300 символов) ≈ 100-150 токенов
// Длинное сообщение (600 символов) ≈ 200-300 токенов
```

## Обработка edge cases

### 1. Отсутствующие настройки агента:
```typescript
// agentData = null или agentData.settings = {}
// Результат: используются все дефолтные значения
```

### 2. Частичные настройки:
```typescript
// agentData.settings = { model: "claude-3-haiku-20240307" }
// Результат: model из агента, остальное - дефолтные значения
```

### 3. Некорректные типы данных:
```typescript
// agentData.settings = { maxTokens: "not-a-number" }
// Результат: используется дефолтное значение (1000)
```

## Логирование параметров

Обновленное логирование включает информацию об источнике параметров:

```typescript
WhatsAppLogger.logLLMRequest(logContext, {
  model: llmParams.model,
  maxTokens: llmParams.maxTokens,
  temperature: llmParams.temperature,
  promptLength: llmParams.promptLength
});
```

### Пример лога:
```
🤖 [WhatsApp] LLM_REQUEST - 2024-01-15T10:30:00.250Z
📋 Context: [Lead ID: 123 | Agent ID: 5 | Agent: Creative Agent]
📝 Sending request to Anthropic Claude
📊 Data: {
  model: "claude-3-5-sonnet-20241022",
  maxTokens: 1500,
  temperature: 0.8,
  promptLength: 450,
  estimatedTokens: 113
}
```

## Тестирование

### Тестовый скрипт: `src/scripts/test-agent-settings-usage.ts`

Проверяет:
- ✅ Извлечение параметров из настроек агента
- ✅ Fallback на дефолтные значения
- ✅ Обработка edge cases
- ✅ Анализ стоимости и производительности
- ✅ Рекомендации по настройкам

### Запуск тестов:
```bash
npx tsx src/scripts/test-agent-settings-usage.ts
```

## Стоимость и производительность

### Анализ стоимости (за 1M токенов):

| Модель | Input | Output | Применение |
|--------|-------|--------|------------|
| Haiku | $0.25 | $1.25 | Массовые рассылки |
| Sonnet | $3.00 | $15.00 | Стандартные задачи |
| Opus | $15.00 | $75.00 | Премиум качество |
| Sonnet 3.5 | $3.00 | $15.00 | Лучший баланс |

### Оценка для 1000 WhatsApp сообщений:
- **Haiku**: ~$0.20
- **Sonnet**: ~$2.40
- **Opus**: ~$12.00
- **Sonnet 3.5**: ~$2.40

## Рекомендации

### 1. Выбор модели по сценарию:

**Массовые рассылки** → Haiku
**Персонализированные сообщения** → Sonnet
**Премиум клиенты** → Opus или Sonnet 3.5

### 2. Настройка температуры:

**B2B формальные** → 0.1-0.3
**B2C дружелюбные** → 0.4-0.6
**Маркетинговые** → 0.7-0.9

### 3. Оптимизация токенов:

**WhatsApp** → 500-800 токенов
**Email** → 1000-2000 токенов
**Длинный контент** → 2000+ токенов

## Результат

✅ **Гибкость** - каждый агент может использовать свои параметры модели  
✅ **Производительность** - оптимизация под конкретные задачи  
✅ **Стоимость** - контроль расходов через выбор модели  
✅ **Качество** - настройка под требования к качеству  
✅ **Обратная совместимость** - fallback на дефолтные значения  

WhatsApp сервис теперь полностью использует настройки агентов для максимальной гибкости и эффективности!

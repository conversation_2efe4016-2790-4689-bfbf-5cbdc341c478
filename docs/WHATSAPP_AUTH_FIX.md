# Исправление ошибки 401 для WhatsApp API

## Проблема
При запросе к эндпоинту `/api/v1/settings/whatsapp` на production сервере возникала ошибка 401 (Unauthorized), несмотря на наличие валидного session token в cookie.

## Анализ причин

### 1. Неправильная конфигурация NextAuth для production
- `NEXTAUTH_URL` был настроен на `http://localhost:3000` вместо `https://app.salesflow.team`
- Настройки cookies не учитывали особенности HTTPS и production домена

### 2. Проблемы с middleware
- Middleware защищал маршруты `/api/auth/*`, что мешало работе NextAuth
- Исправлено: убрана защита для маршрутов NextAuth

### 3. Отсутствие логирования для отладки
- Добавлено подробное логирование в API эндпоинты для диагностики проблем аутентификации

## Решение

### 1. Исправлена конфигурация NextAuth (`src/lib/auth.ts`)

```typescript
export const authOptions: AuthOptions = {
    // ... providers
    session: {
        strategy: "jwt",
        maxAge: 24 * 60 * 60, // 24 hours
    },
    cookies: {
        sessionToken: {
            name: process.env.NODE_ENV === 'production' 
                ? '__Secure-next-auth.session-token' 
                : 'next-auth.session-token',
            options: {
                httpOnly: true,
                sameSite: 'lax',
                path: '/',
                secure: process.env.NODE_ENV === 'production',
                // Не устанавливаем domain для production
            },
        },
    },
    debug: process.env.NODE_ENV === 'development',
    // ... callbacks
};
```

### 2. Исправлен middleware (`src/middleware.ts`)

```typescript
export const config = {
    matcher: [
        // НЕ защищаем /api/auth/* - это маршруты NextAuth
        "/api/emails/:path*",
        "/api/leads/:path*",
        // ... остальные маршруты
        "/api/v1/settings/:path*",
        // ...
    ]
};
```

### 3. Добавлено логирование в WhatsApp API

```typescript
export async function GET(request: NextRequest) {
    try {
        console.log("WhatsApp API GET: Starting authentication check");
        
        const cookies = request.headers.get('cookie');
        console.log("WhatsApp API GET: Cookies received:", cookies ? 'present' : 'none');
        
        const session = await getServerSession(authOptions);
        console.log("WhatsApp API GET: Session data:", {
            hasSession: !!session,
            hasUser: !!session?.user,
            hasEmail: !!session?.user?.email,
            email: session?.user?.email
        });
        
        if (!session?.user?.email) {
            console.log("WhatsApp API GET: Authentication failed");
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }
        // ...
    }
}
```

### 4. Создан debug эндпоинт (`src/app/api/v1/auth/debug/route.ts`)

Эндпоинт для диагностики проблем аутентификации:
- Логирует все заголовки и cookies
- Проверяет наличие session tokens
- Выводит информацию о сессии и переменных окружения

### 5. Настроены переменные окружения для production

Создан файл `.env.production` с правильными настройками:
```bash
NEXTAUTH_URL=https://app.salesflow.team
NEXTAUTH_SECRET=zKfZEWS4pl4fO9naeAFfpB7awu2/iLwkWO06EbaEduM=
NEXT_PUBLIC_APP_URL=https://app.salesflow.team
```

## Проверка решения

### 1. Локальная проверка
```bash
# Запуск сервера разработки
npx next dev

# Тест debug эндпоинта
curl http://localhost:3001/api/v1/auth/debug

# Тест WhatsApp API (должен вернуть 307 redirect на /login)
curl http://localhost:3001/api/v1/settings/whatsapp
```

### 2. Production проверка
```bash
# Тест с валидным session token
curl -H "Cookie: __Secure-next-auth.session-token=VALID_TOKEN" \
     https://app.salesflow.team/api/v1/settings/whatsapp
```

## Дополнительные рекомендации

### 1. Мониторинг
- Добавить мониторинг ошибок аутентификации
- Настроить алерты для частых 401 ошибок

### 2. Безопасность
- Регулярно ротировать `NEXTAUTH_SECRET`
- Мониторить подозрительную активность

### 3. Отладка
- Использовать debug эндпоинт для диагностики проблем
- Проверять логи сервера при проблемах с аутентификацией

## Заключение

Проблема была вызвана неправильной конфигурацией NextAuth для production окружения. После исправления настроек cookies, переменных окружения и middleware, аутентификация должна работать корректно.

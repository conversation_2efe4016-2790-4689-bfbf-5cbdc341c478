# Исправление ошибки 401 для WhatsApp API

## Проблема
При запросе к эндпоинту `/api/v1/settings/whatsapp` на production сервере возникала ошибка 401 (Unauthorized), несмотря на наличие валидного session token в cookie.

Из production логов:
```
WhatsApp API GET: Session data: { hasSession: true, hasUser: true, hasEmail: false, email: undefined }
WhatsApp API GET: Authentication failed - no session or email
```

## Анализ причин

### 1. Основная проблема: Отсутствие email в сессии
Сессия создавалась успешно, но email не передавался из JWT token в session object.

### 2. Архитектурная проблема: Несогласованность схем БД
- **whatsappConfigs**: `createdBy` ссылается на `users.email` (varchar)
- **emailConfigs**: `createdBy` ссылается на `users.id` (integer)
- Другие таблицы: используют либо `users.id`, либо не имеют FK

### 3. Неправильный подход к аутентификации
- Использовался `getServerSession(authOptions)` вместо `auth()`
- Проверялся `session?.user?.email` вместо `session?.user`

## Решение

### 1. Миграция схемы базы данных

**Проблема**: `whatsappConfigs.createdBy` ссылался на `users.email`, что создавало проблемы с аутентификацией.

**Решение**: Изменить схему для использования `users.id` как в других таблицах.

#### Миграция SQL (`src/db/migrations/0004_migrate_whatsapp_configs_created_by.sql`):
```sql
-- Add new column created_by_id (integer)
ALTER TABLE whatsapp_configs ADD COLUMN created_by_id INTEGER;

-- Populate with user IDs based on existing emails
UPDATE whatsapp_configs
SET created_by_id = users.id
FROM users
WHERE whatsapp_configs.created_by = users.email;

-- Make NOT NULL and add foreign key
ALTER TABLE whatsapp_configs ALTER COLUMN created_by_id SET NOT NULL;
ALTER TABLE whatsapp_configs ADD CONSTRAINT whatsapp_configs_created_by_id_users_id_fk
FOREIGN KEY (created_by_id) REFERENCES users(id) ON DELETE CASCADE;

-- Drop old column and rename new one
ALTER TABLE whatsapp_configs DROP CONSTRAINT whatsapp_configs_created_by_users_email_fk;
ALTER TABLE whatsapp_configs DROP COLUMN created_by;
ALTER TABLE whatsapp_configs RENAME COLUMN created_by_id TO created_by;
```

#### Обновленная схема TypeScript (`src/db/schema/whatsapp-configs.ts`):
```typescript
createdBy: integer("created_by")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
```

### 2. Исправлен подход к аутентификации

Используется `auth()` вместо `getServerSession(authOptions)` как в других API:

```typescript
// Было:
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
const session = await getServerSession(authOptions);
if (!session?.user?.email) { ... }

// Стало:
import { auth } from "@/lib/auth";
const session = await auth();
if (!session?.user) { ... }
```

### 3. Упрощена логика аутентификации

```typescript
// Get user ID from session
if (!session.user.id) {
    return NextResponse.json({ error: "User ID not found" }, { status: 401 });
}

const userId = parseInt(session.user.id, 10);
if (isNaN(userId)) {
    return NextResponse.json({ error: "Invalid user ID" }, { status: 401 });
}

// Use userId directly in database queries
const configs = await db
    .select()
    .from(whatsappConfigs)
    .where(eq(whatsappConfigs.createdBy, userId));
```

```typescript
export const authOptions: AuthOptions = {
    // ... providers
    session: {
        strategy: "jwt",
        maxAge: 24 * 60 * 60, // 24 hours
    },
    cookies: {
        sessionToken: {
            name: process.env.NODE_ENV === 'production' 
                ? '__Secure-next-auth.session-token' 
                : 'next-auth.session-token',
            options: {
                httpOnly: true,
                sameSite: 'lax',
                path: '/',
                secure: process.env.NODE_ENV === 'production',
                // Не устанавливаем domain для production
            },
        },
    },
    debug: process.env.NODE_ENV === 'development',
    // ... callbacks
};
```

### 2. Исправлен middleware (`src/middleware.ts`)

```typescript
export const config = {
    matcher: [
        // НЕ защищаем /api/auth/* - это маршруты NextAuth
        "/api/emails/:path*",
        "/api/leads/:path*",
        // ... остальные маршруты
        "/api/v1/settings/:path*",
        // ...
    ]
};
```

### 3. Добавлено логирование в WhatsApp API

```typescript
export async function GET(request: NextRequest) {
    try {
        console.log("WhatsApp API GET: Starting authentication check");
        
        const cookies = request.headers.get('cookie');
        console.log("WhatsApp API GET: Cookies received:", cookies ? 'present' : 'none');
        
        const session = await getServerSession(authOptions);
        console.log("WhatsApp API GET: Session data:", {
            hasSession: !!session,
            hasUser: !!session?.user,
            hasEmail: !!session?.user?.email,
            email: session?.user?.email
        });
        
        if (!session?.user?.email) {
            console.log("WhatsApp API GET: Authentication failed");
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }
        // ...
    }
}
```

### 4. Создан debug эндпоинт (`src/app/api/v1/auth/debug/route.ts`)

Эндпоинт для диагностики проблем аутентификации:
- Логирует все заголовки и cookies
- Проверяет наличие session tokens
- Выводит информацию о сессии и переменных окружения

### 5. Настроены переменные окружения для production

Создан файл `.env.production` с правильными настройками:
```bash
NEXTAUTH_URL=https://app.salesflow.team
NEXTAUTH_SECRET=zKfZEWS4pl4fO9naeAFfpB7awu2/iLwkWO06EbaEduM=
NEXT_PUBLIC_APP_URL=https://app.salesflow.team
```

## Применение миграции

### 1. Подготовка
```bash
# Создать резервную копию базы данных
pg_dump $DATABASE_URL > backup_before_whatsapp_migration.sql

# Убедиться, что все изменения кода применены
git add .
git commit -m "Fix WhatsApp API authentication - migrate to users.id"
```

### 2. Применение миграции
```bash
# Запустить миграцию
node scripts/migrate-whatsapp-configs.js

# Или применить SQL вручную
psql $DATABASE_URL < src/db/migrations/0004_migrate_whatsapp_configs_created_by.sql
```

### 3. Развертывание кода
```bash
# Развернуть обновленный код на production
# (используйте ваш процесс развертывания)
```

## Проверка решения

### 1. Проверка миграции
```bash
# Проверить схему таблицы
psql $DATABASE_URL -c "\d whatsapp_configs"

# Проверить данные
psql $DATABASE_URL -c "SELECT id, created_by FROM whatsapp_configs LIMIT 5;"
```

### 2. Тестирование API
```bash
# Тест с валидным session token
curl -H "Cookie: __Secure-next-auth.session-token=VALID_TOKEN" \
     https://app.salesflow.team/api/v1/settings/whatsapp

# Должен вернуть 200 OK с данными конфигурации
```

## Дополнительные рекомендации

### 1. Мониторинг
- Добавить мониторинг ошибок аутентификации
- Настроить алерты для частых 401 ошибок

### 2. Безопасность
- Регулярно ротировать `NEXTAUTH_SECRET`
- Мониторить подозрительную активность

### 3. Отладка
- Использовать debug эндпоинт для диагностики проблем
- Проверять логи сервера при проблемах с аутентификацией

## Заключение

Проблема была вызвана неправильной конфигурацией NextAuth для production окружения. После исправления настроек cookies, переменных окружения и middleware, аутентификация должна работать корректно.

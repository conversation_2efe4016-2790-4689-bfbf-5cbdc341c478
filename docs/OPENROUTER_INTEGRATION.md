# OpenRouter Integration for WhatsApp Service

## Обзор изменений

WhatsApp сервис успешно переведен с прямого подключения к Anthropic на использование OpenRouter API для максимальной гибкости и совместимости с существующей архитектурой проекта.

## Мотивация

### Проблемы прямого подключения к Anthropic:
- Жесткая привязка к одному провайдеру
- Отсутствие единообразия с остальной частью проекта
- Сложность переключения между моделями
- Ограниченные возможности мониторинга

### Преимущества OpenRouter:
- ✅ **Единый API** для доступа к множеству LLM провайдеров
- ✅ **Гибкость моделей** - легкое переключение между Claude, GPT и другими
- ✅ **Оптимизация стоимости** - сравнение цен разных провайдеров
- ✅ **Надежность** - fallback опции при недоступности провайдера
- ✅ **Стандартизация** - единый формат запросов/ответов
- ✅ **Мониторинг** - детальная статистика использования

## Архитектурные изменения

### 1. Удаление прямой зависимости от Anthropic

**БЫЛО:**
```typescript
import Anthropic from "@anthropic-ai/sdk";

const anthropic = new Anthropic({
  apiKey: process.env.NEXT_ANTHROPIC_API_KEY,
});

const message = await anthropic.messages.create({
  model: "claude-3-sonnet-20240229",
  max_tokens: 1000,
  temperature: 0.2,
  messages: [{ role: "user", content: prompt }],
});
```

**СТАЛО:**
```typescript
// OpenRouter API configuration
const OPENROUTER_API = {
  URL: 'https://openrouter.ai/api/v1/chat/completions',
  MODELS: {
    HAIKU: 'anthropic/claude-3.5-haiku-20241022',
    SONNET: 'anthropic/claude-3-sonnet-20240229',
    SONNET_35: 'anthropic/claude-3.5-sonnet-20241022',
    OPUS: 'anthropic/claude-3-opus-20240229',
  } as const,
} as const;

const openRouterResponse = await makeOpenRouterRequest(
  prompt,
  llmParams.model,
  llmParams.maxTokens,
  llmParams.temperature
);
```

### 2. Функция для вызова OpenRouter API

```typescript
async function makeOpenRouterRequest(
  prompt: string,
  model: string,
  maxTokens: number,
  temperature: number
): Promise<OpenRouterResponse> {
  const apiKey = process.env.NEXT_OPENROUTER_API_KEY;
  
  if (!apiKey) {
    throw new Error('NEXT_OPENROUTER_API_KEY environment variable is not set');
  }

  const response = await fetch(OPENROUTER_API.URL, {
    method: "POST",
    headers: {
      "Authorization": `Bearer ${apiKey}`,
      "HTTP-Referer": process.env.NEXTAUTH_URL || 'http://localhost:3000',
      "X-Title": 'SalesFlow WhatsApp Generator',
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      model: model,
      messages: [{ role: "user", content: prompt }],
      max_tokens: maxTokens,
      temperature: temperature
    })
  });

  if (!response.ok) {
    const errorData = await response.text();
    throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${errorData}`);
  }

  return response.json();
}
```

### 3. Маппинг моделей

Автоматическое преобразование legacy названий моделей в формат OpenRouter:

```typescript
// Map model names to OpenRouter format if needed
let openRouterModel = agentSettings.model || OPENROUTER_API.MODELS.SONNET;

// If the model doesn't start with a provider prefix, assume it's an Anthropic model
if (!openRouterModel.includes('/')) {
  const modelMapping: Record<string, string> = {
    'claude-3-haiku-20240307': OPENROUTER_API.MODELS.HAIKU,
    'claude-3-sonnet-20240229': OPENROUTER_API.MODELS.SONNET,
    'claude-3.5-sonnet-20241022': OPENROUTER_API.MODELS.SONNET_35,
    'claude-3-opus-20240229': OPENROUTER_API.MODELS.OPUS,
  };
  openRouterModel = modelMapping[openRouterModel] || OPENROUTER_API.MODELS.SONNET;
}
```

### 4. Обработка ответов

**Anthropic формат:**
```typescript
const content = getTextFromContent(message.content);
const usage = {
  input_tokens: message.usage?.input_tokens,
  output_tokens: message.usage?.output_tokens,
  total_tokens: (message.usage?.input_tokens || 0) + (message.usage?.output_tokens || 0)
};
```

**OpenRouter формат:**
```typescript
const generatedMessage = openRouterResponse.choices[0]?.message?.content;
const usage = {
  input_tokens: openRouterResponse.usage?.prompt_tokens,
  output_tokens: openRouterResponse.usage?.completion_tokens,
  total_tokens: openRouterResponse.usage?.total_tokens
};
```

## Поддерживаемые модели

### Anthropic Claude (через OpenRouter):
- **anthropic/claude-3.5-haiku-20241022** - Быстрая, экономичная
- **anthropic/claude-3-sonnet-20240229** - Сбалансированная (дефолт)
- **anthropic/claude-3.5-sonnet-20241022** - Новейшая Sonnet
- **anthropic/claude-3-opus-20240229** - Максимальное качество

### Возможность расширения:
- **openai/gpt-4** - GPT-4 от OpenAI
- **openai/gpt-3.5-turbo** - GPT-3.5 Turbo
- **meta-llama/llama-2-70b-chat** - Llama 2
- **google/palm-2-chat-bison** - PaLM 2
- И многие другие модели через OpenRouter

## Обратная совместимость

### Автоматическое маппинг legacy моделей:
```typescript
// Эти форматы автоматически преобразуются:
"claude-3-haiku-20240307" → "anthropic/claude-3.5-haiku-20241022"
"claude-3-sonnet-20240229" → "anthropic/claude-3-sonnet-20240229"
"claude-3.5-sonnet-20241022" → "anthropic/claude-3.5-sonnet-20241022"
"claude-3-opus-20240229" → "anthropic/claude-3-opus-20240229"
```

### Существующие агенты продолжают работать:
- Агенты с legacy названиями моделей автоматически маппятся
- Агенты с OpenRouter форматом используются как есть
- Fallback на дефолтную модель при неизвестных названиях

## Конфигурация

### Переменные окружения:

**Новые (обязательные):**
```bash
NEXT_OPENROUTER_API_KEY=your_openrouter_api_key_here
```

**Существующие (используются):**
```bash
NEXTAUTH_URL=http://localhost:3000  # Для HTTP-Referer header
```

**Удаленные:**
```bash
# NEXT_ANTHROPIC_API_KEY - больше не нужен
```

### Получение API ключа OpenRouter:
1. Зарегистрируйтесь на https://openrouter.ai
2. Создайте API ключ в настройках
3. Добавьте ключ в переменные окружения

## Мониторинг и логирование

### Обновленное логирование:
```typescript
// Логирование запроса к OpenRouter
WhatsAppLogger.logLLMRequest(logContext, {
  model: llmParams.model,  // Теперь в формате OpenRouter
  maxTokens: llmParams.maxTokens,
  temperature: llmParams.temperature,
  promptLength: llmParams.promptLength
});

// Логирование ответа от OpenRouter
WhatsAppLogger.logLLMResponse(logContext, {
  content: generatedMessage,
  usage: {
    input_tokens: openRouterResponse.usage?.prompt_tokens,
    output_tokens: openRouterResponse.usage?.completion_tokens,
    total_tokens: openRouterResponse.usage?.total_tokens
  },
  executionTimeMs: llmExecutionTime
});
```

### Метрики для мониторинга:
- Использование разных моделей
- Стоимость по провайдерам
- Время ответа по моделям
- Частота ошибок по провайдерам

## Обработка ошибок

### Типичные ошибки OpenRouter:
- **401 Unauthorized** - Неверный API ключ
- **402 Payment Required** - Недостаточно средств
- **429 Too Many Requests** - Превышен лимит запросов
- **503 Service Unavailable** - Провайдер недоступен

### Обработка в коде:
```typescript
if (!response.ok) {
  const errorData = await response.text();
  throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${errorData}`);
}
```

## Тестирование

### Тестовый скрипт: `src/scripts/test-openrouter-integration.ts`

Проверяет:
- ✅ Конфигурацию API
- ✅ Маппинг моделей
- ✅ Формат запросов
- ✅ Обработку ответов
- ✅ Извлечение статистики токенов
- ✅ Обработку ошибок

### Запуск тестов:
```bash
npx tsx src/scripts/test-openrouter-integration.ts
```

## Миграция

### Шаги миграции:
1. ✅ **Удален импорт Anthropic SDK**
2. ✅ **Добавлена конфигурация OpenRouter**
3. ✅ **Создана функция makeOpenRouterRequest**
4. ✅ **Обновлена обработка ответов**
5. ✅ **Добавлен маппинг моделей**
6. ✅ **Обновлено логирование**

### Проверка миграции:
- Существующие агенты работают без изменений
- Новые модели доступны через OpenRouter формат
- Логирование показывает правильные модели
- Статистика токенов корректна

## Преимущества реализации

### 1. **Единообразие архитектуры**
- Соответствие паттернам, используемым в других частях проекта
- Единый подход к работе с LLM API

### 2. **Гибкость провайдеров**
- Легкое переключение между Claude, GPT, Llama
- Возможность A/B тестирования разных моделей
- Fallback на другие провайдеры при сбоях

### 3. **Оптимизация стоимости**
- Сравнение цен разных провайдеров
- Выбор оптимальной модели для каждой задачи
- Централизованный мониторинг расходов

### 4. **Масштабируемость**
- Поддержка новых моделей без изменения кода
- Автоматическое управление rate limiting
- Встроенная аналитика использования

## Результат

✅ **Интеграция завершена** - WhatsApp сервис использует OpenRouter  
✅ **Обратная совместимость** - существующие агенты продолжают работать  
✅ **Гибкость моделей** - поддержка множества LLM провайдеров  
✅ **Единообразие** - соответствие архитектуре проекта  
✅ **Мониторинг** - детальное логирование и метрики  

WhatsApp сервис теперь использует OpenRouter для максимальной гибкости и совместимости!

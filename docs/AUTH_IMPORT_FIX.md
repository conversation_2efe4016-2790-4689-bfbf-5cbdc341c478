# Auth Import Fix

## Проблема

При компиляции возникала ошибка:

```
Module not found: Can't resolve '@/auth'
./src/app/api/whatsapp/route.ts:2:0
> 2 | import { auth } from "@/auth";
```

## Причина

В WhatsApp API файлах использовался неправильный путь импорта для функции авторизации. Пытались импортировать из `@/auth`, но такого файла не существует.

## Анализ существующих импортов

В проекте используются два разных подхода для авторизации в API endpoints:

### 1. Использование `auth()` из `@/lib/auth`
```typescript
// Файл: src/lib/auth.ts
export const auth = () => getServerSession(authOptions);

// Использование:
import { auth } from "@/lib/auth";
const session = await auth();
```

### 2. Использование `getServerSession(authOptions)`
```typescript
// Импорты:
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

// Использование:
const session = await getServerSession(authOptions);
```

## Решение

### Исправлены импорты в файлах WhatsApp API:

#### 1. `src/app/api/whatsapp/route.ts`
**Было:**
```typescript
import { auth } from "@/auth";
```

**Стало:**
```typescript
import { auth } from "@/lib/auth";
```

#### 2. `src/app/api/whatsapp/send/route.ts`
**Было:**
```typescript
import { auth } from "@/auth";
```

**Стало:**
```typescript
import { auth } from "@/lib/auth";
```

#### 3. `src/app/api/v1/settings/whatsapp/route.ts`
**Добавлено:**
```typescript
import { authOptions } from "@/lib/auth";
```

**Обновлено:**
```typescript
// Было:
const session = await getServerSession();

// Стало:
const session = await getServerSession(authOptions);
```

#### 4. `src/app/api/v1/settings/whatsapp/[id]/route.ts`
**Добавлено:**
```typescript
import { authOptions } from "@/lib/auth";
```

**Обновлено все вызовы:**
```typescript
// Было:
const session = await getServerSession();

// Стало:
const session = await getServerSession(authOptions);
```

## Паттерны авторизации в проекте

### Когда использовать `auth()`:
- Простые случаи, когда нужна только сессия
- Когда не требуется дополнительная конфигурация
- Краткий синтаксис

```typescript
import { auth } from "@/lib/auth";

export async function POST(request: NextRequest) {
    const session = await auth();
    if (!session?.user?.email) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // ...
}
```

### Когда использовать `getServerSession(authOptions)`:
- Когда нужен полный контроль над конфигурацией
- В сложных сценариях авторизации
- Когда требуется совместимость с другими частями приложения

```typescript
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

export async function GET(request: NextRequest) {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    // ...
}
```

## Результат

✅ **Ошибка исправлена** - все API endpoints компилируются без ошибок  
✅ **Консистентность** - используются правильные паттерны авторизации  
✅ **Функциональность сохранена** - авторизация работает как прежде  

## Файлы изменены

- `src/app/api/whatsapp/route.ts`
- `src/app/api/whatsapp/send/route.ts`
- `src/app/api/v1/settings/whatsapp/route.ts`
- `src/app/api/v1/settings/whatsapp/[id]/route.ts`

## Рекомендации для будущего

### 1. Стандартизация импортов
Рекомендуется использовать единый подход во всем проекте:

```typescript
// Предпочтительный вариант для новых API endpoints
import { auth } from "@/lib/auth";
const session = await auth();
```

### 2. Проверка существующих паттернов
Перед созданием новых API endpoints всегда проверяйте, как реализована авторизация в существующих файлах:

```bash
# Поиск паттернов авторизации
grep -r "import.*auth" src/app/api/
grep -r "getServerSession" src/app/api/
```

### 3. Документирование паттернов
В файле `src/lib/auth.ts` добавлены комментарии для ясности:

```typescript
// Экспортируем функцию auth для удобного использования в API routes
export const auth = () => getServerSession(authOptions);

// Экспортируем authOptions для случаев, когда нужен полный контроль
export const authOptions: AuthOptions = {
    // ...
};
```

## Проверка исправления

После внесения изменений убедитесь, что:

1. **Компиляция проходит успешно**
2. **API endpoints отвечают корректно**
3. **Авторизация работает как ожидается**
4. **Нет TypeScript ошибок**

```bash
# Проверка компиляции
npm run build

# Проверка типов
npm run type-check
```

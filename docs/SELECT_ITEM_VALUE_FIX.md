# Select Item Value Fix

## Проблема

При нажатии на кнопку "Send WhatsApp" возникала ошибка:

```
Error: A <Select.Item /> must have a value prop that is not an empty string. 
This is because the Select value can be set to an empty string to clear the selection and show the placeholder.
```

## Причина

В компоненте `SendWhatsAppModal.tsx` использовался `SelectItem` с пустым значением:

```tsx
<SelectItem value="">Default Configuration</SelectItem>
```

Radix UI Select не позволяет использовать пустые строки как значения для `SelectItem`, так как пустая строка зарезервирована для очистки выбора и показа placeholder.

## Решение

### 1. Изменено значение для опции по умолчанию

**Было:**
```tsx
<SelectItem value="">Default Configuration</SelectItem>
```

**Стало:**
```tsx
<SelectItem value="default">Default Configuration</SelectItem>
```

### 2. Обновлено начальное состояние

**Было:**
```tsx
const [selectedConfig, setSelectedConfig] = useState<string>("");
```

**Стало:**
```tsx
const [selectedConfig, setSelectedConfig] = useState<string>("default");
```

### 3. Обновлена логика отправки

**Было:**
```tsx
whatsappConfigId: selectedConfig || undefined,
```

**Стало:**
```tsx
whatsappConfigId: selectedConfig === "default" ? undefined : selectedConfig,
```

## Результат

✅ **Ошибка исправлена** - модальное окно WhatsApp открывается без ошибок  
✅ **Функциональность сохранена** - опция "Default Configuration" работает как прежде  
✅ **Логика корректна** - при выборе "default" передается `undefined` в API  

## Файлы изменены

- `src/components/leads/SendWhatsAppModal.tsx`

## Рекомендации

При использовании Radix UI Select всегда убедитесь, что:

1. **Значения SelectItem не пустые строки**
2. **Используйте осмысленные значения** (например, "default", "none", "auto")
3. **Обрабатывайте специальные значения** в логике приложения

### Пример правильного использования:

```tsx
<Select value={selectedValue} onValueChange={setSelectedValue}>
  <SelectTrigger>
    <SelectValue placeholder="Choose option" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="auto">Auto</SelectItem>
    <SelectItem value="manual">Manual</SelectItem>
    <SelectItem value="disabled">Disabled</SelectItem>
  </SelectContent>
</Select>
```

### Обработка в логике:

```tsx
const processValue = (value: string) => {
  switch (value) {
    case "auto":
      return undefined; // Использовать автоматические настройки
    case "disabled":
      return null; // Отключить функцию
    default:
      return value; // Использовать конкретное значение
  }
};
```

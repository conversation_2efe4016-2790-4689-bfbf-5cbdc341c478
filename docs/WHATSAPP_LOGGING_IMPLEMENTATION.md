# WhatsApp Service Detailed Logging Implementation

## Обзор реализации

Успешно реализовано детальное логирование для WhatsApp сервиса, обеспечивающее полную прозрачность процесса генерации и отправки сообщений для отладки и мониторинга.

## Архитектура логирования

### 1. Утилитарный модуль (`src/utils/whatsapp-logger.ts`)

#### Основной класс: `WhatsAppLogger`
- **Статические методы** для различных типов логирования
- **Структурированный вывод** с префиксами и временными метками
- **Контекстная информация** для трассировки
- **Защита приватности** (маскирование телефонов)

#### Интерфейсы:
```typescript
interface LogContext {
  leadId: number;
  leadName?: string;
  leadCompany?: string;
  agentId?: number;
  agentName?: string;
  sessionId?: string;
}

interface LLMRequestParams {
  model: string;
  maxTokens: number;
  temperature: number;
  promptLength: number;
}

interface LLMResponse {
  content: string;
  usage?: TokenUsage;
  executionTimeMs: number;
}

interface MessageProcessing {
  rawResponse: string;
  processedContent: string;
  finalMessage: string;
  placeholdersReplaced: number;
}
```

## Типы логирования

### 1. **Логирование сессий**
```typescript
// Начало сессии
WhatsAppLogger.logSessionStart(logContext, "MESSAGE_GENERATION");

// Завершение сессии
WhatsAppLogger.logSessionEnd(logContext, "MESSAGE_GENERATION", true, 3200);
```

**Вывод:**
```
================================================================================
🚀 WhatsApp Service - MESSAGE_GENERATION SESSION STARTED
⏰ Timestamp: 2024-01-15T10:30:00.000Z
📋 Context: [Lead ID: 123 | Name: Иван Петров | Company: TechCorp | Agent ID: 5]
================================================================================
```

### 2. **Логирование промптов**
```typescript
WhatsAppLogger.logPromptProcessing(logContext, originalPrompt, finalPrompt, placeholdersFound);
```

**Что логируется:**
- Исходный промпт агента (до парсинга)
- Финальный промпт (после парсинга плейсхолдеров)
- Найденные плейсхолдеры
- Длина промптов

### 3. **Логирование LLM запросов**
```typescript
WhatsAppLogger.logLLMRequest(logContext, {
  model: "claude-3-sonnet-20240229",
  maxTokens: 1000,
  temperature: 0.2,
  promptLength: 1250
});
```

**Что логируется:**
- Параметры модели
- Время отправки запроса
- Размер промпта
- Оценка токенов

### 4. **Логирование LLM ответов**
```typescript
WhatsAppLogger.logLLMResponse(logContext, {
  content: "Generated message...",
  usage: { input_tokens: 245, output_tokens: 89, total_tokens: 334 },
  executionTimeMs: 1850
});
```

**Что логируется:**
- Полученный ответ от Claude
- Статистика токенов
- Время выполнения
- Скорость обработки (токены/сек)

### 5. **Логирование обработки сообщений**
```typescript
WhatsAppLogger.logMessageProcessing(logContext, {
  rawResponse: "Message: Hello...",
  processedContent: "Hello...",
  finalMessage: "Привет Иван...",
  placeholdersReplaced: 7
});
```

**Что логируется:**
- Сырой ответ от LLM
- Обработанный контент
- Финальное сообщение
- Количество замененных плейсхолдеров

### 6. **Логирование валидации телефонов**
```typescript
WhatsAppLogger.logPhoneValidation(logContext, "79115576367", {
  isValid: true,
  formatted: "+79115576367"
});
```

**Что логируется:**
- Исходный номер (замаскированный)
- Результат валидации
- Отформатированный номер
- Ошибки валидации

### 7. **Логирование работы с агентами**
```typescript
WhatsAppLogger.logAgentRetrieval(logContext, 5, true, agentData);
```

**Что логируется:**
- ID агента
- Найден ли агент
- Данные агента (имя, длина промпта, настройки)

### 8. **Логирование операций с БД**
```typescript
WhatsAppLogger.logDatabaseOperation(logContext, "INSERT", "whatsappMessages", true, {
  messageId: 456,
  contentLength: 245
});
```

**Что логируется:**
- Тип операции (INSERT/UPDATE/SELECT)
- Таблица
- Успешность операции
- Дополнительные детали

### 9. **Логирование отправки сообщений**
```typescript
// Попытка отправки
WhatsAppLogger.logSendingAttempt(logContext, messageId, phoneNumber, configId);

// Успешная отправка
WhatsAppLogger.logSendingSuccess(logContext, messageId, phoneNumber, apiResponse);

// Ошибка отправки
WhatsAppLogger.logSendingError(logContext, messageId, phoneNumber, error, apiResponse);
```

**Что логируется:**
- ID сообщения
- Номер телефона (замаскированный)
- Конфигурация WhatsApp API
- Ответ от API
- Детали ошибок

### 10. **Логирование ошибок**
```typescript
WhatsAppLogger.logGenerationError(logContext, error, "llm_request");
```

**Что логируется:**
- Контекст ошибки
- Сообщение об ошибке
- Стек вызовов (первые 3 строки)
- Этап, на котором произошла ошибка

## Интеграция в WhatsApp сервис

### Метод `generatePersonalizedMessage`:

1. **Начало сессии** - создание контекста и старт логирования
2. **Валидация телефона** - логирование результатов валидации
3. **Получение агента** - логирование поиска и данных агента
4. **Обработка промпта** - логирование парсинга плейсхолдеров
5. **LLM запрос** - логирование параметров и времени запроса
6. **LLM ответ** - логирование ответа и статистики токенов
7. **Обработка сообщения** - логирование финальной обработки
8. **Сохранение в БД** - логирование операций с базой данных
9. **Завершение сессии** - логирование итогов и времени выполнения

### Метод `sendMessage`:

1. **Начало сессии** - создание контекста для отправки
2. **Попытка отправки** - логирование параметров отправки
3. **Результат отправки** - логирование успеха или ошибки
4. **Обновление БД** - логирование изменений статуса
5. **Завершение сессии** - логирование итогов отправки

## Формат логов

### Структура лога:
```
🤖 [WhatsApp] PREFIX - TIMESTAMP
📋 Context: [Lead ID: 123 | Name: Иван Петров | Company: TechCorp]
📝 MESSAGE
📊 Data: { structured_data }
📄 Content: truncated_content
```

### Префиксы:
- `PROMPT_PROCESSING` - обработка промптов
- `LLM_REQUEST` - запросы к LLM
- `LLM_RESPONSE` - ответы от LLM
- `MESSAGE_PROCESSING` - обработка сообщений
- `PHONE_VALIDATION` - валидация телефонов
- `AGENT_RETRIEVAL` - получение агентов
- `DATABASE_OPERATION` - операции с БД
- `SENDING_ATTEMPT` - попытки отправки
- `SENDING_SUCCESS` - успешная отправка
- `SENDING_ERROR` - ошибки отправки
- `GENERATION_SUCCESS` - успешная генерация
- `GENERATION_ERROR` - ошибки генерации

## Защита приватности

### Маскирование телефонов:
```typescript
// Исходный: 79115576367
// Замаскированный: 79*******67

// Исходный: +1234567890  
// Замаскированный: +1*******90
```

### Безопасность:
- ✅ API токены не логируются
- ✅ Телефоны маскируются
- ✅ Чувствительные данные скрываются
- ✅ Контент обрезается для читаемости

## Преимущества реализации

### 1. **Полная трассировка**
- Каждое сообщение имеет уникальный session ID
- Полный жизненный цикл от генерации до отправки
- Связь между всеми этапами процесса

### 2. **Производительность**
- Измерение времени выполнения каждого этапа
- Статистика использования токенов
- Скорость обработки LLM запросов

### 3. **Отладка**
- Детальная информация об ошибках
- Стек вызовов для быстрого поиска проблем
- Контекст выполнения для каждой операции

### 4. **Мониторинг**
- Структурированные логи для парсинга
- Метрики для дашбордов
- Алерты на основе паттернов ошибок

### 5. **Аналитика**
- Использование агентов
- Эффективность плейсхолдеров
- Статистика отправки сообщений

## Использование в продакшене

### Фильтрация логов:
```bash
# Все логи WhatsApp сервиса
grep "🤖 \[WhatsApp\]" app.log

# Только ошибки
grep "GENERATION_ERROR\|SENDING_ERROR" app.log

# Конкретный лид
grep "Lead ID: 123" app.log

# LLM производительность
grep "LLM_RESPONSE" app.log
```

### Мониторинг метрик:
- Среднее время генерации сообщений
- Успешность отправки сообщений
- Использование токенов по агентам
- Частота ошибок по типам

## Результат

✅ **Полная прозрачность** - каждый этап процесса логируется  
✅ **Структурированные логи** - легко парсятся и анализируются  
✅ **Защита приватности** - чувствительные данные маскируются  
✅ **Производительность** - детальные метрики времени выполнения  
✅ **Отладка** - полная информация для решения проблем  
✅ **Мониторинг** - готовые данные для дашбордов и алертов  

WhatsApp сервис теперь обеспечивает полную наблюдаемость процесса генерации и отправки сообщений!

## Тестирование

### Тестовый скрипт: `src/scripts/test-whatsapp-logging.ts`

Проверяет все типы логирования:
- ✅ Сессии (старт/завершение)
- ✅ Обработка промптов
- ✅ LLM запросы и ответы
- ✅ Обработка сообщений
- ✅ Валидация телефонов
- ✅ Работа с агентами
- ✅ Операции с БД
- ✅ Отправка сообщений
- ✅ Обработка ошибок

### Запуск тестов:
```bash
npx tsx src/scripts/test-whatsapp-logging.ts
```

## Примеры логов в продакшене

### Успешная генерация сообщения:
```
================================================================================
🚀 WhatsApp Service - MESSAGE_GENERATION SESSION STARTED
⏰ Timestamp: 2024-01-15T10:30:00.000Z
📋 Context: [Lead ID: 123 | Name: Иван Петров | Company: TechCorp | Agent ID: 5]
================================================================================

🤖 [WhatsApp] PHONE_VALIDATION - 2024-01-15T10:30:00.100Z
📋 Context: [Lead ID: 123 | Name: Иван Петров | Company: TechCorp | Agent ID: 5]
📝 Validating phone number
📊 Data: { originalPhone: "79*******67", isValid: true, formattedPhone: "+7*******67" }

🤖 [WhatsApp] AGENT_RETRIEVAL - 2024-01-15T10:30:00.150Z
📋 Context: [Lead ID: 123 | Name: Иван Петров | Company: TechCorp | Agent ID: 5]
📝 Retrieving agent data
📊 Data: { agentId: 5, agentFound: true, agentName: "WhatsApp Sales Agent", agentPromptLength: 450 }

🤖 [WhatsApp] PROMPT_PROCESSING - 2024-01-15T10:30:00.200Z
📋 Context: [Lead ID: 123 | Name: Иван Петров | Company: TechCorp | Agent ID: 5]
📝 Processing prompt for LLM request
📊 Data: { originalPromptLength: 450, finalPromptLength: 380, placeholdersFound: 7 }

🤖 [WhatsApp] LLM_REQUEST - 2024-01-15T10:30:00.250Z
📋 Context: [Lead ID: 123 | Name: Иван Петров | Company: TechCorp | Agent ID: 5]
📝 Sending request to Anthropic Claude
📊 Data: { model: "claude-3-sonnet-20240229", maxTokens: 1000, temperature: 0.2, estimatedTokens: 95 }

🤖 [WhatsApp] LLM_RESPONSE - 2024-01-15T10:30:02.100Z
📋 Context: [Lead ID: 123 | Name: Иван Петров | Company: TechCorp | Agent ID: 5]
📝 Received response from Anthropic Claude
📊 Data: { executionTimeMs: 1850, contentLength: 245, usage: { input_tokens: 245, output_tokens: 89, total_tokens: 334 }, tokensPerSecond: 180 }

🤖 [WhatsApp] MESSAGE_PROCESSING - 2024-01-15T10:30:02.150Z
📋 Context: [Lead ID: 123 | Name: Иван Петров | Company: TechCorp | Agent ID: 5]
📝 Processing LLM response
📊 Data: { rawResponseLength: 245, processedContentLength: 230, finalMessageLength: 220, placeholdersReplaced: 0 }

🤖 [WhatsApp] GENERATION_SUCCESS - 2024-01-15T10:30:02.200Z
📋 Context: [Lead ID: 123 | Name: Иван Петров | Company: TechCorp | Agent ID: 5]
📝 WhatsApp message generated successfully
📊 Data: { messageId: 456, phoneNumber: "+7*******67", totalExecutionTimeMs: 2200 }

================================================================================
🏁 WhatsApp Service - MESSAGE_GENERATION SESSION COMPLETED
⏰ Timestamp: 2024-01-15T10:30:02.200Z
📋 Context: [Lead ID: 123 | Name: Иван Петров | Company: TechCorp | Agent ID: 5]
⏱️  Total Execution Time: 2200ms
✅ Status: SUCCESS
================================================================================
```

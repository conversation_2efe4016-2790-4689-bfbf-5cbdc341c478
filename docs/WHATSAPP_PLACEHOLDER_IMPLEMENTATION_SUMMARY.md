# WhatsApp Placeholder Parsing - Implementation Summary

## Проблема решена

**До реализации:** WhatsApp сообщения содержали непарсенные плейсхолдеры:
```
"Здравствуйте, {{leadFirstName}} {{leadLastName}}, 
Я заметил, что вы работаете в {{leadCompany}} и находитесь в городе {{details_city}}."
```

**После реализации:** Плейсхолдеры корректно заменяются на реальные данные:
```
"Здравствуйте, Иван Петров, 
Я заметил, что вы работаете в TechCorp и находитесь в городе Москва."
```

## Созданные файлы

### 1. Утилитарный модуль
- `src/utils/whatsapp-placeholder-parser.ts` - основная логика парсинга плейсхолдеров

### 2. Тестирование
- `src/scripts/test-whatsapp-placeholders.ts` - комплексные тесты функциональности

### 3. Документация
- `docs/WHATSAPP_PLACEHOLDER_PARSING.md` - подробная документация
- `docs/WHATSAPP_PLACEHOLDER_IMPLEMENTATION_SUMMARY.md` - краткое резюме

## Обновленные файлы

### 1. WhatsApp сервис (`src/services/whatsapp.ts`)
- Добавлены импорты парсера плейсхолдеров
- Интегрирован парсинг в процесс генерации сообщений
- Обновлен дефолтный промпт для использования новых плейсхолдеров

## Ключевые функции

### 1. `parseWhatsAppPlaceholders(template, context)`
- **Назначение:** Заменяет плейсхолдеры в шаблоне на реальные данные
- **Поддержка:** Базовые поля, JSON поля, вложенные данные
- **Обработка:** Graceful fallback для отсутствующих данных

### 2. `normalizeWhatsAppPromptVariables(prompt)`
- **Назначение:** Нормализует различные форматы плейсхолдеров
- **Примеры:** `{{lead.firstName}}` → `{{leadFirstName}}`
- **Совместимость:** Обеспечивает работу с legacy форматами

### 3. `detectUnknownWhatsAppPlaceholders(template)`
- **Назначение:** Обнаруживает неизвестные плейсхолдеры
- **Использование:** Отладка и валидация шаблонов

### 4. `validateWhatsAppTemplate(template)`
- **Назначение:** Комплексная валидация шаблонов
- **Возвращает:** Статус валидности, неизвестные плейсхолдеры, предупреждения

## Поддерживаемые плейсхолдеры

### Базовая информация (11 плейсхолдеров):
```
{{leadFirstName}}, {{leadLastName}}, {{leadFullName}}
{{leadCompany}}, {{leadTitle}}, {{leadEmail}}, {{leadPhone}}
{{leadIndustry}}, {{leadWebsite}}, {{leadStatus}}, {{leadDescription}}
```

### Информация об отправителе (3 плейсхолдера):
```
{{senderName}}, {{senderCompany}}, {{senderPosition}}
```

### JSON поля - Details (5 плейсхолдеров):
```
{{details_city}}, {{details_country}}, {{details_region}}
{{details_address}}, {{details_linkedin}}
```

### JSON поля - Communications (7 плейсхолдеров):
```
{{communications_linkedin}}, {{communications_email}}, {{communications_phone}}
{{communications_website}}, {{communications_telegram}}, {{communications_whatsapp}}
{{communications_vkontakte}}
```

### JSON поля - Company Info (6 плейсхолдеров):
```
{{company_info_name}}, {{company_info_description}}, {{company_info_industry}}
{{company_info_size}}, {{company_info_website}}, {{company_info_location}}
```

### Legacy поддержка (3 плейсхолдера):
```
{{lead_details}}, {{lead_history}}, {{company_info}}
```

**Итого: 35 поддерживаемых плейсхолдеров**

## Архитектурные принципы

### 1. Модульность
- ✅ Отдельный утилитарный модуль
- ✅ Четкое разделение ответственности
- ✅ Переиспользуемые функции

### 2. Обратная совместимость
- ✅ Поддержка legacy форматов
- ✅ Автоматическая нормализация
- ✅ Graceful fallback

### 3. Надежность
- ✅ Обработка отсутствующих данных
- ✅ Валидация шаблонов
- ✅ Обнаружение ошибок

### 4. Тестируемость
- ✅ Комплексные тесты
- ✅ Mock данные
- ✅ Edge cases

## Интеграция в WhatsApp сервис

### Процесс генерации сообщения:

1. **Получение промпта** (агент или дефолтный)
2. **Нормализация плейсхолдеров** (`normalizeWhatsAppPromptVariables`)
3. **Парсинг промпта** (`parseWhatsAppPlaceholders`)
4. **Генерация AI сообщения** (Anthropic Claude)
5. **Парсинг сгенерированного контента** (`parseWhatsAppPlaceholders`)
6. **Сохранение в базу данных**

### Контекст для парсинга:
```typescript
const whatsappContext: WhatsAppContext = {
  lead: leadData,
  senderName: "SalesFlow Team",
  senderCompany: "SalesFlow",
  senderPosition: "Sales Manager"
};
```

## Примеры использования

### 1. Простая персонализация:
```
Привет {{leadFirstName}}! 👋
Работаете в {{leadCompany}}?
{{senderName}}
```

### 2. Использование JSON полей:
```
{{leadFirstName}}, привет!
Нашел ваш LinkedIn: {{communications_linkedin}}
Вы из {{details_city}}? 
{{senderName}} из {{senderCompany}}
```

### 3. Обработка отсутствующих данных:
```
Здравствуйте{{#if leadFirstName}}, {{leadFirstName}}{{/if}}!
{{#if leadCompany}}Работаете в {{leadCompany}}? {{/if}}
```

## Тестирование

### Тестовые сценарии:
- ✅ Базовые плейсхолдеры
- ✅ JSON поля (вложенные данные)
- ✅ Нормализация форматов
- ✅ Отсутствующие данные
- ✅ Неизвестные плейсхолдеры
- ✅ Валидация шаблонов
- ✅ Комплексные вложенные структуры

### Запуск тестов:
```bash
npx tsx src/scripts/test-whatsapp-placeholders.ts
```

## Преимущества реализации

### 1. Персонализация
- Полная персонализация WhatsApp сообщений
- Использование всех доступных данных лида
- Гибкие шаблоны для разных сценариев

### 2. Совместимость
- Работа с существующими промптами агентов
- Поддержка legacy форматов
- Плавная миграция без breaking changes

### 3. Надежность
- Graceful обработка отсутствующих данных
- Валидация и отладка шаблонов
- Четкие сообщения об ошибках

### 4. Расширяемость
- Легкое добавление новых плейсхолдеров
- Поддержка сложных JSON структур
- Модульная архитектура

## Следующие шаги

### Возможные улучшения:
1. **Условная логика** - поддержка if/else в шаблонах
2. **Форматирование** - функции для форматирования дат, чисел
3. **Локализация** - поддержка разных языков
4. **Кэширование** - оптимизация производительности
5. **UI для шаблонов** - визуальный редактор плейсхолдеров

### Мониторинг:
- Отслеживание использования плейсхолдеров
- Статистика по персонализации
- Анализ эффективности шаблонов

## Результат

✅ **Проблема решена** - плейсхолдеры корректно парсятся в WhatsApp сообщениях  
✅ **35 плейсхолдеров поддерживается** - полный доступ к данным лидов  
✅ **Обратная совместимость** - существующие промпты продолжают работать  
✅ **Надежность** - graceful обработка всех edge cases  
✅ **Тестируемость** - комплексное покрытие тестами  

WhatsApp сообщения теперь генерируются с полностью персонализированным контентом на основе реальных данных лидов!

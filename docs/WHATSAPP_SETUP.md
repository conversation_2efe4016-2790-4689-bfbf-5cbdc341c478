# WhatsApp Integration Setup

Эта документация описывает настройку и использование функциональности отправки WhatsApp сообщений лидам.

## Обзор функциональности

Система WhatsApp интеграции позволяет:
- Генерировать персонализированные WhatsApp сообщения для лидов с использованием AI
- Отправлять сообщения через внешний WhatsApp API
- Валидировать номера телефонов
- Логировать отправленные сообщения
- Управлять конфигурациями WhatsApp API

## Настройка

### 1. Переменные окружения

Добавьте следующие переменные в ваш `.env` файл:

```env
# WhatsApp API Configuration
WHATSAPP_API_URL=https://your-whatsapp-api-url.com
WHATSAPP_PROFILE_ID=your_profile_id
WHATSAPP_TOKEN=your_whatsapp_token
```

### 2. База данных

Выполните миграцию для создания необходимых таблиц:

```bash
# Если используете drizzle-kit
npx drizzle-kit push

# Или выполните SQL миграцию напрямую
psql -d your_database -f drizzle/0003_create_whatsapp_tables.sql
```

Создаются следующие таблицы:
- `whatsapp_messages` - хранение сгенерированных сообщений
- `whatsapp_history` - история отправки сообщений
- `whatsapp_configs` - пользовательские конфигурации API
- `whatsapp_logs` - логи AI генерации сообщений

### 3. Внешний WhatsApp API

Система интегрируется с внешним WhatsApp API со следующими параметрами:

**Endpoint:** `POST {WHATSAPP_API_URL}/api/sync/message/send?profile_id={WHATSAPP_PROFILE_ID}`

**Headers:**
```
Authorization: {WHATSAPP_TOKEN}
Content-Type: application/json
```

**Body:**
```json
{
    "body": "Текст сообщения",
    "recipient": "79115576367"
}
```

## Использование

### 1. Интерфейс лидов

1. Перейдите на страницу лидов (`/leads`)
2. Выберите одного или несколько лидов с номерами телефонов
3. Нажмите кнопку "Send WhatsApp"
4. В модальном окне:
   - Выберите конфигурацию WhatsApp (опционально)
   - Нажмите "Generate Messages" для создания сообщений
   - Нажмите "Send Messages" для отправки

### 2. API Endpoints

#### Генерация сообщения
```
POST /api/whatsapp
Content-Type: application/json

{
    "leadId": 123
}
```

#### Отправка сообщения
```
POST /api/whatsapp/send
Content-Type: application/json

{
    "messageId": 456,
    "whatsappConfigId": "optional-config-id"
}
```

#### Управление конфигурациями
```
GET /api/v1/settings/whatsapp
POST /api/v1/settings/whatsapp
PUT /api/v1/settings/whatsapp/{id}
DELETE /api/v1/settings/whatsapp/{id}
```

### 3. Тестовые endpoints

#### Генерация тестового сообщения
```
POST /api/test/whatsapp/generate
Content-Type: application/json

{
    "salesforceId": "lead-sf-id",
    "prompt": "Custom prompt (optional)"
}
```

#### Отправка тестового сообщения
```
POST /api/test/whatsapp/send
Content-Type: application/json

{
    "messageId": 123,
    "whatsappConfigId": "optional"
}
```

## Валидация номеров телефонов

Система автоматически валидирует и форматирует российские мобильные номера:

- Принимает форматы: `79115576367`, `89115576367`, `9115576367`, `+79115576367`
- Нормализует к формату: `79115576367`
- Поддерживает номера с разделителями: `7 911 557 63 67`, `8 (911) 557-63-67`

## Настройки WhatsApp

Пользователи могут создавать собственные конфигурации WhatsApp API через интерфейс настроек:

1. Перейдите в настройки
2. Найдите секцию "WhatsApp Settings"
3. Добавьте новую конфигурацию с:
   - API URL
   - Profile ID
   - Token

## Логирование и мониторинг

Система логирует:
- Генерацию сообщений AI (в `whatsapp_logs`)
- Статус отправки сообщений (в `whatsapp_history`)
- Ошибки API и валидации

## Безопасность

- Токены WhatsApp API хранятся в зашифрованном виде
- Валидация всех входящих данных
- Проверка авторизации для всех endpoints
- Ограничение доступа к конфигурациям по пользователям

## Troubleshooting

### Ошибка "Lead does not have a phone number"
Убедитесь, что у лида заполнено поле `phone` в базе данных.

### Ошибка "Invalid phone number format"
Проверьте формат номера телефона. Поддерживаются только российские мобильные номера.

### Ошибка "WhatsApp API configuration is missing"
Проверьте переменные окружения или создайте пользовательскую конфигурацию.

### Ошибка "WhatsApp API error: 401"
Проверьте правильность токена авторизации.

## Пример использования

```typescript
import { WhatsAppService, validatePhoneNumber } from '@/services/whatsapp';

// Валидация номера
const phoneValidation = validatePhoneNumber("89115576367");
if (phoneValidation.isValid) {
    console.log("Formatted:", phoneValidation.formatted); // "79115576367"
}

// Генерация сообщения
const message = await WhatsAppService.generatePersonalizedMessage("sf-lead-id");

// Отправка сообщения
const [result] = await WhatsAppService.sendMessage(message.message_id);
console.log(result); // "WhatsApp message was successfully sent to 79115576367"
```

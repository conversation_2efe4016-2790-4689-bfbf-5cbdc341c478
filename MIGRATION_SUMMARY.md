# WhatsApp API Authentication Fix - Migration Summary

## Проблема
Ошибка 401 в WhatsApp API из-за отсутствия email в сессии пользователя.

## Корневая причина
Таблица `whatsappConfigs` использовала `users.email` как внешний ключ, в то время как другие таблицы используют `users.id`.

## Решение
Миграция схемы базы данных для использования `users.id` вместо `users.email`.

## Изменения

### 1. База данных
- **Файл**: `src/db/migrations/0004_migrate_whatsapp_configs_created_by.sql`
- **Изменение**: `whatsappConfigs.createdBy` теперь ссылается на `users.id` (integer)

### 2. Схема TypeScript
- **Файл**: `src/db/schema/whatsapp-configs.ts`
- **Изменение**: Тип поля изменен с `varchar` на `integer`

### 3. API эндпоинты
- **Файлы**: 
  - `src/app/api/v1/settings/whatsapp/route.ts`
  - `src/app/api/v1/settings/whatsapp/[id]/route.ts`
- **Изменения**:
  - Использование `auth()` вместо `getServerSession(authOptions)`
  - Проверка `session?.user` вместо `session?.user?.email`
  - Использование `session.user.id` для запросов к БД

### 4. Скрипты
- **Файл**: `scripts/migrate-whatsapp-configs.js`
- **Назначение**: Автоматическое применение миграции с проверками

## Инструкции по применению

### 1. Резервное копирование
```bash
pg_dump $DATABASE_URL > backup_before_whatsapp_migration.sql
```

### 2. Применение миграции
```bash
node scripts/migrate-whatsapp-configs.js
```

### 3. Развертывание кода
Развернуть обновленный код на production сервер.

### 4. Проверка
```bash
# Проверить API
curl -H "Cookie: __Secure-next-auth.session-token=VALID_TOKEN" \
     https://app.salesflow.team/api/v1/settings/whatsapp
```

## Преимущества

1. **Упрощенная аутентификация**: Не нужно получать email из БД
2. **Согласованность**: Все таблицы теперь используют `users.id`
3. **Производительность**: Меньше запросов к БД
4. **Надежность**: Использование числового ID вместо email

## Откат (если необходимо)

```sql
-- Добавить обратно email column
ALTER TABLE whatsapp_configs ADD COLUMN created_by_email VARCHAR(255);

-- Заполнить email из users таблицы
UPDATE whatsapp_configs 
SET created_by_email = users.email 
FROM users 
WHERE whatsapp_configs.created_by = users.id;

-- Удалить новый constraint и column
ALTER TABLE whatsapp_configs DROP CONSTRAINT whatsapp_configs_created_by_users_id_fk;
ALTER TABLE whatsapp_configs DROP COLUMN created_by;
ALTER TABLE whatsapp_configs RENAME COLUMN created_by_email TO created_by;

-- Добавить обратно email constraint
ALTER TABLE whatsapp_configs 
ADD CONSTRAINT whatsapp_configs_created_by_users_email_fk 
FOREIGN KEY (created_by) REFERENCES users(email);
```

## Статус
- [x] Миграция SQL создана
- [x] Схема TypeScript обновлена
- [x] API код обновлен
- [x] Скрипт миграции создан
- [x] Документация обновлена
- [ ] Миграция применена на production
- [ ] Код развернут на production
- [ ] Тестирование завершено

CREATE TABLE "email_logs" (
	"id" serial PRIMARY KEY NOT NULL,
	"email_id" integer NOT NULL,
	"raw_prompt" text NOT NULL,
	"formatted_prompt" text NOT NULL,
	"variables" jsonb NOT NULL,
	"ai_response" jsonb NOT NULL,
	"execution_time_ms" integer,
	"model_name" text,
	"prompt_tokens" integer,
	"completion_tokens" integer,
	"total_tokens" integer,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "emails" ADD COLUMN "created_by" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "email_logs" ADD CONSTRAINT "email_logs_email_id_emails_id_fk" FOREIGN KEY ("email_id") REFERENCES "public"."emails"("id") ON DELETE cascade ON UPDATE no action;
-- Create whatsapp_messages table
CREATE TABLE IF NOT EXISTS "whatsapp_messages" (
	"id" serial PRIMARY KEY NOT NULL,
	"lead_id" integer NOT NULL,
	"content" text NOT NULL,
	"prompt" text NOT NULL,
	"phone_number" varchar(20) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"created_by" varchar(255) DEFAULT '',
	"sent" boolean DEFAULT false NOT NULL,
	"sent_at" timestamp
);

-- Create whatsapp_history table
CREATE TABLE IF NOT EXISTS "whatsapp_history" (
	"id" serial PRIMARY KEY NOT NULL,
	"message_id" integer,
	"status" text NOT NULL,
	"sent_at" timestamp,
	"error" text,
	"created_at" timestamp DEFAULT now()
);

-- Create whatsapp_configs table
CREATE TABLE IF NOT EXISTS "whatsapp_configs" (
	"id" varchar(191) PRIMARY KEY NOT NULL,
	"api_url" text NOT NULL,
	"profile_id" text NOT NULL,
	"token" text NOT NULL,
	"created_by" varchar(255) NOT NULL,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Create whatsapp_logs table
CREATE TABLE IF NOT EXISTS "whatsapp_logs" (
	"id" serial PRIMARY KEY NOT NULL,
	"message_id" integer NOT NULL,
	"raw_prompt" text NOT NULL,
	"formatted_prompt" text NOT NULL,
	"variables" jsonb NOT NULL,
	"ai_response" jsonb NOT NULL,
	"execution_time_ms" integer,
	"model_name" text,
	"prompt_tokens" integer,
	"completion_tokens" integer,
	"total_tokens" integer,
	"created_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
ALTER TABLE "whatsapp_messages" ADD CONSTRAINT "whatsapp_messages_lead_id_leads_id_fk" FOREIGN KEY ("lead_id") REFERENCES "public"."leads"("id") ON DELETE cascade ON UPDATE no action;
ALTER TABLE "whatsapp_history" ADD CONSTRAINT "whatsapp_history_message_id_whatsapp_messages_id_fk" FOREIGN KEY ("message_id") REFERENCES "public"."whatsapp_messages"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "whatsapp_configs" ADD CONSTRAINT "whatsapp_configs_created_by_users_email_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("email") ON DELETE no action ON UPDATE no action;
ALTER TABLE "whatsapp_logs" ADD CONSTRAINT "whatsapp_logs_message_id_whatsapp_messages_id_fk" FOREIGN KEY ("message_id") REFERENCES "public"."whatsapp_messages"("id") ON DELETE cascade ON UPDATE no action;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_whatsapp_messages_lead_id" ON "whatsapp_messages"("lead_id");
CREATE INDEX IF NOT EXISTS "idx_whatsapp_messages_phone_number" ON "whatsapp_messages"("phone_number");
CREATE INDEX IF NOT EXISTS "idx_whatsapp_history_message_id" ON "whatsapp_history"("message_id");
CREATE INDEX IF NOT EXISTS "idx_whatsapp_logs_message_id" ON "whatsapp_logs"("message_id");
CREATE INDEX IF NOT EXISTS "idx_whatsapp_configs_created_by" ON "whatsapp_configs"("created_by");

{"id": "41111f7d-9eed-4bfc-aa18-b0fcd468ee55", "prevId": "5712fb1e-30d0-4a3a-9765-7a7d6d846ad6", "version": "7", "dialect": "postgresql", "tables": {"public.agents": {"name": "agents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "prompt_id": {"name": "prompt_id", "type": "integer", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"agents_prompt_id_prompts_id_fk": {"name": "agents_prompt_id_prompts_id_fk", "tableFrom": "agents", "tableTo": "prompts", "columnsFrom": ["prompt_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"agents_uuid_unique": {"name": "agents_uuid_unique", "nullsNotDistinct": false, "columns": ["uuid"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_tokens": {"name": "api_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_used_at": {"name": "last_used_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"api_tokens_user_id_users_id_fk": {"name": "api_tokens_user_id_users_id_fk", "tableFrom": "api_tokens", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"api_tokens_token_unique": {"name": "api_tokens_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.email_configs": {"name": "email_configs", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": true, "notNull": true}, "smtp": {"name": "smtp", "type": "text", "primaryKey": false, "notNull": true}, "port": {"name": "port", "type": "integer", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"email_configs_created_by_users_id_fk": {"name": "email_configs_created_by_users_id_fk", "tableFrom": "email_configs", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.email_logs": {"name": "email_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "email_id": {"name": "email_id", "type": "integer", "primaryKey": false, "notNull": true}, "raw_prompt": {"name": "raw_prompt", "type": "text", "primaryKey": false, "notNull": true}, "formatted_prompt": {"name": "formatted_prompt", "type": "text", "primaryKey": false, "notNull": true}, "variables": {"name": "variables", "type": "jsonb", "primaryKey": false, "notNull": true}, "ai_response": {"name": "ai_response", "type": "jsonb", "primaryKey": false, "notNull": true}, "execution_time_ms": {"name": "execution_time_ms", "type": "integer", "primaryKey": false, "notNull": false}, "model_name": {"name": "model_name", "type": "text", "primaryKey": false, "notNull": false}, "prompt_tokens": {"name": "prompt_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "completion_tokens": {"name": "completion_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "total_tokens": {"name": "total_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"email_logs_email_id_emails_id_fk": {"name": "email_logs_email_id_emails_id_fk", "tableFrom": "email_logs", "tableTo": "emails", "columnsFrom": ["email_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.email_history": {"name": "email_history", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "email_id": {"name": "email_id", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "sent_at": {"name": "sent_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"email_history_email_id_emails_id_fk": {"name": "email_history_email_id_emails_id_fk", "tableFrom": "email_history", "tableTo": "emails", "columnsFrom": ["email_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.emails": {"name": "emails", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "lead_id": {"name": "lead_id", "type": "integer", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "''"}, "sent": {"name": "sent", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "sent_at": {"name": "sent_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"emails_lead_id_leads_id_fk": {"name": "emails_lead_id_leads_id_fk", "tableFrom": "emails", "tableTo": "leads", "columnsFrom": ["lead_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.leads": {"name": "leads", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "salesforce_id": {"name": "salesforce_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "company": {"name": "company", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "industry": {"name": "industry", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "lead_source": {"name": "lead_source", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "number_of_employees": {"name": "number_of_employees", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "details": {"name": "details", "type": "json", "primaryKey": false, "notNull": false}, "history": {"name": "history", "type": "json", "primaryKey": false, "notNull": false}, "company_info": {"name": "company_info", "type": "json", "primaryKey": false, "notNull": false}, "communications": {"name": "communications", "type": "json", "primaryKey": false, "notNull": false}, "last_modified_date": {"name": "last_modified_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.operations": {"name": "operations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "details": {"name": "details", "type": "text", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"operations_uuid_unique": {"name": "operations_uuid_unique", "nullsNotDistinct": false, "columns": ["uuid"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.prompt_tests": {"name": "prompt_tests", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true}, "result": {"name": "result", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.prompts": {"name": "prompts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'system'"}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "version": {"name": "version", "type": "serial", "primaryKey": false, "notNull": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "company": {"name": "company", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}